// vite-plugin-yaml-to-json.js
import fs from 'fs';
import path from 'path';
import yaml from 'js-yaml';

// === 工具类：文件模式匹配器 ===
class FilePatternMatcher {
  constructor(watchPattern) {
    const { baseDir, extensions } = this.parseWatchPattern(watchPattern);
    this.baseDir = baseDir;
    this.extensions = extensions;
  }

  parseWatchPattern(watchPattern) {
    const parts = watchPattern.split('/');
    const baseDir = parts[0] === '**' ? '.' : parts[0];
    const extensions = ['.yaml', '.yml'];
    return { baseDir, extensions };
  }

  matches(filePath) {
    const relativePath = path.relative(process.cwd(), filePath);

    if (this.baseDir !== '.' && !relativePath.startsWith(this.baseDir)) {
      return false;
    }

    return this.extensions.some(ext => filePath.endsWith(ext));
  }
}

// === 工具类：依赖关系管理器 ===
class DependencyManager {
  constructor() {
    this.dependencyMap = new Map();
  }

  addDependency(parentFile, childFile) {
    if (!this.dependencyMap.has(childFile)) {
      this.dependencyMap.set(childFile, new Set());
    }
    this.dependencyMap.get(childFile).add(parentFile);
    console.log(`[yaml-to-json] Dependency added: ${path.relative(process.cwd(), childFile)} -> ${path.relative(process.cwd(), parentFile)}`);
  }

  getDependents(filePath) {
    return this.dependencyMap.get(filePath) || new Set();
  }

  clear() {
    this.dependencyMap.clear();
  }

  get size() {
    return this.dependencyMap.size;
  }

  entries() {
    return this.dependencyMap.entries();
  }
}

// === 工具类：文件扫描器 ===
class FileScanner {
  constructor(patternMatcher) {
    this.patternMatcher = patternMatcher;
  }

  findYamlFiles(dir, yamlFiles = []) {
    try {
      const items = fs.readdirSync(dir, { withFileTypes: true });

      for (const item of items) {
        const fullPath = path.join(dir, item.name);

        if (item.isDirectory()) {
          if (!['node_modules', '.git', 'dist', 'build'].includes(item.name)) {
            this.findYamlFiles(fullPath, yamlFiles);
          }
        } else if (item.isFile() && this.patternMatcher.matches(fullPath)) {
          yamlFiles.push(fullPath);
        }
      }
    } catch (error) {
      console.warn(`[yaml-to-json] Cannot read directory ${dir}:`, error.message);
    }

    return yamlFiles;
  }

  scanDependencies(filePath, dependencyManager, rootFilePath = filePath, visited = new Set()) {
    if (visited.has(filePath)) {
      console.warn(`[yaml-to-json] Circular dependency detected: ${filePath}`);
      return;
    }
    visited.add(filePath);

    try {
      if (!fs.existsSync(filePath)) {
        return;
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const includePattern = /!include\s+(['"]?)([^'"\s]+)\1/g;
      let match;

      while ((match = includePattern.exec(content)) !== null) {
        const includePath = match[2];
        const fullPath = path.resolve(path.dirname(filePath), includePath);

        if (rootFilePath !== fullPath) {
          dependencyManager.addDependency(rootFilePath, fullPath);
          this.scanDependencies(fullPath, dependencyManager, rootFilePath, new Set(visited));
        }
      }
    } catch (error) {
      console.error(`[yaml-to-json] Error scanning dependencies in ${filePath}:`, error);
    }
  }
}

// === 工具类：YAML处理器 ===
class YamlProcessor {
  constructor(transform) {
    this.transform = transform;
  }

  processFile(filePath, dependencyManager, isInitial = false) {
    try {
      console.log(`[yaml-to-json] ${isInitial ? 'Initial processing' : 'File changed'}: ${path.relative(process.cwd(), filePath)}`);

      const result = loadYaml(filePath, filePath, dependencyManager.addDependency.bind(dependencyManager));
      const finalResult = this.transform ? this.transform(result, filePath) : result;

      const jsonPath = filePath.replace(/\.ya?ml$/, '.json');
      fs.writeFileSync(jsonPath, JSON.stringify(finalResult, null, 2), 'utf8');
      console.log(`[yaml-to-json] Generated: ${path.relative(process.cwd(), jsonPath)}`);

      return true;
    } catch (error) {
      console.error(`[yaml-to-json] Error processing ${filePath}:`, error);
      return false;
    }
  }
}

// === 工具类：依赖关系初始化器 ===
class DependencyInitializer {
  constructor(patternMatcher, fileScanner, dependencyManager) {
    this.patternMatcher = patternMatcher;
    this.fileScanner = fileScanner;
    this.dependencyManager = dependencyManager;
  }

  initialize(watchPattern) {
    try {
      console.log(`[yaml-to-json] Initializing dependency graph...`);
      console.log(`[yaml-to-json] Watch pattern: ${watchPattern}`);
      console.log(`[yaml-to-json] Base directory: ${this.patternMatcher.baseDir}`);

      const searchDir = this.patternMatcher.baseDir === '.'
        ? process.cwd()
        : path.join(process.cwd(), this.patternMatcher.baseDir);

      const yamlFiles = this.fileScanner.findYamlFiles(searchDir);
      console.log(`[yaml-to-json] Found ${yamlFiles.length} YAML files in watch pattern`);

      this.dependencyManager.clear();

      for (const filePath of yamlFiles) {
        this.fileScanner.scanDependencies(filePath, this.dependencyManager);
      }

      console.log(`[yaml-to-json] Dependency graph initialized. Total dependencies: ${this.dependencyManager.size}`);
      this.logDependencyGraph();
    } catch (error) {
      console.error(`[yaml-to-json] Error initializing dependencies:`, error);
    }
  }

  logDependencyGraph() {
    for (const [childFile, parentFiles] of this.dependencyManager.entries()) {
      const childName = path.relative(process.cwd(), childFile);
      const parentNames = Array.from(parentFiles).map(p => path.relative(process.cwd(), p));
      console.log(`[yaml-to-json] ${childName} ← [${parentNames.join(', ')}]`);
    }
  }
}

// === 工具类：文件变更处理器 ===
class FileChangeHandler {
  constructor(patternMatcher, dependencyManager, yamlProcessor, dependencyInitializer) {
    this.patternMatcher = patternMatcher;
    this.dependencyManager = dependencyManager;
    this.yamlProcessor = yamlProcessor;
    this.dependencyInitializer = dependencyInitializer;
    this.processedFiles = new Set();
  }

  handleFileChange(filePath, server) {
    if (!this.patternMatcher.matches(filePath)) {
      return;
    }

    try {
      const success = this.yamlProcessor.processFile(filePath, this.dependencyManager);

      if (success) {
        this.processDependendFiles(filePath);
        this.notifyClient(server, filePath);
      }
    } catch (error) {
      console.error(`[yaml-to-json] Error processing ${filePath}:`, error);
      this.sendErrorToClient(server, error);
    }
  }

  handleFileAdd(filePath, watchPattern) {
    if (!this.patternMatcher.matches(filePath) || this.processedFiles.has(filePath)) {
      return;
    }

    console.log(`[yaml-to-json] New YAML file detected: ${path.relative(process.cwd(), filePath)}`);

    this.dependencyInitializer.initialize(watchPattern);

    const success = this.yamlProcessor.processFile(filePath, this.dependencyManager, true);
    if (success) {
      this.processedFiles.add(filePath);
    }
  }

  handleFileDelete(filePath, watchPattern) {
    if (!this.patternMatcher.matches(filePath)) {
      return;
    }

    console.log(`[yaml-to-json] YAML file deleted: ${path.relative(process.cwd(), filePath)}`);

    this.dependencyInitializer.initialize(watchPattern);
    this.processedFiles.delete(filePath);
  }

  processDependendFiles(filePath) {
    const dependentFiles = this.dependencyManager.getDependents(filePath);

    for (const dependentFile of dependentFiles) {
      console.log(`[yaml-to-json] Triggering dependent file: ${path.relative(process.cwd(), dependentFile)}`);
      this.yamlProcessor.processFile(dependentFile, this.dependencyManager);
    }
  }

  notifyClient(server, filePath) {
    const jsonPath = filePath.replace(/\.ya?ml$/, '.json');
    server.ws.send({
      type: 'update',
      path: jsonPath,
      timestamp: Date.now()
    });

    const dependentFiles = this.dependencyManager.getDependents(filePath);
    for (const dependentFile of dependentFiles) {
      const dependentJsonPath = dependentFile.replace(/\.ya?ml$/, '.json');
      server.ws.send({
        type: 'update',
        path: dependentJsonPath,
        timestamp: Date.now()
      });
    }
  }

  sendErrorToClient(server, error) {
    server.ws.send({
      type: 'error',
      err: {
        message: `YAML processing error: ${error.message}`,
        stack: error.stack
      }
    });
  }
}

/**
 * Vite插件：将YAML文件热更新为JSON
 * @param {Object} options 插件配置选项
 * @param {string} options.watch 要监听的YAML文件匹配模式
 * @param {Function} options.transform 可选的自定义转换函数
 * @returns {import('vite').Plugin}
 */
export default function yamlToJsonPlugin(options = {}) {
  const { watch = '**/*.yaml', transform } = options;

  // 初始化工具类
  const patternMatcher = new FilePatternMatcher(watch);
  const dependencyManager = new DependencyManager();
  const fileScanner = new FileScanner(patternMatcher);
  const yamlProcessor = new YamlProcessor(transform);
  const dependencyInitializer = new DependencyInitializer(patternMatcher, fileScanner, dependencyManager);
  const fileChangeHandler = new FileChangeHandler(patternMatcher,
    dependencyManager, yamlProcessor, dependencyInitializer);

  return {
    name: 'vite-plugin-yaml-to-json',

    configureServer(server) {
      dependencyInitializer.initialize(watch);
      server.watcher.add(watch);

      server.watcher.on('change', (filePath) => {
        fileChangeHandler.handleFileChange(filePath, server);
      });

      server.watcher.on('add', (filePath) => {
        fileChangeHandler.handleFileAdd(filePath, watch);
      });

      server.watcher.on('unlink', (filePath) => {
        fileChangeHandler.handleFileDelete(filePath, watch);
      });
    }
  };
}

// --- YAML加载函数 ---
function loadYaml(filePath, rootFilePath, addDependency) {
  console.log(`Loading YAML file: ${filePath}`);
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    console.log(`File content length: ${content.length} bytes`);
    
    // 创建特定文件路径的 schema（闭包保存当前文件路径和依赖关系记录函数）
    const schema = createSchemaWithIncludes(filePath, rootFilePath, addDependency);
    
    // 使用特定文件的 schema 加载内容
    const result = yaml.load(content, { schema });
    
    return result;
  } catch (error) {
    console.error(`Error loading file ${filePath}:`, error);
    throw error;
  }
}

// --- 为每个文件创建一个新的 schema，以便在闭包中捕获文件路径 ---
function createSchemaWithIncludes(currentFilePath, rootFilePath, addDependency) {
  // 为当前文件定义特定的 !include 标签
  const IncludeYamlType = new yaml.Type('!include', {
    kind: 'scalar',
    resolve: function(data) {
      return typeof data === 'string';
    },
    construct: function(filepath) {
      console.log(`Processing include: ${filepath}`);
      try {
        // 使用闭包中的 currentFilePath，而不是 this.filePath
        const fullPath = path.resolve(path.dirname(currentFilePath), filepath);
        console.log(`Full path: ${fullPath}`);
        
        if (!fs.existsSync(fullPath)) {
          console.error(`File not found: ${fullPath}`);
          throw new Error(`File not found: ${fullPath}`);
        }
        
        // 记录依赖关系：rootFilePath 依赖 fullPath
        if (addDependency && rootFilePath !== fullPath) {
          addDependency(rootFilePath, fullPath);
        }
        
        // 递归加载和解析子文件
        return loadYaml(fullPath, rootFilePath, addDependency);
      } catch (error) {
        console.error(`Error processing include ${filepath}:`, error);
        throw error;
      }
    }
  });

  // 创建并返回特定于此文件的 schema
  return yaml.DEFAULT_SCHEMA.extend([IncludeYamlType]);
}
