#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 读取配置文件
function loadConfig() {
  const configPath = path.resolve(process.cwd(), 'filename.config.cjs');

  if (fs.existsSync(configPath)) {
    try {
      delete require.cache[require.resolve(configPath)];
      return require(configPath);
    } catch (error) {
      console.warn('⚠️  配置文件读取失败，使用默认配置:', error.message);
    }
  }

  // 默认配置
  return {
    filenameCase: 'kebab-case',
    directoryCase: 'kebab-case',
    checkExtensions: ['.vue', '.ts', '.js', '.tsx', '.jsx'],
    excludeDirs: ['node_modules', '.git', '.husky', 'dist', 'build'],
    excludeFiles: ['main.ts', 'app.vue', 'index.ts', 'index.js', 'vite.config.js', 'tailwind.config.js', 'postcss.config.js', 'babel.config.js']
  };
}

const CONFIG = loadConfig();

// 命名规范检查函数
const namingCheckers = {
  'kebab-case': (name) => {
    return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(name);
  },
  'PascalCase': (name) => /^[A-Z][a-zA-Z0-9]*$/.test(name),
  'camelCase': (name) => /^[a-z][a-zA-Z0-9]*$/.test(name)
};

// 获取文件名（不含扩展名）
function getBaseName(filename) {
  return path.parse(filename).name;
}

// 检查单个文件名
function checkFilename(filePath) {
  const errors = [];
  const relativePath = path.relative(process.cwd(), filePath);
  const pathParts = relativePath.split(path.sep);

  // 检查目录名
  for (let i = 0; i < pathParts.length - 1; i++) {
    const dirName = pathParts[i];

    // 跳过排除的目录
    if (CONFIG.excludeDirs.includes(dirName)) {
      continue;
    }

    const checker = namingCheckers[CONFIG.directoryCase];
    if (!checker(dirName)) {
      errors.push({
        type: 'directory',
        path: pathParts.slice(0, i + 1).join('/'),
        name: dirName,
        expected: CONFIG.directoryCase,
        message: `目录名 "${dirName}" 不符合 ${CONFIG.directoryCase} 命名规范`
      });
    }
  }

  // 检查文件名
  const filename = pathParts[pathParts.length - 1];
  const baseName = getBaseName(filename);
  const extension = path.extname(filename);

  // 跳过排除的文件
  if (CONFIG.excludeFiles.includes(filename)) {
    return errors;
  }

  // 只检查指定扩展名的文件
  if (CONFIG.checkExtensions.length > 0 && !CONFIG.checkExtensions.includes(extension)) {
    return errors;
  }

  const checker = namingCheckers[CONFIG.filenameCase];
  if (!checker(baseName)) {
    errors.push({
      type: 'file',
      path: relativePath,
      name: baseName,
      expected: CONFIG.filenameCase,
      message: `文件名 "${baseName}" 不符合 ${CONFIG.filenameCase} 命名规范`
    });
  }

  return errors;
}

// 检查传入的文件列表
function checkFiles(files) {
  const allErrors = [];

  for (const file of files) {
    // 检查文件名规范，不管文件是否真实存在
    // 这对于 pre-commit 检查很有用
    const errors = checkFilename(file);
    allErrors.push(...errors);
  }

  return allErrors;
}

// 主函数
function main() {
  const files = process.argv.slice(2);

  if (files.length === 0) {
    console.log('没有文件需要检查');
    return;
  }

  console.log(`🔍 检查文件名规范 (文件: ${CONFIG.filenameCase}, 目录: ${CONFIG.directoryCase})`);

  const errors = checkFiles(files);

  if (errors.length === 0) {
    console.log('✅ 所有文件名都符合命名规范');
    process.exit(0);
  } else {
    console.log(`❌ 发现 ${errors.length} 个命名规范问题:\n`);

    errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.message}`);
      console.log(`   路径: ${error.path}`);
      console.log(`   期望格式: ${error.expected}\n`);
    });

    console.log('请修正上述文件名后重新提交');
    process.exit(1);
  }
}

// 如果作为脚本直接运行
if (require.main === module) {
  main();
}

module.exports = {
  checkFiles,
  checkFilename,
  CONFIG
};