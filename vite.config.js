import { resolve } from 'path';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
import { visualizer } from 'rollup-plugin-visualizer';
import { createHtmlPlugin } from 'vite-plugin-html';
// import eslintPlugin from 'vite-plugin-eslint';
// import { viteExternalsPlugin } from 'vite-plugin-externals';
import svgPlugin from 'unplugin-svg-vue-component/vite';
import { viteMockServe } from 'vite-plugin-mock';
import yamlToJsonPlugin from './scripts/vite-plugin-yaml-to-json.js';

// 强制启用mock插件，确保开发时能够工作
const mockPlugins = [
  viteMockServe({
    mockPath: 'mock',
    enable: true,
    watchFiles: true,
    logger: true,
  }),
];

const base = process.env.NODE_ENV === 'development' 
? '/' 
: process.env.CDN_URL ;


console.log(`base path:${base}`);
export default defineConfig({
  // 在开发环境下移除base路径，让mock正常工作
  base: base,
  plugins: [
    createHtmlPlugin(
      {
        inject:{
          data:{
            isDev:process.env.NODE_ENV === 'development',
          }
        }
      }
    ),
    vueJsx(),
    vue({
      jsx: true,
    }),
    svgPlugin(),
    // viteExternalsPlugin({
    //   vue: 'Vue',
    // }),
    visualizer(),
    // eslintPlugin(),
    ...mockPlugins,
    // YAML到JSON转换插件，实时监听YAML文件变化并生成对应的JSON文件
    yamlToJsonPlugin({
      // 监听src目录下的所有YAML文件
      watch: 'src/**/*.{yaml,yml}',
      // 可选：自定义转换逻辑
      transform: (data, filePath) => {
        // 在这里可以对数据进行额外处理，比如添加元信息
        return data;
      }
    }),
  ],
  server: {
    port: 8080,
  },
  input: {
    index: resolve(__dirname, 'index.html'),
  },
  resolve: {
    alias: [
      {
        find: '@',
        replacement: resolve(__dirname, 'src'),
      },
    ],
    extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue', '.css'],
  },
  build: {
    minify: false, // 关闭代码压缩，便于线上调试
    sourcemap: true, // 生成source map文件，便于调试时查看源代码
    rollupOptions: {
      input: {
        index: resolve(__dirname, 'index.html'),
      },
    },
    chunkSizeWarningLimit: 700, // Default is 500
    outDir: 'build',
    // 确保构建过程中处理YAML文件
    assetsInclude: ['**/*.yaml', '**/*.yml'],
  },
});
