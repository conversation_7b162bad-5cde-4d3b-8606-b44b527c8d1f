## XPage项目开发规范

### 目录约定

```
src
├── main.ts                // 入口文件
├── app.vue                // vue主应用
├── assets                 // 存放资源
│   └── fonts
│   └── images
│   └── styles
├── components             // 组件
├── helpers                // 工具函数 | 常量等
│   └── constants          // 常量定义
│   └── xxx
├── models                 // 业务逻辑，面向对象
│   ├── app-model.ts       // 全局model，勿删，勿改名
│   ├── common-models      // 页面之间共用的model，不对外直接提供，由app-model或page-model调用
│   └── page-models        // 页面model，和页面名一一对应
├── pages                  // 页面
│   └── counter.vue
├── router                 // 路由
│   └── index.ts
└── services               // API调用
    └── request.ts         // 基础request, 根据需求修改
```

### SDK

@tencent/xpage-web-sdk已内置 请求库 | 一些工具函数 | lodash-es |
dayjs，[查看文档](https://tfr.woa.com/td/xpage-doc/prod/pro/sdk.html)，如有其他npm包需要使
用，请联系管理员

使用方式

```ts
import { dayjs, request } from '@tencent/xpage-web-sdk';
```

### 开发约定

1. 文件名统一小写中划线

2. 路径别名：暂时只支持 '@' 执行 'src'

3. svg使用：

   ```
   (1) 存放在 assets/images 目录下
   (2) 像使用普通组件一样
   ```
# test