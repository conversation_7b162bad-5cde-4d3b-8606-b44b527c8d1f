import { UserPersonaType } from '@/helpers/constants/model';
import { computCountByPercentOfPersona } from '@/helpers/insight-helper';
import { PIE_CHART_COLOR, PIE_CHART_COLOR_DEFAULT } from '@/helpers/types/insight';
import { UserPersonaDetailCategory } from '@/helpers/types/models';
import { AoErrorCode, AoErrorCodeMap } from '@/helpers/types/models/error-code';
import type { PieChartData, BarChartItem, TableData } from '@/helpers/types/portrait';
import Logger from '@/helpers/utils/logger';
import { RequestErrorInfo } from '@tencent/xpage-web-sdk';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';
import { LocationQuery } from 'vue-router';

const logger = new Logger('BasePersonaModel');

export default class BasePersonaModel {
  // ========= 页面状态 =========

  /** 加载中 */
  pageInited = false;
  /** 面包屑 */
  breadcrumbData = [
    {
      label: '用户洞察',
      href: '/home/<USER>',
    },
    {
      label: '画像详情',
    },
  ];
  readonly outlineInfo = {
    ageStatistic: {
      label: '年龄分布',
      id: 'age-statistic-info',
    },
    genderStatistic: {
      label: '性别分布',
      id: 'cgender-statistic-info',
    },
    geoStatistic: {
      label: '地域分布',
      id: 'geo-statistic-info',
    },
    dailyConsume: {
      label: '日均消费',
      id: 'daily-consume-info',
    },
    consumerPreferences: {
      label: '消费偏好',
      id: 'consumer-preferences-info',
    },
    consumerTimePreferences: {
      label: '消费时间段偏好',
      id: 'consumer-time-preferences-info',
    },
    socialPreferences: {
      label: '社交偏好',
      id: 'social-preferences-info',
    },
  };
  /** 大纲 */
  outlineData = Object.values(this.outlineInfo);
  /** 性别表格 */
  genderColumns = [
    { colKey: 'content', title: '性别' },
    { colKey: 'number', title: '人数' },
    { colKey: 'percent', title: '占比' },
  ];

  /** 是否显示图表中的具体数值 */
  showDetailNum = ref(true);

  // ========= 从后台获取到的数据 =========

  /** 原始画像详情数据 */
  personaDetailCategory = ref<UserPersonaDetailCategory[]>([]);

  // ========= 对后台接口处理后的数据 =========

  /** 画像详情数据 - 年龄 */
  personaDetailOfAge = ref<BarChartItem[]>([]);
  /** 画像详情数据 - 性别 图 */
  personaDetailOfGenderChart = ref<PieChartData>([]);
  /** 画像详情数据 - 性别 表 */
  personaDetailOfGenderTable = ref<TableData[]>([]);
  /** 画像详情数据 - 省份 */
  personaDetailOfProvince = ref<TableData[]>([]);
  /** 画像详情数据 - 城市 */
  personaDetailOfCity = ref<TableData[]>([]);
  /** 画像详情数据 - 日均消费 */
  personaDetailOfDayAverage = ref<BarChartItem[]>([]);
  /** 画像详情数据 - 消费偏好 */
  personaDetailOfConsumePrefer = ref<BarChartItem[]>([]);
  /** 画像详情数据 - 消费时间段偏好 */
  personaDetailOfConsumeTimePrefer = ref<BarChartItem[]>([]);
  /** 画像详情数据 - 社交偏好 */
  personaDetailOfSocialPrefer = ref<BarChartItem[]>([]);

  /**
   * 初始化页面数据
   */
  async initPageModel(query: LocationQuery) {
    logger.log('initPageModel invoke');
    try {
      this.initPageParams(query);
      await this.initPersonaDetail();
    } catch (e) {
      logger.error('initPageModel error', e);

      this.pageInited = true;
      return;
    }

    this.pageInited = true;
  }

  /**
   * 初始化页面参数
   * 需要被子类实现
   * @param query useRoute().query
   */
  initPageParams(query: LocationQuery) {
    logger.log('initPageParams invoke', query);
    throw new Error('initPageParams need to be implemented by subclass');
  }

  /**
   * 初始化页面
   */
  async initPersonaDetail() {
    logger.log('initPersonaDetail invoke');

    try {
      this.personaDetailCategory.value = await this.getPersonaDetailFromServer();
    } catch (e) {
      logger.error('initPersonaDetail error', e);

      // 数据为空：提示
      if (
        (e as RequestErrorInfo)?.code === AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY ||
        (e as RequestErrorInfo)?.code === AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY
      ) {
        MessagePlugin.info(AoErrorCodeMap[(e as RequestErrorInfo).code! as AoErrorCode].title);
        throw new Error('获取活动概况数据为空');
      }

      // 其他情况：报错
      MessagePlugin.error('获取用户画像详情失败');
      throw new Error('获取用户画像详情失败');
    }

    this.formatPerosnaDetailOfAge();
    this.formatPersonaDetailOfGender();
    this.formatPersonaDetailOfProvince();
    this.formatPersonaDetailOfCity();
    this.formatPersonaDetailOfDayAverage();
    this.formatPersonaDetailOfConsumePrefer();
    this.formatPersonaDetailOfConsumeTimePrefer();
    this.formatPersonaDetailOfSocialPrefer();
  }

  /**
   * 从服务器获取用户画像详情
   * 需要被子类实现
   */
  async getPersonaDetailFromServer(): Promise<UserPersonaDetailCategory[]> {
    throw new Error('initPersonaDetail need to be implemented by subclass');
  }

  /**
   * 格式化 年龄数据
   */
  formatPerosnaDetailOfAge() {
    this.personaDetailOfAge.value = this.baseFormatPersonDetailOfBarChartData(UserPersonaType.USER_PERSONA_AGE);
  }

  /**
   * 格式化 性别数据
   */
  formatPersonaDetailOfGender() {
    const genderRecord = this.personaDetailCategory.value.find(
      (x) => x.user_persona_type === UserPersonaType.USER_PERSONA_SEX,
    );

    if (!Array.isArray(genderRecord?.user_persona_detail_content_list)) {
      logger.error('formatPersonaDetailOfGender genderRecord is empty');
      this.personaDetailOfGenderTable.value = [];
      return;
    }

    const genderList = genderRecord.user_persona_detail_content_list.sort((x, y) => {
      // 0:未知 排到最后
      if (x.item_type === 0) {
        return 1;
      }
      if (y.item_type === 0) {
        return -1;
      }
      return x.rank - y.rank;
    });
    this.personaDetailOfGenderChart.value = genderList.map((x, i) => ({
      name: x.label,
      value: this.getItemNum(x.number || 0, x.percent),
      customPercent: Number(x.percent.replace(/%|-/, '')),
      color: PIE_CHART_COLOR[i] || PIE_CHART_COLOR_DEFAULT,
    }));

    this.personaDetailOfGenderTable.value = genderList.map((x) => ({
      value: x.item_type,
      content: x.label,
      number: x.number || 0,
      percent: x.percent,
      rank: x.rank || '',
    }));
  }

  /**
   * 格式化 省份数据
   */
  formatPersonaDetailOfProvince() {
    const provinceRecord = this.personaDetailCategory.value.find(
      (x) => x.user_persona_type === UserPersonaType.USER_PERSONA_REGION_PROVINCE,
    );

    if (!Array.isArray(provinceRecord?.user_persona_detail_content_list)) {
      logger.error('formatPersonaDetailOfProvince provinceRecord is empty');
      this.personaDetailOfProvince.value = [];
      return;
    }

    const provinceList = provinceRecord.user_persona_detail_content_list.sort((x, y) => x.rank - y.rank);

    this.personaDetailOfProvince.value = provinceList.map((x) => ({
      value: x.item_type,
      content: x.label,
      // 不显示具体数值的情况下，把percent当作数值
      number: this.getItemNum(x.number, x.percent),
      percent: x.percent,
      rank: x.rank || '',
      customPercent: Number(x.percent.replace(/%|-/, '')),
    }));
  }

  /**
   * 格式化 城市数据
   */
  formatPersonaDetailOfCity() {
    const cityRecord = this.personaDetailCategory.value.find(
      (x) => x.user_persona_type === UserPersonaType.USER_PERSONA_REGION_CITY,
    );

    if (!Array.isArray(cityRecord?.user_persona_detail_content_list)) {
      logger.error('formatPersonaDetailOfCity cityRecord is empty');
      this.personaDetailOfCity.value = [];
      return;
    }

    const cityList = cityRecord.user_persona_detail_content_list.sort((x, y) => x.rank - y.rank);

    this.personaDetailOfCity.value = cityList.map((x) => ({
      value: x.item_type,
      content: x.label,
      // 不显示具体数值的情况下，把percent当作数值
      number: this.getItemNum(x.number, x.percent),
      percent: x.percent,
      rank: x.rank || '',
      customPercent: Number(x.percent.replace(/%|-/, '')),
    }));
  }
  /**
   * 格式化 日均消费 - 柱状图数据
   */
  async formatPersonaDetailOfDayAverage() {
    this.personaDetailOfDayAverage.value = this.baseFormatPersonDetailOfBarChartData(
      UserPersonaType.USER_PERSONA_DAY_AVERAGE,
    );
  }
  /**
   * 格式化 消费偏好 - 柱状图数据
   */
  async formatPersonaDetailOfConsumePrefer() {
    this.personaDetailOfConsumePrefer.value = this.baseFormatPersonDetailOfBarChartData(
      UserPersonaType.USER_PERSONA_CONSUME_PREFER,
    );
  }
  /**
   * 格式化 消费时间段偏好 - 柱状图数据
   */
  async formatPersonaDetailOfConsumeTimePrefer() {
    this.personaDetailOfConsumeTimePrefer.value = this.baseFormatPersonDetailOfBarChartData(
      UserPersonaType.USER_PERSONA_CONSUME_TIME_PREFER,
    );
  }

  /**
   * 格式化 社交偏好 - 柱状图数据
   */
  async formatPersonaDetailOfSocialPrefer() {
    this.personaDetailOfSocialPrefer.value = this.baseFormatPersonDetailOfBarChartData(
      UserPersonaType.USER_PERSONA_SOCIAL_PREFER,
    );
  }

  /**
   * 获取柱状图数据
   */
  baseFormatPersonDetailOfBarChartData(userPersonaType: UserPersonaType): BarChartItem[] {
    const record = this.personaDetailCategory.value.find((x) => x.user_persona_type === userPersonaType);
    if (!Array.isArray(record?.user_persona_detail_content_list)) {
      logger.error('baseFormatPersonDetailOfBarChartData records is empty', userPersonaType);
      return [];
    }

    const majorColor = '#07C160';
    const minorColor = '#E5E5E5';
    const result = record.user_persona_detail_content_list
      .sort((x, y) => {
        // 0:未知 排到最后
        if (x.item_type === 0) {
          return 1;
        }
        if (y.item_type === 0) {
          return -1;
        }
        return x.item_type - y.item_type;
      })
      .map((item) => ({
        name: item.label,
        value:
          this.showDetailNum.value && typeof item.number === 'undefined'
            ? computCountByPercentOfPersona(record.user_persona_detail_content_list, item.percent)
            : this.getItemNum(item.number, item.percent), // 后台有时候居然不返回number，只返回percent，无奈根据percent推算下number
        color: item.item_type === 0 ? minorColor : majorColor,
        customPercent: Number(item.percent.replace(/%|-/, '')),
      }));

    return result;
  }

  /**
   * 获取具体数值
   * 不显示具体数值的情况下，把percent当作数值来计算图表
   */
  getItemNum(num: number | undefined, percent: string): number {
    if (this.showDetailNum.value) {
      return num || 0;
    }

    return Number(percent.replace(/%|-/, ''));
  }
}
