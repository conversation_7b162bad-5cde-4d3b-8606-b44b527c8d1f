import {
  ActiveCompassRulePeriod,
  TV1GetCompassActivityPersonaDetailResponseParams,
  UserPersonaDetailCategory,
} from '@/helpers/types/models';
import Logger from '@/helpers/utils/logger';
import BasePersonaModel from '../common-models/base-persona-model';
import { compassActivityService } from '@/services/compass-activity-service';
import { ActivityType } from '@/helpers/constants/model';
import {
  getDefaultEarliestDate,
  getDefaultEndDate,
  getDefaultEndDateNext,
  getDefaultStartDate,
  getRealActiveDateBeginAndTips,
  MAX_DATE_RANGE_DAYS,
} from '@/helpers/utils/date';
import { LocationQuery } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { dayjs } from '@tencent/xpage-web-sdk';
import { computed, ref } from 'vue';

const logger = new Logger('shake-activity-persona-model');

export default class ShakeActivityPersonaModel extends BasePersonaModel {
  private static instance: ShakeActivityPersonaModel | null = null;

  static getInstance() {
    if (!ShakeActivityPersonaModel.instance) {
      ShakeActivityPersonaModel.instance = new ShakeActivityPersonaModel();
    }
    return ShakeActivityPersonaModel.instance;
  }

  static destroyInstance() {
    ShakeActivityPersonaModel.instance = null;
  }
  /** 是否显示图表中的具体数值 */
  showDetailNum = ref(false);

  /** 生效中的品牌版本号 */
  activeBrandVersion = ref(0);

  /** 面包屑 */
  breadcrumbData = [
    {
      label: '摇一摇有优惠',
      href: '/home/<USER>',
    },
    {
      label: '画像详情',
    },
  ];

  /** 性别表格 */
  genderColumns = [
    { colKey: 'content', title: '性别' },
    { colKey: 'percent', title: '占比' },
  ];

  // ========= 页面打开时传入的参数 =========
  dateFilter = ref({
    /** after的下一天，用于当作dateFilter的基准日期 */
    baseDate: getDefaultEndDateNext(),
    /** 查询的开始日期 */
    getDateBegin: getDefaultStartDate(),
    /** 查询的结束日期 */
    getDateEnd: getDefaultEndDate(),
  });

  /** 报告生效时间段 */
  activeCompassRulePeriod = ref<ActiveCompassRulePeriod>({
    active_begin_time: Number(getDefaultEarliestDate('YYYYMMDD')),
    active_end_time: Number(getDefaultEndDate('YYYYMMDD')),
  });
  /** 生效中的罗盘规则 - 格式化后的数据 */
  activeCompassRulePeriodFormated = ref({
    before: getDefaultEarliestDate(),
    after: getDefaultEndDate(),
  });
  dateFilterTips = computed(
    () =>
      getRealActiveDateBeginAndTips(
        this.activeCompassRulePeriod.value,
        this.dateFilter.value.getDateBegin
      ).tips
  );

  /**
   * 初始化页面参数
   * @param query useRoute().query
   * query.activeBrandVersion {require} 版本号
   * query.baseDate {optional} 基准日期，该天对应的数据报表没出；不传的话默认为当天
   * query.disableDateBefore {optional} 可以选择的最早的结束时间；不传的话默认为很早之前的一天
   * query.disableDateAfter {optional} 可以选择的最晚的结束时间；不传的话默认为当天
   */
  initPageParams(query: LocationQuery) {
    logger.log('initPageParams invoke', query);
    if (!Number.isInteger(Number(query.activeBrandVersion))) {
      MessagePlugin.error('参数错误');
      throw new Error('activeBrandVersion is required');
    }
    this.activeBrandVersion.value = Number(query.activeBrandVersion);

    if (typeof query.baseDate === 'string') {
      this.dateFilter.value.baseDate = query.baseDate;
    }
    if (typeof query.disableDateBefore === 'string') {
      this.activeCompassRulePeriodFormated.value.before = query.disableDateBefore;
    }
    if (typeof query.disableDateAfter === 'string') {
      this.activeCompassRulePeriodFormated.value.after = query.disableDateAfter;
    }

    // 查询开始时间：基准时间往前推7天（默认查一周）
    this.dateFilter.value.getDateBegin = dayjs(this.dateFilter.value.baseDate)
      .subtract(7, 'day')
      .format('YYYY-MM-DD');
    // 查询结束时间：基准时间往前推1天（基准时间开始报表没出）
    this.dateFilter.value.getDateEnd = dayjs(this.dateFilter.value.baseDate)
      .subtract(1, 'day')
      .format('YYYY-MM-DD');
    // 查询开始时间：基准时间往前推7天（默认查一周），包含当天，所以+1
    this.activeCompassRulePeriod.value.active_begin_time = Number(
      dayjs(this.activeCompassRulePeriodFormated.value.before)
        .add(1, 'day')
        .format('YYYYMMDD')
    );
    this.activeCompassRulePeriod.value.active_end_time = Number(
      dayjs(this.activeCompassRulePeriodFormated.value.after).format('YYYYMMDD')
    );
  }

  /**
   * 从服务器获取用户画像详情
   */
  async getPersonaDetailFromServer(): Promise<UserPersonaDetailCategory[]> {
    logger.log('getPersonaDetailFromServer invoke');

    let res: TV1GetCompassActivityPersonaDetailResponseParams['data'];
    try {
      res = await compassActivityService.getPersonaDetail({
        get_date_begin: this.dateFilter.value.getDateBegin,
        get_date_end: this.dateFilter.value.getDateEnd,
        activity_type: `${ActivityType.ACTIVITY_SHAKE}`,
        active_brand_version: String(this.activeBrandVersion.value),
      });
    } catch (e) {
      logger.error('getPersonaDetailFromServer error', e);
      throw e;
    }

    const personaDetailCategory =
      res?.activity_persona_detail?.user_persona_detail_category_list;
    if (!Array.isArray(personaDetailCategory)) {
      throw new Error('user_persona_detail_category_list is not an array');
    }

    return personaDetailCategory!;
  }

  /**
   * 修改周期结束日期
   */
  async updateActivityOverview(getDateBegin: string, getDateEnd: string) {
    logger.log('updateActivityOverview', getDateBegin, getDateEnd);
    if (!this.pageInited) {
      logger.log('updateActivityOverview', 'page not inited');
      return;
    }

    if (dayjs(getDateEnd).diff(dayjs(getDateBegin), 'day') > MAX_DATE_RANGE_DAYS) {
      logger.error(
        'updateActivityOverviewTrade',
        '日期范围超过限制',
        getDateBegin,
        getDateEnd
      );
      MessagePlugin.error(`日期范围不能超过${MAX_DATE_RANGE_DAYS}天`);
      return;
    }

    this.dateFilter.value.getDateBegin = getDateBegin;
    this.dateFilter.value.getDateEnd = getDateEnd;

    this.initPersonaDetail();
  }
}
