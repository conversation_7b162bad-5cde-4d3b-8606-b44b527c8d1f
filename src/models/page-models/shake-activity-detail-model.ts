import { computed, ref } from 'vue';
import {
  getActivityOverviewOfTrend,
  getActivityOverviewOfStatistics,
  switchOverviewCheckedState,
} from '@/helpers/insight-helper';
import {
  StatisticDataTrendInfo,
  type StatisticDataCardInfo,
} from '@/helpers/types/insight';
import Logger from '@/helpers/utils/logger';
import {
  ActiveCompassRulePeriod,
  ActivityOverview,
  BrandManagerActivity,
  TV1GetCompassActivityDetailOverviewResponseParams,
} from '@/helpers/types/models';
import { BrandManagerActivityExtend, activityService } from '@/services/activity-service';
import { MessagePlugin } from 'tdesign-vue-next';
import { compassActivityService } from '@/services/compass-activity-service';
import {
  ActivityOverviewType,
  ActivityOverviewTypeGroup,
  ActivityOverviewTypeGroupMap,
  ActivityOverviewTypeMap,
  ActivityType,
} from '@/helpers/constants/model';
import { DatePickerType } from '@/helpers/constants/common';
import {
  getDefaultEarliestDate,
  getDefaultEndDate,
  getDefaultEndDateNext,
  getDefaultStartDate,
  getRealActiveDateBeginAndTips,
  MAX_DATE_RANGE_DAYS,
} from '@/helpers/utils/date';
import { dayjs, RequestErrorInfo } from '@tencent/xpage-web-sdk';
import { AoErrorCode, AoErrorCodeMap } from '@/helpers/types/models/error-code';

const logger = new Logger('shake-activity-detail-model');

export default class ShakeActivityDetailModel {
  private static instance: ShakeActivityDetailModel | null = null;

  static getInstance() {
    if (!ShakeActivityDetailModel.instance) {
      ShakeActivityDetailModel.instance = new ShakeActivityDetailModel();
    }
    return ShakeActivityDetailModel.instance;
  }

  static destroyInstance() {
    ShakeActivityDetailModel.instance = null;
  }

  // ========== 页面状态 ==========
  /** 页面是否初始化完成 */
  pageInited = false;
  /** 顶部面包屑数据 */
  readonly breadcrumbData = [
    { label: '活动管理', to: { name: 'shakeActivityOverview' } },
    { label: '活动详情' },
  ];
  /** 侧边栏大纲信息 */
  readonly outlineInfo = {
    activitieData: {
      label: '活动信息',
      id: 'activitie-data-info',
    },
    saleData: {
      label: '活动交易数据',
      id: 'sale-data-info',
    },
  };
  /** 侧边栏大纲列表数据 */
  readonly outlineData = Object.values(this.outlineInfo);

  /** 快捷查询日期类型 */
  dateFilter = ref({
    dateType: DatePickerType.Week,
    /** after的下一天，用于当作dateFilter的基准日期 */
    baseDate: getDefaultEndDateNext(),
    /** 查询的开始日期 */
    getDateBegin: getDefaultStartDate(),
    /** 查询的结束日期 */
    getDateEnd: getDefaultEndDate(),
  });
  /** 活动 id */
  activityId = ref('');

  // ========= 后台返回的数据 =========
  /** 后台返回的原始活动介绍 */
  activityInfo = ref<BrandManagerActivityExtend | null>(null);
  /** 后台返回的原始活动概览数据 */
  activityOverview = ref<ActivityOverview>({
    activity_overview_statistics_list: [],
    activity_overview_trend_list: [],
  });
  /** 活动概览数据 - 统计值部分 */
  activityOverviewOfStatistics = ref<StatisticDataCardInfo[]>([]);
  /** 活动概览数据 - 统计值部分按类型分组 */
  activityOverviewOfStatisticsGrouped = computed<
    { title: string; statistic: StatisticDataCardInfo[] }[]
  >(() => {
    const getGroupData = (
      groupId: ActivityOverviewTypeGroup
    ): { title: string; statistic: StatisticDataCardInfo[] } => {
      const groupInfo = ActivityOverviewTypeGroupMap[groupId];
      return {
        title: groupInfo.title,
        statistic: this.activityOverviewOfStatistics.value.filter(
          (item) =>
            ActivityOverviewTypeMap[item.overviewType as ActivityOverviewType].group ===
            groupId
        ),
      };
    };
    return [
      getGroupData(ActivityOverviewTypeGroup.GROUP_SALE),
      getGroupData(ActivityOverviewTypeGroup.GROUP_CONVERT),
    ];
  });
  /** 活动概览数据 - 图表部分 */
  activityOverviewOfTrend = computed<StatisticDataTrendInfo[]>(() =>
    // 根据选择的统计数据、原始数据，获取数据趋势
    getActivityOverviewOfTrend(
      this.activityOverviewOfStatistics.value,
      this.activityOverview.value
    )
  );
  /** 报告生效时间段 */
  activeCompassRulePeriod = ref<ActiveCompassRulePeriod>({
    active_begin_time: 0,
    active_end_time: 0,
  });
  /** 报告生效时间段 - 格式化后的数据是否已经初始化 */
  activeCompassRulePeriodInited = false;
  /** 生效中的罗盘规则 - 格式化后的数据 */
  activeCompassRulePeriodFormated = ref({
    // 禁用before之前的日期，包含before
    before: getDefaultEarliestDate(),
    // 禁用after之后的日期，不包含after，与tdesign的逻辑一致
    after: getDefaultEndDate(),
  });
  dateFilterTips = computed(
    () =>
      getRealActiveDateBeginAndTips(
        this.activeCompassRulePeriod.value,
        this.dateFilter.value.getDateBegin
      ).tips
  );

  // ========= 方法 =========

  /**
   * 页面初始化
   */
  async initPageData(activityId: string) {
    this.activityId.value = activityId;

    try {
      await Promise.all([this.getActivityInfo(), this.getActivityOverview()]);
    } catch (e) {
      logger.error('initPageData error', e);
      this.pageInited = true;
      return;
    }

    this.pageInited = true;
  }

  /**
   * 获取活动介绍
   */
  async getActivityInfo() {
    let res: BrandManagerActivity;
    try {
      res = await activityService.getActivityDetail(Number(this.activityId.value));
    } catch (e) {
      logger.warn('getActivityInfo fail', e);
      MessagePlugin.error('获取活动详情失败');
      return;
    }

    this.activityInfo.value = activityService.formatActivityItem(res!);
  }

  /**
   * 获取活动概览
   */
  async getActivityOverview() {
    let res: TV1GetCompassActivityDetailOverviewResponseParams['data'];
    try {
      res = await compassActivityService.getCompassActivityDetailOverview(
        this.activityId.value,
        getRealActiveDateBeginAndTips(
          this.activeCompassRulePeriod.value,
          this.dateFilter.value.getDateBegin
        ).dateBegin,
        this.dateFilter.value.getDateEnd,
        ActivityType.ACTIVITY_SHAKE
      );
    } catch (e) {
      logger.error('getActivityOverview', '获取活动概况数据失败', e);

      // 数据为空：提示
      if (
        (e as RequestErrorInfo)?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY
      ) {
        MessagePlugin.info(
          AoErrorCodeMap[
            AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY
          ].title
        );
        throw new Error('获取活动概况数据为空');
      }

      // 其他情况：报错
      MessagePlugin.error('获取活动概况数据失败');
      throw new Error('获取活动概况数据失败');
    }

    this.activityOverview.value = res!.activity_detail_overview!;
    this.activeCompassRulePeriod.value = res!.active_compass_rule_period!;

    this.activityOverviewOfStatistics.value = getActivityOverviewOfStatistics(
      res!.activity_detail_overview!
    );
    this.formatActiveCompassRulePeriod();
  }

  /**
   * 格式化报告生效时间段
   */
  formatActiveCompassRulePeriod() {
    // 只有第一次返回的数据才是准确的，所以只初始化一次
    if (this.activeCompassRulePeriodInited) {
      return;
    }

    this.activeCompassRulePeriodInited = true;
    this.activeCompassRulePeriodFormated.value = {
      // before 回禁用before对应的那天，所以需要往前算一天
      before: dayjs(`${this.activeCompassRulePeriod.value.active_begin_time}`, 'YYYYMMDD')
        .subtract(1, 'day')
        .format('YYYY-MM-DD'),
      // after 不包含after对应的那天，所以不需要改动
      after: dayjs(
        `${this.activeCompassRulePeriod.value.active_end_time}`,
        'YYYYMMDD'
      ).format('YYYY-MM-DD'),
    };
    this.dateFilter.value.baseDate = dayjs(
      `${this.activeCompassRulePeriod.value.active_end_time}`,
      'YYYYMMDD'
    )
      .add(1, 'day')
      .format('YYYY-MM-DD');
  }

  /**
   * 更新活动概览的日期范围
   */
  async updateActivityOverview(
    type: DatePickerType,
    getDateBegin: string,
    getDateEnd: string
  ) {
    logger.log('updateActivityOverview', type, getDateBegin, getDateEnd);
    if (!this.pageInited) {
      logger.log('getActivityOverview', 'page not inited');
      return;
    }

    if (dayjs(getDateEnd).diff(dayjs(getDateBegin), 'day') > MAX_DATE_RANGE_DAYS) {
      logger.error('getActivityOverview', '日期范围超过限制', getDateBegin, getDateEnd);
      MessagePlugin.error(`日期范围不能超过${MAX_DATE_RANGE_DAYS}天`);
      return;
    }

    this.dateFilter.value.getDateBegin = getDateBegin;
    this.dateFilter.value.getDateEnd = getDateEnd;
    return this.getActivityOverview();
  }

  /**
   * 选择要显示图标的卡片
   */
  handleDataCardCheckChange = (
    item: StatisticDataCardInfo,
    checked: boolean
  ): {
    success: boolean;
    message: string;
  } => {
    logger.log('handleDataCardCheckChange', item, checked);

    const res = switchOverviewCheckedState(
      this.activityOverviewOfStatistics.value,
      checked,
      item
    );
    if (res.success) {
      this.activityOverviewOfStatistics.value = res.statistics;
    }

    return {
      success: res.success,
      message: res.message,
    };
  };
}
