import { computed, ref } from 'vue';
import type {
  StatisticDataCardInfo,
  SankeyDataCardInfo,
  StatisticDataTrendInfo,
} from '@/helpers/types/insight';
import {
  getUserInsightOfStatistics,
  getUserInsightOfTrend,
  switchOverviewCheckedState,
  TradeCycleDays,
} from '@/helpers/insight-helper';
import {
  ActiveCompassRule,
  ActiveCompassRulePeriod,
  CompassRule,
  TV1GetCompassUserInsightResponseParams,
  TV1GetCompassUserTrendResponseParams,
  UserFlow,
  UserPartition,
  UserPersonaSurvey,
} from '@/helpers/types/models';
import {
  DateFormat,
  DEFAULT_USER_INSIGHT_TRENT_DATE_RANGE,
  getDefaultEarliestDate,
  getDefaultEndDate,
  getDefaultStartDate,
  getRealActiveDateBeginAndTips,
  MAX_DATE_RANGE_DAYS,
} from '@/helpers/utils/date';
import Logger from '@/helpers/utils/logger';
import { compassRuleService } from '@/services/compass-rule-service';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { compassUserService } from '@/services/compass-user-service';
import {
  UserPersonaType,
  UserPersonaTypeMap,
  UserPartitionTypeGroup,
  UserPartitionTypeGroupMap,
  UserPartitionTypeMap,
  UserPartitionType,
} from '@/helpers/constants/model';
import { dayjs, RequestErrorInfo } from '@tencent/xpage-web-sdk';
import { UserPortraitInfo } from '@/helpers/types/portrait';
import { AoErrorCode, AoErrorCodeMap } from '@/helpers/types/models/error-code';

const logger = new Logger('user-insight-model');

export default class UserInsightModel {
  private static instance: UserInsightModel | null = null;

  static getInstance() {
    if (!UserInsightModel.instance) {
      UserInsightModel.instance = new UserInsightModel();
    }
    return UserInsightModel.instance;
  }

  static destroyInstance() {
    UserInsightModel.instance = null;
  }
  // ========== 页面状态 ==========
  /** 页面是否初始化完成 */
  pageInited = false;

  /** 侧边栏大纲信息 */
  readonly outlineInfo = {
    userLayer: {
      label: '用户分层',
      id: 'user-layer-info',
    },
    userFlow: {
      label: '用户流向分析',
      id: 'user-flow-info',
    },
    userPortriat: {
      label: '用户画像对比',
      id: 'user-portriat-info',
    },
  };
  /** 侧边栏大纲列表数据 */
  readonly outlineData = Object.values(this.outlineInfo);

  /** 快捷查询日期类型 */
  dateFilter = ref({
    /** 查询的结束日期 */
    getDateEnd: getDefaultEndDate(),
  });
  /** 日期类型，分层使用 */
  dateFilterOfPortrait = ref({
    getDateBegin: getDefaultStartDate(),
    getDateEnd: getDefaultEndDate(),
  });
  /** 日期类型，图表使用 */
  dateFilterOfTrend = ref({
    getDateBegin: getDefaultStartDate(),
    getDateEnd: getDefaultEndDate(),
  });
  // 自定义指标弹框
  showIndicatorDialog = ref<boolean>(false);
  userFlowGroupTabs = ref<{
    currentIndex: number;
    tabs: { id: string | number; label: string }[];
  }>({
    currentIndex: 1,
    tabs: [
      {
        id: UserPartitionTypeGroup.GROUP_REBUY,
        label: UserPartitionTypeGroupMap[UserPartitionTypeGroup.GROUP_REBUY].title,
      },
      {
        id: UserPartitionTypeGroup.GROUP_FREQ,
        label: UserPartitionTypeGroupMap[UserPartitionTypeGroup.GROUP_FREQ].title,
      },
      {
        id: UserPartitionTypeGroup.GROUP_TREND,
        label: UserPartitionTypeGroupMap[UserPartitionTypeGroup.GROUP_TREND].title,
      },
    ],
  });
  userFlowSelectedType = ref(UserPartitionType.USER_PATITION_HIGH_FREQ);

  /** 最新的罗盘规则（可能还没生效的罗盘规则）用于显示在页面顶部 */
  compassRule = ref<CompassRule>({
    trade_cycle: 7,
    high_freq_trade_user_limit: 0,
    low_freq_trade_user_limit: 0,
    lose_user_cycle: 0,
  });
  /** 用户分层 - 原始数据 */
  userPartition = ref<UserPartition>({
    user_partition_statistics_list: [],
    user_trend_list: [],
  });
  /** 用户分层 - 统计数据部分 */
  userPartitionOfStatistics = ref<StatisticDataCardInfo[]>([]);
  /** 用户分层 - 统计数据部分分组 */
  userPartitionOfStatisticsGroup = computed<{
    [groupId: number]: StatisticDataCardInfo[];
  }>(() => {
    const getGroupData = (groupId: UserPartitionTypeGroup): StatisticDataCardInfo[] =>
      this.userPartitionOfStatistics.value
        .filter(
          (item) =>
            UserPartitionTypeMap[item.overviewType as UserPartitionTypeGroup].group ===
            groupId
        )
        .sort((a, b) => Number(a.overviewType) - Number(b.overviewType));

    return {
      [UserPartitionTypeGroup.GROUP_ALL]: getGroupData(UserPartitionTypeGroup.GROUP_ALL),
      [UserPartitionTypeGroup.GROUP_REBUY]: getGroupData(
        UserPartitionTypeGroup.GROUP_REBUY
      ),
      [UserPartitionTypeGroup.GROUP_FREQ]: getGroupData(
        UserPartitionTypeGroup.GROUP_FREQ
      ),
      [UserPartitionTypeGroup.GROUP_TREND]: getGroupData(
        UserPartitionTypeGroup.GROUP_TREND
      ),
    };
  });
  /** 用户分层 - 统计数据部分映射 */
  userPartitionOfStatisticsMap = computed<{
    [groupId: number]: StatisticDataCardInfo;
  }>(() => {
    const res: { [groupId: number]: StatisticDataCardInfo } = {};
    this.userPartitionOfStatistics.value.forEach((x) => {
      res[x.overviewType as number] = x;
    });
    return res;
  });
  /** 用户分层 - 图表数据 */
  activityOverviewOfTrend = computed<StatisticDataTrendInfo[]>(() =>
    // 根据选择的统计数据、原始数据，获取数据趋势
    getUserInsightOfTrend(this.userPartitionOfStatistics.value, this.userPartition.value)
  );

  /** 用户流向 - 原始数据 */
  userFlow = ref<UserFlow>({
    user_flow_info: [],
  });
  /** 用户流向 - 图表左右两侧 */
  userFlowFormatedOfData = computed<{
    left: Record<number, SankeyDataCardInfo[]>;
    right: SankeyDataCardInfo[];
  }>(() => {
    const currentGroupId =
      this.userFlowGroupTabs.value.tabs[this.userFlowGroupTabs.value.currentIndex].id;
    const currentGroupTypes = Object.entries(UserPartitionTypeMap)
      .filter(([_key, value]) => value.group === currentGroupId)
      .map(([key, _value]) => Number(key));

    const left: Record<number, SankeyDataCardInfo[]> = {};
    currentGroupTypes.forEach((type) => {
      const targetFlowInfo = (this.userFlow.value.user_flow_info || []).find(
        (x) => x.user_partition_type === type
      );

      left[type] = currentGroupTypes.map((sourceType) => {
        const sourceFlowInfo = (targetFlowInfo?.user_flow_conversion || []).find(
          (x) => x.user_partition_type === sourceType
        );
        return {
          label: UserPartitionTypeMap[sourceType].title,
          value: sourceType,
          num: sourceFlowInfo?.user_num || 0,
          converRate: sourceFlowInfo?.conversion_percent || '0%',
          compare: sourceFlowInfo?.user_compare_last_cycle || 0,
        };
      });
    });

    const right = currentGroupTypes.map((type) => ({
      label: UserPartitionTypeMap[type].title,
      value: type,
      num:
        (this.userFlow.value.user_flow_info || []).find(
          (x) => x.user_partition_type === type
        )?.user_num || 0,
    }));

    return {
      left,
      right,
    };
  });
  /** 用户流向 - 图表中间部分 */
  userFlowFormatedOfChart = computed<{
    nodes: Array<{ type: number; index: number }>;
    links: Array<{ source: number; target: number; value: number }>;
  }>(() => {
    const currentGroupId =
      this.userFlowGroupTabs.value.tabs[this.userFlowGroupTabs.value.currentIndex].id;
    const currentType = this.userFlowSelectedType.value;
    const currentGroupTypes = Object.entries(UserPartitionTypeMap)
      .filter(([_key, value]) => value.group === currentGroupId)
      .map(([key, _value]) => Number(key));

    // 包含左、右两侧所有节点
    const nodes: Array<{ type: number; index: number }> = currentGroupTypes.map(
      (type, index) => ({ type, index })
    );
    // 右侧只有一个节点，所以直接push；push的时候，index自增
    nodes.push({ type: currentType, index: nodes.length });

    const targetFlowInfo = (this.userFlow.value.user_flow_info || []).find(
      (x) => x.user_partition_type === currentType
    );
    const links: Array<{ source: number; target: number; value: number }> = [];
    currentGroupTypes.forEach((type, index) => {
      links.push({
        source: index,
        target: nodes.length - 1, // nodes里最后一个节点，即图中右侧的节点
        value:
          (targetFlowInfo?.user_flow_conversion || []).find(
            (x) => x.user_partition_type === type
          )?.user_num || 0,
      });
    });
    return {
      nodes,
      links,
    };
  });
  /** 用户画像 - 原始数据 */
  userPersonaSurvey = ref<UserPersonaSurvey>({
    user_persona_survey_category_list: [],
  });
  /** 用户画像 - 格式化数据 */
  userPersonaSurveyFormated = computed<UserPortraitInfo>(() => {
    console.log('userPersonaSurveyFormated', this.userPersonaSurvey.value);

    return {
      title: '全部用户',
      link: {
        href: `/home/<USER>
          this.activeCompassRulePeriodFormated.value.before
        }&disableDateAfter=${this.activeCompassRulePeriodFormated.value.after}&activeRuleVersion=${
          this.activeCompassRule.value.rule_version
        }&activeBrandVersion=${this.activeCompassRule.value.brand_version}`,
      },
      data: (this.userPersonaSurvey.value.user_persona_survey_category_list || []).map(
        (item) => ({
          title:
            UserPersonaTypeMap[item.user_persona_type as UserPersonaType]?.label || '',
          value: item.label,
          desc: `占比 ${item.percent}`,
        })
      ),
    };
  });
  /** 生效的罗盘规则（可能并不是最新的罗盘规则），用于显示在用户洞察数据上 */
  activeCompassRule = ref<ActiveCompassRule>({
    trade_cycle: 7,
    high_freq_trade_user_limit: 0,
    low_freq_trade_user_limit: 0,
    lose_user_cycle: 0,
    rule_version: 1,
    brand_version: 2,
    recommend_cycle: 1,
  });
  /** 报告生效时间段 */
  activeCompassRulePeriod = ref<ActiveCompassRulePeriod>({
    active_begin_time: 0,
    active_end_time: 0,
  });
  /** 报告生效时间段 - 格式化后的数据是否已经初始化 */
  activeCompassRulePeriodInited = false;
  /** 生效中的罗盘规则 - 格式化后的数据 */
  activeCompassRulePeriodFormated = ref({
    before: getDefaultEarliestDate(),
    after: getDefaultEndDate(),
  });
  dateFilterTipsOfTrend = computed(
    () =>
      getRealActiveDateBeginAndTips(
        this.activeCompassRulePeriod.value,
        this.dateFilterOfTrend.value.getDateBegin
      ).tips
  );

  /** 罗盘规则是否有变化 */
  compassRuleChanged = computed(() => {
    let changed = false;
    Object.keys(this.compassRule.value).forEach((key) => {
      if (
        this.compassRule.value[key as unknown as keyof CompassRule] !==
        this.activeCompassRule.value[key as unknown as keyof CompassRule]
      ) {
        changed = true;
      }
    });
    return changed;
  });

  // ========== 页面方法 ==========
  /**
   * 初始化页面数据
   */
  async initPageModel(): Promise<void> {
    logger.log('initPageModel invoke');
    try {
      await Promise.all([this.getCompassRule(), this.getUserInsight()]);
    } catch (e) {
      logger.error('initPageModel fail', e);

      this.pageInited = true;
      return;
    }

    this.pageInited = true;
  }

  /**
   * 获取罗盘规则
   */
  async getCompassRule(): Promise<void> {
    try {
      this.compassRule.value = await compassRuleService.getCompassRule();
    } catch (e) {
      logger.error('获取罗盘规则失败', e);
      MessagePlugin.warning('获取罗盘规则失败');
      return;
    }
  }

  /**
   * 获取用户洞察数据
   */
  async getUserInsight(): Promise<void> {
    let userInsight: TV1GetCompassUserInsightResponseParams['data'];

    try {
      userInsight = await compassUserService.getUserInsight(
        this.dateFilter.value.getDateEnd
      );
    } catch (e) {
      logger.error('获取用户洞察数据失败', e);

      // 数据为空：提示
      if (
        (e as RequestErrorInfo)?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY
      ) {
        MessagePlugin.info(
          AoErrorCodeMap[
            AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY
          ].title
        );
        throw new Error('获取用户洞察数据失败');
      }

      // 其他情况：报错
      MessagePlugin.warning('获取用户洞察数据失败');
      return;
    }

    this.userPartition.value = userInsight!.user_partition;
    this.userFlow.value = userInsight!.user_flow;
    this.userPersonaSurvey.value = userInsight!.user_persona_survey;
    this.activeCompassRule.value = userInsight!.active_compass_rule;
    this.activeCompassRulePeriod.value = userInsight!.active_compass_rule_period;

    this.userPartitionOfStatistics.value = getUserInsightOfStatistics(
      userInsight!.user_partition
    );
    this.formatActiveCompassRulePeriod();

    if (userInsight?.real_get_date) {
      this.dateFilter.value.getDateEnd = userInsight.real_get_date;

      // 用户分层的起始时间为生效周期；由于包含开始时间对应的当天，所以要-1
      this.dateFilterOfPortrait.value.getDateBegin = dayjs(userInsight.real_get_date)
        .subtract(userInsight!.active_compass_rule.trade_cycle - 1, 'day')
        .format(DateFormat.api);
      this.dateFilterOfPortrait.value.getDateEnd = userInsight.real_get_date;

      // 用户趋势的起始时间固定为31天；由于包含开始时间对应的当天，所以要-1
      this.dateFilterOfTrend.value.getDateBegin = dayjs(userInsight.real_get_date)
        .subtract(DEFAULT_USER_INSIGHT_TRENT_DATE_RANGE - 1, 'day')
        .format('YYYY-MM-DD');
      this.dateFilterOfTrend.value.getDateEnd = userInsight.real_get_date;
    }
  }

  /**
   * 格式化报告生效时间段
   */
  formatActiveCompassRulePeriod() {
    // 只有第一次返回的数据才是准确的，所以只初始化一次
    if (this.activeCompassRulePeriodInited) {
      return;
    }

    this.activeCompassRulePeriodInited = true;
    this.activeCompassRulePeriodFormated.value = {
      // before 回禁用before对应的那天，所以需要往前算一天
      before: dayjs(`${this.activeCompassRulePeriod.value.active_begin_time}`, 'YYYYMMDD')
        .subtract(1, 'day')
        .format('YYYY-MM-DD'),
      // after 不包含after对应的那天，所以不需要改动
      after: dayjs(
        `${this.activeCompassRulePeriod.value.active_end_time}`,
        'YYYYMMDD'
      ).format('YYYY-MM-DD'),
    };
  }

  /**
   * 修改自定义指标
   */
  changeCompassRuleIndicator = async (
    value: Omit<CompassRule, 'trade_cycle'>
  ): Promise<void> => {
    logger.log('changeCompassRuleIndicator invoke', this.compassRule.value, value);
    try {
      const res = await compassRuleService.setCompassRule({
        ...this.compassRule.value,
        ...value,
      });
      this.compassRule.value = res;
    } catch (e) {
      logger.warn('changeCompassRuleIndicator error', e);
      MessagePlugin.warning('修改指标失败');
      this.compassRule.value = {
        ...this.compassRule.value,
      };
      return;
    }
    MessagePlugin.success('设置已修改，新数据预计于次日12:00更新。');
  };

  /**
   * 更改周期天数
   */
  changeCycleDays = async (value: number): Promise<void> => {
    logger.log('changeCycleDays invoke', value);

    // 当消费周期为365天时，流失用户的交易周期最大不可超过2
    if (
      value >= TradeCycleDays.THREE_HUNDRED_SIXTY_FIVE &&
      this.compassRule.value.lose_user_cycle > 2
    ) {
      logger.log('changeCycleDays', '周期天数超过限制', value);
      const dialog = DialogPlugin.confirm({
        header: '',
        body: '当消费周期为365天，流失用户暂只支持为设置为近2周期无交易，请先修改流失用户周期',
        confirmBtn: '去修改',
        cancelBtn: '取消',
        onConfirm: () => {
          this.showIndicatorDialog.value = true;
          dialog.destroy();
        },
      });
      return;
    }

    try {
      const res = await compassRuleService.setCompassRule({
        ...this.compassRule.value,
        trade_cycle: value,
      });
      this.compassRule.value = res;
    } catch (e) {
      logger.warn('changeCycleDays error', e);
      MessagePlugin.warning('修改周期天数失败');
      return;
    }

    MessagePlugin.success('设置已修改，新数据预计于次日12:00更新。');
  };

  /**
   * 更改周期结束时间
   * @param value 周期结束时间 YYYY-MM-DD 格式
   */
  changeCycleEndDate = (value: string) => {
    logger.log('changeCyclyEndData invoke', value);

    this.dateFilter.value.getDateEnd = value;
    this.getUserInsight();
  };

  changeUserFlowGroupTab = (index: number, item: { id: number; label: string }) => {
    logger.log('changeUserFlowGroupTab invoke', index, item);

    this.userFlowGroupTabs.value.currentIndex = index;
    const userPersonaTypes = Object.entries(UserPartitionTypeMap)
      .filter(
        ([_key, value]) => value.group === this.userFlowGroupTabs.value.tabs[index].id
      )
      .map(([key, _value]) => Number(key));
    [this.userFlowSelectedType.value] = userPersonaTypes;
  };

  /**
   * 修改要显示的图表数据
   */
  handleDataCardCheckChange = (
    item: StatisticDataCardInfo,
    checked: boolean
  ): {
    success: boolean;
    message: string;
  } => {
    logger.log('handleDataCardCheckChange', item, checked);

    const res = switchOverviewCheckedState(
      this.userPartitionOfStatistics.value,
      checked,
      item
    );
    if (res.success) {
      this.userPartitionOfStatistics.value = res.statistics;
    }

    return {
      success: res.success,
      message: res.message,
    };
  };

  onUserFlowGroupTypeChange = (id: number) => {
    this.userFlowSelectedType.value = id;
  };
  /**
   * 修改用户洞察数据的日期范围
   * @param dateBegin 开始日期 YYYY-MM-DD
   * @param dateEnd 结束日期 YYYY-MM-DD
   */
  changeInsightDateRange = async (getDateBegin: string, getDateEnd: string) => {
    logger.log('changeInsightDateRange invoke', getDateBegin, getDateEnd);
    let userTrend: TV1GetCompassUserTrendResponseParams['data'];

    if (dayjs(getDateEnd).diff(dayjs(getDateBegin), 'day') > MAX_DATE_RANGE_DAYS) {
      logger.error(
        'updateActivityOverviewTrade',
        '日期范围超过限制',
        getDateBegin,
        getDateEnd
      );
      MessagePlugin.error(`日期范围不能超过${MAX_DATE_RANGE_DAYS}天`);
      return;
    }

    try {
      userTrend = await compassUserService.getUserTrend(
        getDateBegin,
        getDateEnd,
        this.activeCompassRule.value.rule_version,
        this.activeCompassRule.value.brand_version
      );
    } catch (e) {
      logger.error('changeInsightDateRange fail', e);
      MessagePlugin.error('获取用户趋势数据失败');
      return;
    }

    this.userPartition.value.user_trend_list = userTrend!.user_trend_list;
    this.dateFilterOfTrend.value = {
      getDateBegin,
      getDateEnd,
    };
  };

  // 弹框显示
  indicatorDialogShow = () => {
    this.showIndicatorDialog.value = true;
  };
  indicatorDialogHide = () => {
    this.showIndicatorDialog.value = false;
  };
}
