import { computed, ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import {
  getActivityOverviewOfStatistics,
  getActivityOverviewOfTrend,
  switchOverviewCheckedState,
} from '@/helpers/insight-helper';
import {
  StatisticDataTrendInfo,
  type StatisticDataCardInfo,
} from '@/helpers/types/insight';
import type { UserPortraitInfo } from '@/helpers/types/portrait';
import { DatePickerType } from '@/helpers/constants/common';
import { activityService, BrandManagerActivityExtend } from '@/services/activity-service';
import { compassActivityService } from '@/services/compass-activity-service';
import Logger from '@/helpers/utils/logger';
import {
  ActiveCompassRule,
  ActiveCompassRulePeriod,
  TV1GetActivityResponseParams,
  TV1GetCompassActivityOverviewResponseParams,
  ActivityOverview,
  UserPersonaSurvey,
} from '@/helpers/types/models';
import {
  ActivityStatusOptions,
  ActivityOverviewType,
  ActivityOverviewTypeGroup,
  ActivityOverviewTypeGroupMap,
  ActivityOverviewTypeMap,
  ActivityStatusExtend,
  ActivityType,
  UserPersonaType,
  UserPersonaTypeMap,
} from '@/helpers/constants/model';
import {
  getDefaultEarliestDate,
  getDefaultEndDate,
  getDefaultEndDateNext,
  getDefaultStartDate,
  getRealActiveDateBeginAndTips,
  MAX_DATE_RANGE_DAYS,
} from '@/helpers/utils/date';
import { dayjs, RequestErrorInfo } from '@tencent/xpage-web-sdk';
import { AoErrorCode, AoErrorCodeMap } from '@/helpers/types/models/error-code';
import SelectableList from '@/components/selectable-list.vue';

const logger = new Logger('shake-activity-overview-model');

export default class ShakeActivityOverviewModel {
  private static instance: ShakeActivityOverviewModel | null = null;

  static getInstance() {
    if (!ShakeActivityOverviewModel.instance) {
      ShakeActivityOverviewModel.instance = new ShakeActivityOverviewModel();
    }
    return ShakeActivityOverviewModel.instance;
  }

  static destroyInstance() {
    ShakeActivityOverviewModel.instance = null;
  }

  // ========= 页面状态 =========
  /** 页面是否已经初始化 */
  pageInited = false;
  /** 顶部面包屑数据 */
  readonly breadcrumbData = [{ label: '摇一摇有优惠' }];
  /** 侧边栏大纲信息 */
  readonly outlineInfo = {
    saleData: {
      label: '活动交易数据',
      id: 'sale-data-info',
    },
    conversionData: {
      label: '人群洞察',
      id: 'conversion-data-info',
    },
    allProduct: {
      label: '全部活动',
      id: 'all-product-info',
    },
  };

  /** 侧边栏大纲列表数据 */
  readonly outlineData = Object.values(this.outlineInfo);

  /** 快捷查询日期类型 */
  dateFilter = ref({
    dateType: DatePickerType.Week,
    /** after的下一天，用于当作dateFilter的基准日期 */
    baseDate: getDefaultEndDateNext(),
    /** 查询的开始日期 */
    getDateBegin: getDefaultStartDate(),
    /** 查询的结束日期 */
    getDateEnd: getDefaultEndDate(),
  });

  // ========= 后台返回的数据 =========

  /** 原始的活动概览数据 */
  activityOverview = ref<ActivityOverview>({
    activity_overview_statistics_list: [],
    activity_overview_trend_list: [],
  });
  /** 活动概览数据 - 统计值部分 */
  activityOverviewOfStatistics = ref<StatisticDataCardInfo[]>([]);
  /** 活动概览数据 - 统计值部分按类型分组 */
  activityOverviewOfStatisticsGrouped = computed<
    { title: string; statistic: StatisticDataCardInfo[] }[]
  >(() => {
    const getGroupData = (
      groupId: ActivityOverviewTypeGroup
    ): { title: string; statistic: StatisticDataCardInfo[] } => {
      const groupInfo = ActivityOverviewTypeGroupMap[groupId];
      return {
        title: groupInfo.title,
        statistic: this.activityOverviewOfStatistics.value.filter(
          (item) =>
            ActivityOverviewTypeMap[item.overviewType as ActivityOverviewType].group ===
            groupId
        ),
      };
    };
    return [
      getGroupData(ActivityOverviewTypeGroup.GROUP_SALE),
      getGroupData(ActivityOverviewTypeGroup.GROUP_CONVERT),
    ];
  });
  /** 活动概览数据 - 图表部分 */
  activityOverviewOfTrend = computed<StatisticDataTrendInfo[]>(() =>
    // 根据选择的统计数据、原始数据，获取数据趋势
    getActivityOverviewOfTrend(
      this.activityOverviewOfStatistics.value,
      this.activityOverview.value
    )
  );
  /** 用户画像 */
  activityPersona = ref<UserPersonaSurvey>({
    user_persona_survey_category_list: [],
  });
  /** 用户画像数据格式化 */
  activityPersonaFormated = computed<UserPortraitInfo>(() => ({
    title: '全部用户',
    link: {
      href: `/home/<USER>
        this.activeCompassRule.value.brand_version
      }&baseDate=${this.dateFilter.value.baseDate}&disableDateBefore=${
        this.activeCompassRulePeriodFormated.value.before
      }&disableDateAfter=${this.activeCompassRulePeriodFormated.value.after}`,
    },
    data: (this.activityPersona.value.user_persona_survey_category_list || []).map(
      (x) => ({
        title: UserPersonaTypeMap[x.user_persona_type as UserPersonaType].label,
        value: x.label,
        desc: `占比 ${x.percent}`,
      })
    ),
  }));
  /** 生效中的罗盘规则 - 后台返回的原始数据 */
  activeCompassRule = ref<ActiveCompassRule>({
    trade_cycle: 0,
    high_freq_trade_user_limit: 0,
    low_freq_trade_user_limit: 0,
    lose_user_cycle: 0,
    rule_version: 0,
    brand_version: 0,
    recommend_cycle: 0,
  });
  /** 报告生效时间段 */
  activeCompassRulePeriod = ref<ActiveCompassRulePeriod>({
    active_begin_time: 0,
    active_end_time: 0,
  });
  /** 报告生效时间段 - 格式化后的数据是否已经初始化 */
  activeCompassRulePeriodInited = false;
  /** 生效中的罗盘规则 - 格式化后的数据 */
  activeCompassRulePeriodFormated = ref({
    before: getDefaultEarliestDate(),
    after: getDefaultEndDate(),
  });
  dateFilterTips = computed(
    () =>
      getRealActiveDateBeginAndTips(
        this.activeCompassRulePeriod.value,
        this.dateFilter.value.getDateBegin
      ).tips
  );

  // ========= 活动列表 =========
  activitySearch = ref({
    actName: '',
  });
  /** 活动表格列定义 */
  activityTableColumns = [
    { colKey: 'act_name', title: '活动名称', width: '200px' },
    {
      colKey: 'displayActivityState',
      title: '状态',
      width: '120px',
      filter: {
        component: SelectableList,
        props: {
          options: ActivityStatusOptions(),
        },
        style: { maxHeight: '283px' },
        resetValue: ActivityStatusExtend.ACTIVITY_STATUS_ALL,
        popupProps: {
          showArrow: false,
          overlayInnerClassName: 'insight-custom-table-filter-popup-content',
        },
        confirmEvents: ['onChange'],
      },
    },
    { colKey: 'displayStocks', title: '商家券', minWidth: '180px' },
    { colKey: 'displayActivityTime', title: '活动时间', minWidth: '250px' },
    { colKey: 'displayCreateTime', title: '创建时间', minWidth: '250px' },
    { colKey: 'operation', title: '操作', align: 'right', width: '120px' },
  ];
  /** 活动表格分页信息 */
  activityPageInfo = ref({
    total: 0,
    size: 10,
    current: 1,
  });

  /** 活动展现的表格数据 */
  activityList = ref<BrandManagerActivityExtend[]>([]);
  activityListFilter = ref({
    displayActivityState: ActivityStatusExtend.ACTIVITY_STATUS_ALL,
  });

  // ========= 方法 =========

  /**
   * 页面初始化
   */
  async initPageData() {
    try {
      await Promise.all([this.getActivityOverview(), this.getActivityTableList()]);
    } catch (e) {
      logger.error('initPageData', 'fail', e);

      this.pageInited = true;
      return;
    }

    this.pageInited = true;
  }

  /**
   * 获取活动概览
   */
  async getActivityOverview() {
    let res: TV1GetCompassActivityOverviewResponseParams['data'];
    try {
      res = await compassActivityService.getCompassActivityOverview(
        getRealActiveDateBeginAndTips(
          this.activeCompassRulePeriod.value,
          this.dateFilter.value.getDateBegin
        ).dateBegin,
        this.dateFilter.value.getDateEnd,
        ActivityType.ACTIVITY_SHAKE
      );
    } catch (e) {
      logger.error('getActivityOverview', '获取活动概况数据失败', e);

      // 数据为空：提示
      if (
        (e as RequestErrorInfo)?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY
      ) {
        MessagePlugin.info(
          AoErrorCodeMap[
            AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY
          ].title
        );
        throw new Error('获取活动概况数据为空');
      }

      // 其他情况：报错
      MessagePlugin.error('获取活动概况数据失败');
      throw new Error('获取活动概况数据失败');
    }

    this.activityOverview.value = res!.activity_overview!;
    this.activityPersona.value = res!.activity_persona!;
    this.activeCompassRule.value = res!.active_compass_rule;
    this.activeCompassRulePeriod.value = res!.active_compass_rule_period;

    this.activityOverviewOfStatistics.value = getActivityOverviewOfStatistics(
      res!.activity_overview!
    );
    this.formatActiveCompassRulePeriod();
  }

  /**
   * 获取活动列表
   */
  async getActivityTableList() {
    logger.log('getActivityTableList', this.activityPageInfo.value);
    const { size, current } = this.activityPageInfo.value;

    let res: TV1GetActivityResponseParams['data'];
    try {
      res = await activityService.getAcitvityList(
        (current - 1) * size,
        size,
        this.activitySearch.value.actName,
        this.activityListFilter.value.displayActivityState ===
          ActivityStatusExtend.ACTIVITY_STATUS_ALL
          ? undefined
          : this.activityListFilter.value.displayActivityState
      );
    } catch (e) {
      logger.warn('getActivityTableList fail', e);
      MessagePlugin.error('获取活动列表失败');
      return;
    }

    this.activityList.value = res!.activity_info_list.map((x) =>
      activityService.formatActivityItem(x)
    );
    this.activityPageInfo.value.total = res!.total_count;
  }

  /**
   * 格式化报告生效时间段
   */
  formatActiveCompassRulePeriod() {
    // 只有第一次返回的数据才是准确的，所以只初始化一次
    if (this.activeCompassRulePeriodInited) {
      return;
    }

    this.activeCompassRulePeriodInited = true;
    this.activeCompassRulePeriodFormated.value = {
      // before 回禁用before对应的那天，所以需要往前算一天
      before: dayjs(`${this.activeCompassRulePeriod.value.active_begin_time}`, 'YYYYMMDD')
        .subtract(1, 'day')
        .format('YYYY-MM-DD'),
      // after 不包含after对应的那天，所以不需要改动
      after: dayjs(
        `${this.activeCompassRulePeriod.value.active_end_time}`,
        'YYYYMMDD'
      ).format('YYYY-MM-DD'),
    };
    this.dateFilter.value.baseDate = dayjs(
      `${this.activeCompassRulePeriod.value.active_end_time}`,
      'YYYYMMDD'
    )
      .add(1, 'day')
      .format('YYYY-MM-DD');
  }

  /**
   * 更新活动概览的日期范围
   */
  async updateActivityOverview(
    type: DatePickerType,
    getDateBegin: string,
    getDateEnd: string
  ) {
    logger.log('updateActivityOverview', type, getDateBegin, getDateEnd);
    if (!this.pageInited) {
      logger.log('updateActivityOverview', 'page not inited');
      return;
    }

    if (dayjs(getDateEnd).diff(dayjs(getDateBegin), 'day') > MAX_DATE_RANGE_DAYS) {
      logger.error(
        'updateActivityOverviewTrade',
        '日期范围超过限制',
        getDateBegin,
        getDateEnd
      );
      MessagePlugin.error(`日期范围不能超过${MAX_DATE_RANGE_DAYS}天`);
      return;
    }

    this.dateFilter.value.getDateBegin = getDateBegin;
    this.dateFilter.value.getDateEnd = getDateEnd;
    return this.getActivityOverview();
  }

  /**
   * 选择要显示图标的卡片
   */
  handleDataCardCheckChange = (
    item: StatisticDataCardInfo,
    checked: boolean
  ): {
    success: boolean;
    message: string;
  } => {
    logger.log('handleDataCardCheckChange', item, checked);

    const res = switchOverviewCheckedState(
      this.activityOverviewOfStatistics.value,
      checked,
      item
    );
    if (res.success) {
      this.activityOverviewOfStatistics.value = res.statistics;
    }

    return {
      success: res.success,
      message: res.message,
    };
  };

  /**
   * 活动列表搜索
   */
  handleActivityTableSearch(actName: string) {
    logger.log('handleActivityTableSearch invoke', actName);
    this.activitySearch.value.actName = actName.trim();

    this.activityPageInfo.value.current = 1;
    this.getActivityTableList();
  }

  /**
   * 活动列表筛选
   */
  handleActivityTableFilter(status: number) {
    logger.log('handleActivityTableFilter invoke', status);
    this.activityListFilter.value.displayActivityState = status;

    this.activityPageInfo.value.current = 1;
    this.getActivityTableList();
  }

  /**
   * 活动列表翻页
   */
  handleActivityTablePageChange(num: number, size: number) {
    logger.log('handleActivityTablePageChange invoke', num, size);
    this.activityPageInfo.value.size = size;
    this.activityPageInfo.value.current = num;

    this.getActivityTableList();
  }
}
