import { StatisticDataTrendInfo, type StatisticDataCardInfo } from '@/helpers/types/insight';
import {
  getBusinessOverviewOfStatistics,
  getBusinessOverviewOfTrend,
  switchOverviewCheckedState,
} from '@/helpers/insight-helper';
import { DatePickerType } from '@/helpers/constants/common';
import { businessOverviewService } from '@/services/business-overview-service';
import Logger from '@/helpers/utils/logger';
import {
  ActiveCompassRule,
  ActiveCompassRulePeriod,
  BusinessOverview,
  BusinessSuggest,
  Optimization,
  TV1GetCompassBusinessOverviewResponseParams,
} from '@/helpers/types/models';
import { DialogPlugin, MessagePlugin } from 'tdesign-vue-next';
import { BusinessType } from '@/helpers/constants/model';
import {
  getDefaultEarliestDate,
  getDefaultEndDate,
  getDefaultEndDateNext,
  getDefaultStartDate,
  getRealActiveDateBeginAndTips,
  MAX_DATE_RANGE_DAYS,
} from '@/helpers/utils/date';
import { dayjs, RequestErrorInfo } from '@tencent/xpage-web-sdk';
import { AoErrorCode, AoErrorCodeMap } from '@/helpers/types/models/error-code';
import { computed, ref } from 'vue';

const logger = new Logger('business-overview-model');

export default class BusinessOverviewModel {
  private static instance: BusinessOverviewModel | null = null;

  // ========= 页面状态 =========
  /** 页面是否已经初始化 */
  pageInited = false;
  /** 侧边栏大纲信息 */
  readonly outlineInfo = {
    transaction: {
      label: '交易数据',
      id: 'transaction-info',
    },
    marketing: {
      label: '营销数据',
      id: 'marketing-info',
    },
    management: {
      label: '经营建议',
      id: 'management-suggestion-info',
    },
  };
  /** 侧边栏大纲列表数据 */
  readonly outlineData = Object.values(this.outlineInfo);

  /** 快捷查询日期类型 */
  dateFilter = ref({
    dateType: DatePickerType.Week,
    /** after的下一天，用于当作dateFilter的基准日期 */
    baseDate: getDefaultEndDateNext(),

    trade: {
      /** 查询的开始日期 */
      getDateBegin: getDefaultStartDate(),
      /** 查询的结束日期 */
      getDateEnd: getDefaultEndDate(),
    },

    market: {
      /** 查询的开始日期 */
      getDateBegin: getDefaultStartDate(),
      /** 查询的结束日期 */
      getDateEnd: getDefaultEndDate(),
    },
  });

  // ========= 后台返回的数据 =========
  /** 交易概况部分 - 原始数据 */
  tradeOverview = ref<BusinessOverview>({
    business_overview_statistics_list: [],
    business_overview_trend_list: [],
  });
  /** 经营概况部分 - 原始数据 */
  marketOverview = ref<BusinessOverview>({
    business_overview_statistics_list: [],
    business_overview_trend_list: [],
  });
  /** 经营建议部分 - 原始数据 */
  businessSuggest = ref<BusinessSuggest>({
    optimization_list: [],
  });
  /** 生效中的罗盘规则 - 原始数据 */
  activeCompassRule = ref<ActiveCompassRule>({
    trade_cycle: 0,
    high_freq_trade_user_limit: 0,
    low_freq_trade_user_limit: 0,
    lose_user_cycle: 0,
    rule_version: 0,
    brand_version: 0,
    recommend_cycle: 0,
  });
  /** 报告生效时间段 - 原始数据 */
  activeCompassRulePeriod = ref<ActiveCompassRulePeriod>({
    active_begin_time: 0,
    active_end_time: 0,
  });
  /** 报告生效时间段 - 格式化后的数据是否已经初始化 */
  activeCompassRulePeriodInited = false;
  /** 生效中的罗盘规则 - 格式化后的数据 */
  activeCompassRulePeriodFormated = ref({
    // 禁用before之前的日期，包含before
    before: getDefaultEarliestDate(),
    // 禁用after之后的日期，不包含after，与tdesign的逻辑一致
    after: getDefaultEndDate(),
  });
  dateFilterTipsOfTrade = computed(
    () =>
      getRealActiveDateBeginAndTips(this.activeCompassRulePeriod.value, this.dateFilter.value.trade.getDateBegin).tips,
  );
  dateFilterTipsOfMarket = computed(
    () =>
      getRealActiveDateBeginAndTips(this.activeCompassRulePeriod.value, this.dateFilter.value.market.getDateBegin).tips,
  );

  /** 交易概况：统计数据部分 */
  tradeOverviewOfStatistics = ref<StatisticDataCardInfo[]>([]);
  /** 交易概况：图标部分 */
  tradeOverviewOfTrend = computed<StatisticDataTrendInfo[]>(() =>
    // 根据选择的统计数据、原始数据，获取数据趋势
    getBusinessOverviewOfTrend(this.tradeOverviewOfStatistics.value, this.tradeOverview.value),
  );
  /** 经营概况：统计数据部分 */
  marketOverviewOfStatistics = ref<StatisticDataCardInfo[]>([]);
  /** 经营概况：图表部分 */
  marketOverviewOfTrend = computed<StatisticDataTrendInfo[]>(() =>
    // 根据选择的统计数据、原始数据，获取数据趋势
    getBusinessOverviewOfTrend(this.marketOverviewOfStatistics.value, this.marketOverview.value),
  );

  /* 获取初始化数据 */
  async initPageData() {
    try {
      await this.getBusinessOverview();
    } catch (e) {
      logger.error('initPageData', 'fail', e);
      this.pageInited = true;
      return;
    }

    this.pageInited = true;
  }

  /**
   * 获取经营概况数据
   */
  getBusinessOverview = async () => {
    let res: TV1GetCompassBusinessOverviewResponseParams['data'];
    try {
      res = await businessOverviewService.getBusinessOverview(getDefaultStartDate(), getDefaultEndDate());
    } catch (e) {
      logger.error('getBusinessOverview', '获取经营概况数据失败', res);

      // 数据为空：提示
      if ((e as RequestErrorInfo)?.code === AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY) {
        MessagePlugin.info(
          AoErrorCodeMap[AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY].title,
        );
        throw new Error('获取经营概况数据为空');
      }

      // 其他情况：报错
      MessagePlugin.error('获取经营概况数据失败');
      throw new Error('获取经营概况数据失败');
    }

    this.tradeOverview.value = res!.trade_overview;
    this.marketOverview.value = res!.market_overview;
    this.businessSuggest.value = res!.business_suggest;
    this.activeCompassRule.value = res!.active_compass_rule;
    this.activeCompassRulePeriod.value = res!.active_compass_rule_period;

    this.tradeOverviewOfStatistics.value = getBusinessOverviewOfStatistics(res!.trade_overview);
    this.marketOverviewOfStatistics.value = getBusinessOverviewOfStatistics(res!.market_overview);
    this.formatActiveCompassRulePeriod();
  };

  /**
   * 格式化报告生效时间段
   */
  formatActiveCompassRulePeriod() {
    // 只有第一次返回的数据才是准确的，所以只初始化一次
    if (this.activeCompassRulePeriodInited) {
      return;
    }

    this.activeCompassRulePeriodInited = true;
    this.activeCompassRulePeriodFormated.value = {
      // before 会禁用before对应的那天，所以需要往前算一天
      before: dayjs(`${this.activeCompassRulePeriod.value.active_begin_time}`, 'YYYYMMDD')
        .subtract(1, 'day')
        .format('YYYY-MM-DD'),
      // after 不包含after对应的那天，所以不需要改动
      after: dayjs(`${this.activeCompassRulePeriod.value.active_end_time}`, 'YYYYMMDD').format('YYYY-MM-DD'),
    };
    this.dateFilter.value.baseDate = dayjs(`${this.activeCompassRulePeriod.value.active_end_time}`, 'YYYYMMDD')
      .add(1, 'day')
      .format('YYYY-MM-DD');
  }

  /**
   * 更新交易概况数据
   * @param type 日期类型
   * @param getDateBegin 查询的开始日期 YYYY-MM-DD
   * @param getDateEnd 查询的结束日期 YYYY-MM-DD
   */
  async updateActivityOverviewTrade(type: DatePickerType, getDateBegin: string, getDateEnd: string) {
    if (!this.pageInited) {
      logger.log('updateActivityOverviewTrade', 'page not inited');
      return;
    }

    if (dayjs(getDateEnd).diff(dayjs(getDateBegin), 'day') > MAX_DATE_RANGE_DAYS) {
      logger.error('updateActivityOverviewTrade', '日期范围超过限制', getDateBegin, getDateEnd);
      MessagePlugin.error(`日期范围不能超过${MAX_DATE_RANGE_DAYS}天`);
      return;
    }

    this.dateFilter.value.trade.getDateBegin = getDateBegin;
    this.dateFilter.value.trade.getDateEnd = getDateEnd;

    let res: BusinessOverview;
    try {
      res = await businessOverviewService.getBusinessOverviewByBusinessType(
        BusinessType.BUSINESS_TYPE_TRADE,
        getRealActiveDateBeginAndTips(this.activeCompassRulePeriod.value, getDateBegin).dateBegin,
        getDateEnd,
        this.activeCompassRule.value.brand_version,
        dayjs(`${this.activeCompassRulePeriod.value.active_begin_time}`, 'YYYYMMDD').format('YYYY-MM-DD'),
      );
    } catch (e) {
      logger.error('updateActivityOverviewTrade error', e);

      // 数据为空：提示
      if ((e as RequestErrorInfo)?.code === AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY) {
        MessagePlugin.info(
          AoErrorCodeMap[AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY].title,
        );
        throw new Error('获取交易概况数据为空');
      }

      // 其他情况：报错
      MessagePlugin.error('获取交易概况数据失败');
      throw e;
    }

    this.tradeOverview.value = res;
    this.tradeOverviewOfStatistics.value = getBusinessOverviewOfStatistics(res);
  }

  /**
   * 更新营销概况数据
   * @param type 日期类型
   * @param getDateBegin 查询的开始日期 YYYY-MM-DD
   * @param getDateEnd 查询的结束日期 YYYY-MM-DD
   */
  async updateBusinessOverviewMarket(type: DatePickerType, getDateBegin: string, getDateEnd: string) {
    if (!this.pageInited) {
      logger.log('updateBusinessOverviewMarket', 'page not inited');
      return;
    }

    if (dayjs(getDateEnd).diff(dayjs(getDateBegin), 'day') > MAX_DATE_RANGE_DAYS) {
      logger.error('updateActivityOverviewTrade', '日期范围超过限制', getDateBegin, getDateEnd);
      MessagePlugin.error(`日期范围不能超过${MAX_DATE_RANGE_DAYS}天`);
      return;
    }

    this.dateFilter.value.market.getDateBegin = getDateBegin;
    this.dateFilter.value.market.getDateEnd = getDateEnd;

    let res: BusinessOverview;
    try {
      res = await businessOverviewService.getBusinessOverviewByBusinessType(
        BusinessType.BUSINESS_TYPE_MARKET,
        getRealActiveDateBeginAndTips(this.activeCompassRulePeriod.value, getDateBegin).dateBegin,
        getDateEnd,
        this.activeCompassRule.value.brand_version,
        dayjs(`${this.activeCompassRulePeriod.value.active_begin_time}`, 'YYYYMMDD').format('YYYY-MM-DD'),
      );
    } catch (e) {
      logger.error('updateBusinessOverviewMarket error', e);

      // 数据为空：提示
      if ((e as RequestErrorInfo)?.code === AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_OWNER_ACCOUNT_DATA_EMPTY) {
        MessagePlugin.info(
          AoErrorCodeMap[AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_ACTIVITY_ACTIVE_DATA_EMPTY].title,
        );
        throw new Error('获取营销概况数据为空');
      }

      // 其他情况：报错
      MessagePlugin.error('获取营销概况数据失败');
      throw e;
    }

    this.marketOverview.value = res;
    this.marketOverviewOfStatistics.value = getBusinessOverviewOfStatistics(res);
  }

  /**
   * 切换交易概况的图表显示
   */
  handleDataCardCheckChangeOfTrade = (
    item: StatisticDataCardInfo,
    selected: boolean,
  ): {
    success: boolean;
    message: string;
  } => {
    logger.log('handleDataCardCheckChangeOfTrade', 'invoke', item);

    const res = switchOverviewCheckedState(this.tradeOverviewOfStatistics.value, selected, item);
    if (res.success) {
      this.tradeOverviewOfStatistics.value = res.statistics;
    }

    return {
      success: res.success,
      message: res.message,
    };
  };

  /**
   * 切换营销概况的图表显示
   */
  handleDataCardCheckChangeOfMarket = (
    item: StatisticDataCardInfo,
    selected: boolean,
  ): {
    success: boolean;
    message: string;
  } => {
    logger.log('handleDataCardCheckChangeOfMarket', 'invoke', item);

    const res = switchOverviewCheckedState(this.marketOverviewOfStatistics.value, selected, item);
    if (res.success) {
      this.marketOverviewOfStatistics.value = res.statistics;
    }

    return {
      success: res.success,
      message: res.message,
    };
  };

  /**
   * 点击改进建议
   */
  clickBusinessSuggest = (item: Optimization) => {
    logger.log('clickBusinessSuggest', 'invoke', item);

    // 若是url类型的优化建议，直接打开链接
    if (item.button_url) {
      window.open(item.button_url, '_blank');
      return;
    }

    // 若是内容类型的优化建议，弹窗提示
    const dialogInstance = DialogPlugin({
      body: item.empty_url_content,
      cancelBtn: null,
      confirmBtn: '知道了',
      onConfirm: () => {
        dialogInstance.destroy();
      },
    });
  };

  static getInstance() {
    if (!BusinessOverviewModel.instance) {
      BusinessOverviewModel.instance = new BusinessOverviewModel();
    }
    return BusinessOverviewModel.instance;
  }

  static destroyInstance() {
    BusinessOverviewModel.instance = null;
  }
}
