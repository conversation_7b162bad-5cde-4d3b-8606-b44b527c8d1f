import { ref } from 'vue';

import Logger from '@/helpers/utils/logger';

const logger = new Logger('home-model');

export default class HomeModel {
  // 页面模型单例
  private static instance: HomeModel | null = null;

  sideBarNavItemsRef = ref([
    {
      name: '名片管理',
      icon: `<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="11" cy="11" r="10" fill="currentColor"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M15.6923 5.64488L10.0588 7.54186C8.86564 7.94365 7.93125 8.88377 7.53677 10.0794L5.67428 15.7244C5.54518 16.1156 5.91819 16.4864 6.30867 16.3549L11.9421 14.4579C13.1353 14.0561 14.0697 13.116 14.4642 11.9203L16.3267 6.2754C16.4558 5.88412 16.0828 5.51339 15.6923 5.64488ZM10.4888 12.9333C11.5566 13.2159 12.6513 12.5794 12.9339 11.5116C13.2165 10.4438 12.58 9.34909 11.5122 9.06647C10.4444 8.78385 9.34968 9.42037 9.06706 10.4882C8.78444 11.556 9.42096 12.6507 10.4888 12.9333Z" fill="white"/>
</svg>
`,
      isExpand: true,
      active: false,
      children: [
        { name: '经营概览', href: '/home/<USER>', active: true },
        { name: '用户洞察', href: '/home/<USER>', active: false },
      ],
    },
    {
      name: '商家名片',
      icon: `<svg width="18" height="22" viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.4995 0.917603C15.7651 0.917603 16.7913 1.94309 16.7915 3.20862V19.2506H17.7085V21.0836H0.291504V19.2506H1.2085V3.20862C1.20867 1.94319 2.23408 0.917768 3.49951 0.917603H14.4995ZM3.49951 2.75061C3.24665 2.75078 3.04168 2.95576 3.0415 3.20862V15.1559C3.0415 15.1559 5.33302 16.5005 8.99951 16.5006C12.6662 16.5006 14.9585 15.1559 14.9585 15.1559V3.20862C14.9583 2.95565 14.7525 2.75061 14.4995 2.75061H3.49951ZM13.3149 7.52405L8.0835 12.7555L4.68506 9.35706L5.98096 8.06116L8.0835 10.1627L12.0181 6.22717L13.3149 7.52405Z" fill="#07C160"/>
</svg>`,
      isExpand: true,
      active: false,
      children: [
        {
          name: '名片管理',
          href: '/home/<USER>/card-home',
          active: true,
          children: [
            { name: '发布名片', href: '/home/<USER>/card-publish', active: true },
            { name: '配置名片服务', href: '/home/<USER>/common-publish', active: true },
            { name: '配置基础信息', href: '/home/<USER>/card-config', active: true },
            { name: '基础信息', href: '/home/<USER>/card-view', active: true },
            { name: '品牌会员或品牌优惠', href: '/home/<USER>/brand-vip', active: true },
            { name: '名片配置', href: '/home/<USER>/card-detail', active: true },
            { name: '商家名片', href: '/home/<USER>/dashboard', active: true },
          ],
        },
        { name: '交易连接名片', href: '/home/<USER>/pay-link-card', active: false },
      ],
    },
    {
      name: '营销活动',
      icon: `<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M8.30908 1.54175C7.59067 1.3064 6.9335 1.09111 6.11019 1.15277C3.51485 1.34713 1.75162 3.42411 1.55756 6.02351C1.31415 9.28405 1.31415 12.5583 1.55756 15.8188C1.75162 18.4179 3.51485 20.4952 6.11019 20.6892C6.90676 20.7489 7.58161 20.5875 8.32493 20.4097C9.07968 20.2291 9.90502 20.0317 11 20.0317C12.0882 20.0317 12.8989 20.2275 13.6443 20.4075C14.3876 20.5871 15.066 20.7509 15.8898 20.6892C18.4851 20.4952 20.2484 18.4179 20.4424 15.8188C20.6859 12.5583 20.6859 9.28405 20.4424 6.02351C20.2484 3.42411 18.4851 1.34713 15.8898 1.15277C15.1277 1.0957 14.4795 1.30384 13.7622 1.53419C12.9952 1.7805 12.149 2.0522 11 2.0522C9.86721 2.0522 9.05512 1.78616 8.30908 1.54175ZM13.7776 6.53652C14.2431 6.071 14.9978 6.071 15.4634 6.53652C15.9123 6.98542 15.9283 7.70326 15.5115 8.17136L15.4634 8.22232L8.22244 15.4632C7.75692 15.9288 7.00216 15.9288 6.53664 15.4632C6.08775 15.0144 6.07171 14.2965 6.48854 13.8284L6.53664 13.7774L13.7776 6.53652ZM14.2656 12.7186C13.4113 12.7186 12.7188 13.4112 12.7188 14.2655C12.7188 15.1198 13.4113 15.8124 14.2656 15.8124C15.1199 15.8124 15.8125 15.1198 15.8125 14.2655C15.8125 13.4112 15.1199 12.7186 14.2656 12.7186ZM6.1875 7.73426C6.1875 6.87994 6.88006 6.18738 7.73438 6.18738C8.58869 6.18738 9.28125 6.87994 9.28125 7.73426C9.28125 8.58857 8.58869 9.28113 7.73438 9.28113C6.88006 9.28113 6.1875 8.58857 6.1875 7.73426Z" fill="currentColor"/>
</svg> 
      `,
      isExpand: true,
      active: false,
      children: [{ name: '摇一摇有优惠', href: '/home/<USER>', active: false }],
    },
  ]);
  siteBarFooterItemsRef = ref([
    {
      name: '平台通知',
      icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M3.7491 6.44533C2.72458 6.68499 2 7.59861 2 8.6508V11.1693C2 12.2215 2.72458 13.1351 3.7491 13.3747L18.2191 16.7595C19.6401 17.0919 21 16.0134 21 14.5541V5.266C21 3.80663 19.6401 2.72814 18.2191 3.06054L3.7491 6.44533ZM11 17.0528L8 16.41V19.91C8 20.7385 8.67157 21.41 9.5 21.41C10.3284 21.41 11 20.7385 11 19.91V17.0528Z" fill="currentColor"/>
          </svg>`,
      active: false,
      badge: { text: '142', bgColor: '#efefef', textColor: '#0000004D' },
    },
    {
      name: '站内信息',
      icon: `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
         <path fill-rule="evenodd" clip-rule="evenodd" d="M3.17691 16.2557C3.4548 16.733 3.94111 17.0057 4.4969 17.0057H19.5031C20.0589 17.0057 20.6147 16.733 20.8231 16.2557C21.101 15.7785 21.0315 15.233 20.7536 14.7557L19.8608 13.5967C19.5574 13.1499 19.2613 12.7671 18.9869 12.4123C18.1305 11.305 17.4851 10.4705 17.4851 8.81818C17.4851 6.29545 16.4463 3.97727 14.0147 3.15909C13.5979 2.40909 12.8337 2 12 2C11.1663 2 10.4021 2.40909 9.98528 3.15909C7.55372 3.97727 6.62353 6.29545 6.62353 8.81818C6.62353 10.4198 5.95334 11.253 5.10224 12.3111C4.79393 12.6943 4.46188 13.1071 4.12935 13.5967L3.24638 14.7557C2.96849 15.233 2.89901 15.7785 3.17691 16.2557ZM12 22C10.1362 22 8.57006 20.7252 8.12602 19H15.874C15.4299 20.7252 13.8638 22 12 22Z" fill="currentColor"/>
        </svg>
      `,
      active: false,
      badge: { text: '142', bgColor: '#FF3D4A', textColor: 'white' },
    },
    {
      name: '账号信息',
      active: false,
      icon: `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clip-rule="evenodd" d="M7.0001 7.64347C7.0001 4.1355 9.2391 2 12.0001 2C14.7611 2 17.0001 4.1355 17.0001 7.64347C17.0001 11.1514 14.7611 15 12.0001 15C9.2391 15 7.0001 11.1514 7.0001 7.64347ZM2 20.995C2 18.7886 7.14444 17 12 17C16.8556 17 22 18.7886 22 20.995C22 21.55 21.3391 22 20.5245 22H3.47545C2.66091 22 2 21.55 2 20.995Z" fill="currentColor" />
     </svg>`,
      children: [{ name: '个人信息' }, { name: '切换账号' }, { name: '退出' }],
    },
  ]);
  copyLinkListRef = ref([
    { href: 'https://www.tencent.com/zh-cn/index.shtml', name: '关于腾讯' },
    { href: 'https://kf.qq.com/faq/181012y6bUNR181012nMFnMr.html', name: '关于微信支付' },
    {
      href: 'https://pay.weixin.qq.com/index.php/core/home/<USER>',
      name: '平台使用协议',
    },
    {
      href: 'https://pay.weixin.qq.com/index.php/public/apply_sign/protocol_v2',
      name: '支付服务协议',
    },
  ]);

  /**
   * 更新侧边栏激活项
   */
  updateSideBarActiveItem(path: string) {
    logger.info('updateSideBarActiveItem', path);

    this.sideBarNavItemsRef.value = this.sideBarNavItemsRef.value.map((menu) => ({
      ...menu,
      children: menu.children.map((submenu) => ({
        ...submenu,
        active: submenu.href.replace('#', '') === path,
      })),
    }));
  }

  static getInstance() {
    if (!HomeModel.instance) {
      HomeModel.instance = new HomeModel();
    }
    return HomeModel.instance;
  }

  static destroyInstance() {
    HomeModel.instance = null;
  }
}
