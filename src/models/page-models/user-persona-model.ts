import {
  TV1GetCompassUserPersonaDetailResponseParams,
  UserPersonaDetailCategory,
} from '@/helpers/types/models';
import Logger from '@/helpers/utils/logger';
import { compassUserService } from '@/services/compass-user-service';
import BasePersonaModel from '../common-models/base-persona-model';
import { getDefaultEarliestDate, getDefaultEndDate } from '@/helpers/utils/date';
import { LocationQuery } from 'vue-router';
import { MessagePlugin } from 'tdesign-vue-next';
import { ref } from 'vue';

const logger = new Logger('UserPersonaModel');

export default class UserPersonaModel extends BasePersonaModel {
  private static instance: UserPersonaModel | null = null;

  static getInstance() {
    if (!UserPersonaModel.instance) {
      UserPersonaModel.instance = new UserPersonaModel();
    }
    return UserPersonaModel.instance;
  }

  static destroyInstance() {
    UserPersonaModel.instance = null;
  }
  /** 是否显示图表中的具体数值 */
  showDetailNum = ref(true);
  /** 生效中的规则版本号 */
  activeRuleVersion = ref(0);
  /** 生效中的品牌版本号 */
  activeBrandVersion = ref(0);

  // ========= 页面打开时传入的参数 =========
  dateFilter = ref({
    /** 查询的结束日期 */
    getDateEnd: getDefaultEndDate(),
  });
  /** 生效中的罗盘规则 - 格式化后的数据 */
  activeCompassRulePeriodFormated = ref({
    before: getDefaultEarliestDate(),
    after: getDefaultEndDate(),
  });

  /**
   * 初始化页面参数
   * @param query useRoute().query
   * query.activeBrandVersion {require} 版本号
   * query.baseDate {optional} 基准日期，该天对应的数据报表没出；不传的话默认为当天
   * query.disableDateBefore {optional} 可以选择的最早的结束时间；不传的话默认为很早之前的一天
   * query.disableDateAfter {optional} 可以选择的最晚的结束时间；不传的话默认为当天
   */
  initPageParams(query: LocationQuery) {
    logger.log('initPageParams invoke', query);

    if (!Number.isInteger(Number(query.activeRuleVersion))) {
      logger.error('activeRuleVersion is not a number');
      MessagePlugin.error('参数错误');
      throw new Error('activeRuleVersion is required');
    }
    this.activeRuleVersion.value = Number(query.activeRuleVersion);

    if (!Number.isInteger(Number(query.activeBrandVersion))) {
      logger.error('activeBrandVersion is not a number');
      MessagePlugin.error('参数错误');
      throw new Error('activeBrandVersion is required');
    }
    this.activeBrandVersion.value = Number(query.activeBrandVersion);

    if (typeof query.getDateEnd === 'string') {
      this.dateFilter.value.getDateEnd = query.getDateEnd;
    }
    if (typeof query.disableDateBefore === 'string') {
      this.activeCompassRulePeriodFormated.value.before = query.disableDateBefore;
    }
    if (typeof query.disableDateAfter === 'string') {
      this.activeCompassRulePeriodFormated.value.after = query.disableDateAfter;
    }
  }

  /**
   * 从服务器获取用户画像详情
   * 需要被子类实现
   */
  async getPersonaDetailFromServer(): Promise<UserPersonaDetailCategory[]> {
    logger.log('getPersonaDetailFromServer invoke');

    let res: TV1GetCompassUserPersonaDetailResponseParams['data'];
    try {
      res = await compassUserService.getUserPersonaDetail({
        get_date_end: this.dateFilter.value.getDateEnd,
        active_rule_version: String(this.activeRuleVersion.value),
        active_brand_version: String(this.activeBrandVersion.value),
      });
    } catch (e) {
      logger.error('getUserInsightPersonaDetail error', e);
      throw new Error('getUserInsightPersonaDetail error');
    }

    const personaDetailCategory =
      res?.user_persona_detail?.user_persona_detail_category_list;
    if (!Array.isArray(personaDetailCategory)) {
      throw new Error('user_persona_detail_category_list is not an array');
    }

    return personaDetailCategory!;
  }

  /**
   * 修改周期结束日期
   */
  async updateActivityOverview(getDateEnd: string) {
    logger.log('updateActivityOverview', getDateEnd);
    if (!this.pageInited) {
      logger.log('updateActivityOverview', 'page not inited');
      return;
    }

    this.dateFilter.value.getDateEnd = getDateEnd;

    this.initPersonaDetail();
  }
}
