/* Main container styles */
.main-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Card container styles */
.card-container {
  border-radius: 16px;
  background-color: #FFFFFF;
  padding-right: 24px;
}

/* Section header styles */
.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

/* Status dot styles */
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 10px;
}
.status-dot.active {
  background-color: #07C160;
}
.status-dot.inactive {
  background-color: #999999;
}

/* Section title styles */
.section-title {
  font-size: 18px;
  font-weight: 500;
  margin: 0;
  color: #000000;
}

/* Config item styles */
.config-item {
  background-color: #F7F8FA;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 4px;
}

/* Config header styles */
.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Config title wrapper styles */
.config-title-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

/* Config title styles */
.config-title {
  font-size: 16px;
  font-weight: 500;
}

/* Status badge styles */
.status-badge {
  display: inline-block;
  padding: 0 8px;
  border-radius: 2px;
  font-size: 12px;
  margin-left: 8px;
  height: 20px;
  line-height: 20px;
}
.status-badge.active {
  background-color: #E8F6EF;
  color: #07C160;
}
.status-badge.inactive {
  background-color: #F1F2F5;
  color: #909399;
}

/* Config description styles */
.config-description {
  margin: 0;
  color: #666;
  line-height: 22px;
  font-size: 14px;
}
