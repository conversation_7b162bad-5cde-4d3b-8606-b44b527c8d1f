@tailwind base;
@tailwind components;
@tailwind utilities;

html,
body,
#app-root {
  height: 100%;
  font-size: 16px;
}
.home-container {
  width: 1224px;
  min-width: 960px;
}
.t-form__item {
  --td-comp-margin-xxl: 32px;
}
.custom-checkbox-group {
  .t-checkbox__input {
    width: 20px;
    height: 20px;
  }
  .t-checkbox__label {
    font-size: 16px;
  }
  .t-checkbox.t-is-checked .t-checkbox__input::after {
    top: 9px;
    left: 5px;
  }
}

.custom-insight-table.t-table {
  thead td,
  th {
    padding-top: 16px;
    padding-bottom: 16px;
    color: var(--font-color-dark);
    font-size: 16px;
    font-weight: 500;
  }
  th,
  td {
    border-bottom-color: var(--bg-color-black-3);
  }
  td {
    height: 88px;
  }
  .t-table__filter-icon {
    color: var(--font-color-light);
  }
  .t-table__sort-icon {
    color: #d9d9d9;
  }
  .t-table-sort-asc {
    margin-bottom: 6px;
  }
  .t-table__sort-icon--active {
    color: var(--primary-color);
  }
}

.custom-insight-table.t-table--hoverable {
  tbody tr:hover {
    background-color: var(--bg-color-black-2);
  }
  tbody tr:hover td:first-child {
    border-radius: 8px 0 0 8px;
  }
  tbody tr:hover td:last-child {
    border-radius: 0 8px 8px 0;
  }
}

.t-popup__content.insight-custom-table-filter-popup-content {
  border-radius: 16px;
  overflow: hidden;
  .t-table__filter-pop-content-inner {
    padding: 0 !important;
  }
}

.t-popup__content.custom-dropdown-filter-popup-content,
.t-popup__content.custom-dropdown-filter-popup-content .t-dropdown__submenu {
  border-radius: 16px;
  background-color: var(--bg-color-black-6);
  border: 1px solid var(--bg-color-black-4);
}
.t-popup__content {
  width: 336px !important;
  background-color: #000000e5;
}
.t-select__dropdown-inner {
  min-height: 60px !important;
}
.t-popup__content .t-select__loading-tips {
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8d8d8d;
  background-color: #000000e5;
}
.t-popup__content .t-select__empty {
  border-radius: 16px;
  height: 60px;
  display: flex;
  align-items: center;
  min-height: 60px;
  justify-content: center;
  background-color: #000000e5;
}

.highlight-keyword {
  color: #00c16e;
  font-weight: bold;
}
.t-popup__content.custom-dropdown-filter-popup-content {
  .t-dropdown__menu {
    padding: 12px;
    gap: 8px;
  }
  .t-dropdown__submenu ul {
    gap: 8px;
  }
  .t-dropdown__item {
    padding: 10px 16px;
    color: #fff;
    font-size: 16px;
    cursor: pointer;
    border-radius: 8px;
    transition: background-color 0.2s cubic-bezier(0.38, 0, 0.24, 1);
  }
  .t-dropdown__item:hover {
    background-color: #fff3;
  }
  .t-dropdown__submenu-wrapper--right {
    left: calc(100% - 12px);
  }
  .t-dropdown__item--active {
    font-weight: 500;
    background-color: var(--primary-color);
  }
}

.t-upload__card-name {
  width: 110px;
}

.t-cascader__popup {
  .t-popup__content {
    margin-top: 4px !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    border: 0 !important;
  }
}

.t-cascader__panel {
  align-items: flex-start;
  height: 216px !important;
  .t-cascader__menu {
    width: 336px;
    max-height: 216px;
    box-sizing: border-box;
    margin-right: 4px;
    padding: 4px 12px 12px;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.04);
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(2px);
  }
  .t-cascader__item {
    color: #fff;
  }
  .t-cascader__item.t-size-l {
    height: 42px;
    padding: 0 16px;
    margin-top: 8px;
  }
  .t-cascader__item-icon.t-icon {
    color: rgba(255, 255, 255, 0.3);
  }
  .t-cascader__item.t-is-expanded {
    border-radius: 8px;
    background: rgba(7, 193, 96, 0.12);
  }
  .t-cascader__item.t-is-selected {
    font-weight: 500;
    background: rgba(7, 193, 96, 0.12);
  }
  .t-cascader__item:hover:not(.t-is-expanded):not(.t-is-disabled) {
    background: rgba(7, 193, 96, 0.12);
  }
}

.breadcrumb {
  margin-bottom: 16px;
}

.breadcrumb-item {
  cursor: pointer;
  color: #000;
  font-size: 24px;
  opacity: 0.3;
}

.breadcrumb-separator {
  margin: 0 8px;
  color: #999;
}

.breadcrumb-item.active {
  color: #000;
  font-weight: 500;
  opacity: 1;
  font-size: 24px;
}
.form-field-container {
  width: 100%;
}

.brand-logo {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.t-popconfirm__content .t-button {
  width: 96px !important;
  height: 40px !important;
  border-radius: 8px;
  font-size: 16px;
}