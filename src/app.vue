<template>
  <RouterView :key="`${route.path}?${sortedQueryString}`" />
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();

// 排序查询参数确保一致性
const sortedQueryString = computed(() => {
  const params = new URLSearchParams(route.query);
  return params.toString();
});
</script>
<style lang="less">
.custom-popconfirm-container {
  width: 320px !important;

  .t-popconfirm__content {
    padding: 0px;
  }

  .t-icon {
    display: none;
  }

  .t-popup__content {
    background: #000000e5;
    padding: 24px;
    border-radius: 12px;
  }

  .t-popconfirm__inner {
    color: #fff;
  }

  .t-popup__arrow::before {
    background: #000000e5;
  }

  .t-popconfirm__buttons {
    margin-top: 24px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }

  .t-button {
    width: 96px;
    height: 40px;
    line-height: 40px;
  }

  .t-popconfirm__cancel {
    background: #ffffff0a;
    color: #ffffff;
  }
}
</style>