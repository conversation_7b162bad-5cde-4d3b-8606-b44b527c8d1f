<template>
  <div class="thumbnail-container">
    <template v-for="(image, index) in props.images" :key="index">
      <t-image v-if="image && typeof image === 'string'" :src="image" class="thumbnail" :style="thumbnailStyle" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const props = withDefaults(
  defineProps<{
    /**
     * Array of image URLs
     */
    images: string[];
    /**
     * Size of each thumbnail in pixels
     * @default 80
     */
    thumbnailSize?: number;
    /**
     * Image fit mode
     * @default 'cover'
     */
    fit?: 'fill' | 'contain' | 'cover' | 'none' | 'scale-down';
    /**
     * Whether to enable lazy loading
     * @default true
     */
    lazy?: boolean;
  }>(),
  {
    thumbnailSize: 80,
    fit: 'cover',
    lazy: true,
  },
);

const emit = defineEmits<
  /**
   * Triggered when thumbnail is clicked
   * @param imageUrl - The clicked image URL
   */
  (e: 'image-click', imageUrl: string) => void
>();

// Computed style for thumbnails
const thumbnailStyle = computed(() => ({
  width: `${props.thumbnailSize}px`,
  height: `${props.thumbnailSize}px`,
}));
</script>

<style scoped>
.thumbnail-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.thumbnail {
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s;
}

.thumbnail:hover {
  transform: scale(1.05);
}
</style>
