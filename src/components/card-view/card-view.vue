<template>
    <div class="card-view-container">
        <div class="title-bar">
            <div class="title">基础信息</div>
            <div class="action-btn" :class="{ 'is-active': canModify }" @click="jumpModify">修改</div>
        </div>
        <service-status-alert v-if="serviceStatus" class="mb-[24px]" type="configView" :status="serviceStatus"  :show="true"/>
        <div class="brand-info">
            <img class="logo" :src="brandInfo?.brandLogo" alt="" srcset="">
            <div class="brand-name">{{ brandInfo?.brandName || '-' }}</div>
        </div>
        <div class="info-list" v-if="customerServiceList.length > 0">
            <div class="label">商家客服</div>
            <div class="list-detail">
                <div class="info-list-item" v-for="(service, index) in customerServiceList" :key="index">
                    <div class="field-info" v-if="service?.customerServiceType === CustomerServiceType.PHONE">
                        <div class="label">客服电话</div>
                        <div class="detail">
                            <div class="detail-item">
                                <div class="detail-label">电话号码</div>
                                <div class="detail-value">{{ service.servicePhone }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="field-info" v-else-if="service?.customerServiceType !== CustomerServiceType.NONE">
                        <div class="label">在线客服</div>
                        <div class="detail">
                            <div class="detail-item">
                                <div class="detail-label">客服类型</div>
                                <div class="detail-value">{{ getCustomerServiceTypeName(service.customerServiceType) }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">{{ getCustomerServicePathLabel(service.customerServiceType) }}</div>
                                <div class="detail-value">{{ getCustomerServicePath(service) }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="info-list" v-if="sceneList.length > 0">
            <div class="label">场景跳转</div>
            <div class="list-detail">
                <div class="info-list-item" v-for="(scene, index) in sceneList" :key="index">
                    <div class="field-info" v-if="scene.sceneType === SceneType.MP && scene.miniProgram">
                        <div class="label">小程序</div>
                        <div class="detail">
                            <div class="detail-item">
                                <div class="detail-label">跳转小程序</div>
                                <div class="detail-value">{{ scene.miniProgram.appid }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">场景标签</div>
                                <div class="detail-value">{{ scene.miniProgram.sceneTag }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">场景展示图片</div>
                                <div class="detail-value">
                                    <t-image-viewer :images="scene.miniProgram.imageList">
                                        <template #trigger="{ open }">
                                            <div class="w-full img-list">
                                                <div
                                                    v-for="(img, index) in scene.miniProgram.imageList"
                                                    :key="index"
                                                    class="img-item-wrapper"
                                                    @click="() => open(index)"
                                                >
                                                    <img
                                                        :src="img"
                                                        alt="场景图片"
                                                        class="img-item"
                                                    />
                                                    <div class="text-white img-preview-overlay">
                                                        预览
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </t-image-viewer>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="field-info" v-if="scene.sceneType === SceneType.FINDER && scene.finder">
                        <div class="label">视频号</div>
                        <div class="detail">
                            <div class="detail-item">
                                <div class="detail-label">跳转视频号</div>
                                <div class="detail-value">{{ scene.finder.finderUsername }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">场景标签</div>
                                <div class="detail-value">{{ scene.finder.sceneTag }}</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-label">场景展示图片</div>
                                <div class="detail-value">
                                    <t-image-viewer :images="scene.finder.imageList">
                                        <template #trigger="{ open }">
                                            <div class="w-full img-list">
                                                <div
                                                    v-for="(img, index) in scene.finder.imageList"
                                                    :key="index"
                                                    class="img-item-wrapper"
                                                    @click="() => open(index)"
                                                >
                                                    <img
                                                        :src="img"
                                                        alt="场景图片"
                                                        class="img-item"
                                                    />
                                                    <div class="text-white img-preview-overlay">
                                                        预览
                                                    </div>
                                                </div>
                                            </div>
                                        </template>
                                    </t-image-viewer>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import ServiceStatusAlert from '../CardService/compontents/ServiceStatusAlert.vue';
import { usePageData, SceneType, CustomerServiceType } from './usePageData';
import { useRouter } from 'vue-router';

const {
  serviceStatus,
  customerServiceList,
  sceneList,
  brandInfo,
  canModify,
  getCustomerServiceTypeName,
  getCustomerServicePath,
  getCustomerServicePathLabel
} = usePageData();

const router = useRouter();
const jumpModify = () => {
  if (canModify.value) {
    router.push('/home/<USER>/card-config');
  }
};

</script>
<style scoped lang="less">
.card-view-container {
    .title-bar {
        display: flex;
        justify-content: space-between;

        .title {
            font-size: 20px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.9);
        }

        .action-btn {
            width: 112px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            padding: 9px 24px;
            user-select: none;
            cursor: not-allowed;
            background-color: rgba(0, 0, 0, 0.04);
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.12);
            margin-bottom: 24px;
            &.is-active {
                cursor: pointer;
                color: rgba(0, 0, 0, 0.9)
            }
        }
    }

    .brand-info {
        width: 100%;
        height: 96px;
        padding: 24px;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.02);
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 24px;

        .logo {
            width: 48px;
            height: 48px;
            border-radius: 48px;
            background-color: antiquewhite;
        }

        .brand-name {
            font-size: 500;
            font-size: 20px;
            color: rgba(0, 0, 0, 0.9);
        }
    }

    .info-list {
        display: flex;
        gap: 32px;

        .label {
            width: 128px;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.5);
        }

        .list-detail {
            flex: 1;
        }

        .field-info {
            margin-bottom: 24px;

            .label {
                font-size: 16px;
                color: rgba(0, 0, 0, 0.9);
                margin-bottom: 16px;
            }

            .detail {
                width: 100%;
                padding: 24px;
                border-radius: 8px;
                background-color: rgba(0, 0, 0, 0.02);

                .detail-item {
                    display: flex;

                    &:not(:last-child) {
                        margin-bottom: 24px;
                    }

                    .detail-label {
                        width: 128px;
                        font-size: 16px;
                        color: rgba(0, 0, 0, 0.5);
                    }

                    .detail-value {
                        font-size: 16px;
                        color: rgba(0, 0, 0, 0.9);
                        flex: 1;
                        width: 520px;
                        word-wrap: break-word;
                    }
                }
            }
        }
        .img-list {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;

            .img-item-wrapper {
                position: relative;
                display: inline-block;
                cursor: pointer;

                .img-item {
                    width: 80px;
                    height: 80px;
                    border-radius: 8px;
                    object-fit: cover;
                    transition: all 0.3s ease;
                }

                .img-preview-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(0, 0, 0, 0.5);
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    transition: all 0.3s ease;

                    .preview-icon {
                        color: white;
                        font-size: 24px;
                    }
                }

                &:hover {
                    .img-item {
                        transform: scale(1.05);
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    }

                    .img-preview-overlay {
                        opacity: 1;
                    }
                }
            }
        }
    }
}
</style>
