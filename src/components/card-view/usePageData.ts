import { computed, ref } from 'vue';
import { request } from '@/utils/request'
import { getIsFirst } from '@/pages/card-mna/common/model/tutorial'
import { MessagePlugin } from 'tdesign-vue-next';
import { ProcessState } from '@/pages/card-mna/common/model/tutorial'
export const SceneType = {
  NONE: 0, // 不使用
  MP: 1, // 小程序
  FINDER: 2, // 视频号
} as const;

export const CustomerServiceType = {
  NONE: 0, // 不使用
  MINI_PROGRAM: 1, // 小程序客服
  WECOM: 2, // 企业微信
  CUSTOMIZE_WEB: 3, // 自定义网页
  CUSTOMIZE_MP: 4, // 自定义小程序
  PHONE: 5, // 电话客服
} as const;

// 小程序客服配置
export interface MiniProgramService {
  path: string;
}

// 企业微信客服配置
export interface WecomService {
  path: string;
}

// 自定义网页客服配置
export interface CustomizeWebService {
  path: string;
}

// 自定义小程序客服配置
export interface CustomizeMpService {
  path: string;
}

// 客服配置
export interface CustomerService {
  customerServiceType: number;
  miniProgram?: MiniProgramService;
  wecom?: WecomService;
  customizeWeb?: CustomizeWebService;
  customizeMp?: CustomizeMpService;
  servicePhone?: string;
}

// 小程序场景配置
export interface MiniProgramScene {
  appid: string;
  sceneTag: string;
  imageList: string[];
}

// 视频号场景配置
export interface FinderScene {
  finderUsername: string;
  sceneTag: string;
  imageList: string[];
}

// 场景配置
export interface Scene {
  sceneType: 0 | 1 | 2; // 0-不使用 1-小程序 2-视频号
  miniProgram?: MiniProgramScene;
  finder?: FinderScene;
  sceneId: string;
}

// 名片信息
export interface CardInfo {
  customerServiceList: CustomerService[];
  sceneList: Scene[];
  cardBrief: string;
}

// 接口返回数据结构
export interface CardInfoResponse {
  code: number;
  message: string;
  data: {
    cardInfo: CardInfo;
  };
}

export interface BrandInfo {
    brandLogo: string,
    brandAlias: string,
    brandShowType: number,
    brandId: string,
    brandName: string,
};

// 客服类型配置映射 - 用于降低圈复杂度
interface CustomerServiceConfig {
  typeName: string;
  pathLabel: string;
  pathExtractor: (service: CustomerService) => string;
}

// 客服配置映射表 - 关注点分离：将所有客服类型相关配置集中管理
const customerServiceConfigMap: Record<number, CustomerServiceConfig> = {
  [CustomerServiceType.MINI_PROGRAM]: {
    typeName: '小程序客服',
    pathLabel: '跳转路径',
    pathExtractor: (service) => service.miniProgram?.path || '-'
  },
  [CustomerServiceType.WECOM]: {
    typeName: '企业微信',
    pathLabel: '企业微信路径',
    pathExtractor: (service) => service.wecom?.path || '-'
  },
  [CustomerServiceType.CUSTOMIZE_WEB]: {
    typeName: '自定义网页',
    pathLabel: '网页路径',
    pathExtractor: (service) => service.customizeWeb?.path || '-'
  },
  [CustomerServiceType.CUSTOMIZE_MP]: {
    typeName: '自定义小程序',
    pathLabel: '小程序路径',
    pathExtractor: (service) => service.customizeMp?.path || '-'
  },
  [CustomerServiceType.PHONE]: {
    typeName: '电话客服',
    pathLabel: '电话号码',
    pathExtractor: (service) => service.servicePhone || '-'
  }
};

export enum PublishState {
  FINISHED = 8,
};

export const usePageData = () => {

  const serviceStatus = ref<any | undefined>();
  const isfirstRef = ref();
  const canModify = computed(() => {
    // 有进行中的单的时候都不能点，从bar的链接进去
    if (isfirstRef.value === false) {
      if (typeof serviceStatus.value === 'undefined') {
        return true
      }
      return [
        ProcessState.INIT,
        ProcessState.CANCELED,
        ProcessState.PUBLISHED
      ].includes(serviceStatus.value?.state)
    }
    // return !(typeof serviceStatus.value === 'undefined' || 'state' in serviceStatus.value)
    return [
      ProcessState.INIT,
      ProcessState.CANCELED,
      ProcessState.PUBLISHED
    ].includes(serviceStatus.value?.state)
  });

  const getServiceStatus = async () => {
    try {
        const res = await request.get({
            url: '/get-latest-card-info-publish-process',
        });
        serviceStatus.value = res?.data?.process;
        const isfirt = await getIsFirst();
        isfirstRef.value = isfirt
    } catch (error) {
        console.error('getServiceStatus error', error);
    }
  };

  const customerServiceList = ref<CustomerService[]>([]);
  const sceneList = ref<Scene[]>([]);
  const cardBrief = ref<string>('');

  const getCardInfo = async () => {
    try {
        const res = await request.get({
            url: '/get-card-info',
        }) as CardInfoResponse | null;
        console.log('getCardInfo', res);
        if (res && res.code === 0 && res.data?.cardInfo) {
          customerServiceList.value = res.data.cardInfo.customerServiceList || [];
          sceneList.value = res.data.cardInfo.sceneList || [];
          cardBrief.value = res.data.cardInfo.cardBrief || '';
        }
    } catch (error) {
        console.error('getCardInfo error', error);
    }
  };

  const brandInfo = ref<BrandInfo | undefined>();
  const getBrandInfo = async () => {
    try {
        const res = await request.get({
            url: '/get-brand-info',
        });
        brandInfo.value = res?.data;
    } catch (error) {
        console.error('getBrandInfo error', error);
    }
  };

  getBrandInfo();
  getCardInfo();
  getServiceStatus();


  // 获取客服类型显示名称 - 降低圈复杂度的实现
  const getCustomerServiceTypeName = (type: number): string => {
    return customerServiceConfigMap[type]?.typeName || '未知类型';
  };

  // 获取客服跳转路径 - 降低圈复杂度的实现
  const getCustomerServicePath = (service: CustomerService): string => {
    const config = customerServiceConfigMap[service.customerServiceType];
    return config?.pathExtractor(service) || '-';
  };

  // 获取客服跳转路径的标签名称 - 降低圈复杂度的实现
  const getCustomerServicePathLabel = (type: number): string => {
    return customerServiceConfigMap[type]?.pathLabel || '跳转路径';
  };

  return {
    serviceStatus,
    customerServiceList,
    sceneList,
    cardBrief,
    brandInfo,
    canModify,
    getCustomerServiceTypeName,
    getCustomerServicePath,
    getCustomerServicePathLabel,
  };
};
