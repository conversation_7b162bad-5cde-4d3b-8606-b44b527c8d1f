export interface CardInfo {
  customerServiceList: [
    {
      customerServiceType: 1;
      miniProgram: {
        appid: string;
        username: string;
      };
      wecom: {
        path: string;
      };
      customizeWeb: {
        path: string;
      };
      customizeMp: {
        appid: string;
        path: string;
      };
      servicePhone: string;
    },
  ];
  sceneList: SceneListItem[];
  cardBrief: string;
}

export interface SceneListItem {
  sceneType: 1;
  miniProgram: {
    appid: string;
    sceneTag: string;
    imageList: string[];
  };
  finder: {
    finderUsername: string;
    sceneTag: string;
    imageList: string[];
  };
  sceneId: string;
}

// 0-不使用 1-待扫码 2-已扫码 3-已过期
export enum QRCodeStateEnum {
  NOT_USE = 0,
  WAITING_SCAN = 1,
  SCANNED = 2,
  EXPIRED = 3,
}

// 草稿预览码数据
export interface IPrevidewCodeData {
  previewId: string;
  previewCode: string;
  expireTime: string;
}

export interface IServiceConfig {
  serviceList: IServiceItem[];
}

export interface IServiceItem {
  serviceId: string;
  serviceName: string;
  serviceClassifyName: string;
  operationSlot?: {
    slotKey: string;
    slotTitle: string;
    slotDesc: string;
    imageList: string[];
  };
  jumpInfo: {
    jumpType: 1;
    miniProgram?: {
      appid: string;
      path: string;
    };
    web?: {
      path: string;
    };
  };
}

// 0-不使用 1-立即发布 2-定时发布
export enum IPublishType {
  NOT_USE = 0,
  IMMEDIATELY_PUBLISH = 1,
  SCHEDULED_PUBLISH = 2,
}

export interface ILatestServicePublishProcess {
  process: {
    processId: string;
    type: 1;
    primaryProcessId: string;
    businessCode: string;
    state: 1;
    stateVersion: string;
    property: string;
    content: {
      serviceList: IServiceItem[];
    };
    audit: {
      overallAuditResult: string;
    };
    idempotentId: string;
    submitAuditTime: string;
    auditPassTime: string;
    auditRejectTime: string;
    cancelTime: string;
    submitPublishTime: string;
    startPublishTime: string;
    finishTime: string;
    publishType: 1;
    scheduleTime: string;
    creator: string;
    modifier: string;
    createTime: string;
    modifyTime: string;
  };
}

export interface IBusinessCardPage {
  showQRCode: string;
  page: string;
  cardInfo: CardInfo;
  otherTab?:string;
  closeable?:boolean;
}

export type PublishState = 'cardInfo'|'serviceConfig';
