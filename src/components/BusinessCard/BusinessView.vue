<template>
  <div class="iphone-view w-[240px] h-[520px] flex flex-col">
    <div>
      <div class="top-status">
        <div />
      </div>
      <div class="pb-[14.77px]">
        <!-- 顶部栏 -->
        <div class="miniapp-header py-[8px] px-[16.62px] text-[12px] h-[28.9231px]">
          <span class="header-time text-[10.4615px]">{{ currentTime }}</span>
          <div class="header-icons">
            <img :src="MobileSignal" class="w-[11px] h-[7.4px]" alt="" />
            <img :src="Wifi" class="w-[10.5px] h-[7.3px]" alt="" />
            <img :src="StatusBar" class="w-[16.86px] h-[8px]" alt="" />
          </div>
        </div>
        <!-- 关闭按钮 -->
        <div class="close-btn h-[27.1px] px-[10px] flex items-center">
          <!-- <t-icon name="close" class="close-icon" size="22px" /> -->
          <img :src="Xmark" class="w-[14.77px] h-[14.77px]" alt="" />
        </div>
        <!-- 顶部信息 -->
        <div class="top-info-container">
          <div
            class="avatar-wrap flex w-[120px] gap-[7.38px] h-[39.3848px] justify-center items-center"
          >
            <t-avatar :image="brandInfo?.brandLogo" size="39.3848px" />
            <div class="top-info-main-left">
              <div class="top-title-row">
                <span class="main-title text-[12px]">{{ brandInfo?.brandName }}</span>
              </div>
            </div>
          </div>
          <div class="top-info-main w-[66px] h-[20px]">
            <t-button
              shape="round"
              variant="text"
              theme="default"
              size="small"
              class="like-btn p-[10px]"
            >
              <img :src="Heart" class="w-[9.85px] h-[9.85px]" alt="" />
              <span style="font-size: 10px">喜欢</span>
            </t-button>
            <t-button
              shape="round"
              variant="text"
              theme="default"
              size="small"
              class="like-btn"
            >
              <!-- <t-icon name="service" style="font-size: 10px" /> -->
              <img :src="CustomerSupport" class="w-[9.85px] h-[9.85px]" alt="" />
            </t-button>
          </div>
        </div>
        <!-- 卡片区 -->
        <div class="flex gap-[8px] px-[9.8px] justify-between pt-[18px]">
          <!-- 小程序 -->
          <div v-for="(card, i) in cards" class="card-item h-[36.92px]" :key="i">
            <div class="card-content">
              <div class="flex gap-[2.46px] mb-[2.46px]">
                <img
                  v-show="card?.sceneType == 1"
                  :src="MiniProgramCircle"
                  class="w-[12.31px] h-[12.31px]"
                  alt=""
                />
                <img
                  v-show="card?.sceneType == 2"
                  :src="Channels"
                  class="w-[12.31px] h-[12.31px]"
                  alt=""
                />
                <span class="mini-title">
                  {{ card?.sceneType == 1 ? '小程序' : '视频号' }}
                </span>
              </div>
              <div class="sub text-[6.15px]">
                {{ card?.desc }}
              </div>
            </div>
            <div class="card-image">
              <t-avatar
                v-for="(img, i) in card?.images"
                :key="i"
                class="card-image-item"
                shape="round"
                size="12.3px"
                :image="img"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- Tabs 和 我的交易 -->
      <div class="tabs-row bg-[#fff] z-10">
        <div
          class="h-[27.7692px] flex items-center w-full px-[9.85px] text-[9.2308px] gap-[14.77px] relative"
        >
          <div
            v-for="(tab, i) in tabs"
            :key="tab.key"
            :class="i > 0 ? 'text-[#808080]' : ''"
          >
            {{ tab.label }}
          </div>
          <div class="tab-item-active" />
        </div>
        <div class="tabs-icon" />
      </div>
    </div>
    <div class="bg-[#fff] flex-1 relative">
      <!-- 服务列表 -->
      <t-list
        class="service-list"
        style="height: 300px"
        :on-scroll="onScroll"
        @scroll="scrollHandler"
      >
        <div v-for="(service, i) in normalizedServiceData" :key="i">
          <t-list-item
            class="service-title"
            divider="false"
            v-show="service?.name !== '无分类'"
          >
            {{ service.name }}
          </t-list-item>
          <t-list-item
            class="service-item"
            divider="true"
            v-for="(s, j) in service?.services"
            :key="j"
          >
            <div class="service-main">
              <div class="service-main-left">
                <span class="service-name">{{ s?.originalService?.serviceName }}</span>
              </div>
              <div style="display: flex; align-items: center; gap: 6px">
                <span
                  v-if="
                    (s?.originalService?.operationSlot &&
                      s?.originalService?.operationSlot?.slotDes) ||
                    ''
                  "
                  class="service-desc"
                  >{{ s?.originalService?.operationSlot.slotDesc }}</span
                >
                <t-avatar
                  v-if="
                    s?.originalService?.operationSlot &&
                    s?.originalService?.operationSlot.imageList.length > 0
                  "
                  class="card-image-item"
                  shape="round"
                  size="14.7px"
                  :image="
                    s?.originalService?.operationSlot &&
                    s?.originalService?.operationSlot.imageList[0]
                  "
                />
              </div>
            </div>
            <t-icon name="chevron-right" class="service-arrow" />
          </t-list-item>
          <div
            v-show="
              normalizedServiceData?.length > 1 && i < normalizedServiceData?.length - 1
            "
            style="height: 4px; background-color: #f2f2f2"
          />
        </div>
      </t-list>
      <t-empty
        v-show="!normalizedServiceData?.length"
        title="商家暂未提供服务"
        :image="Empty"
        class="absolute left-[50%] top-[50%] empty-service text-[#D9D9D9]"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import Empty from '@/assets/images/empty.svg?url';
import Channels from '@/assets/images/channels.png?url';
import MiniProgramCircle from '@/assets/images/mini_program_circle.png';
import CustomerSupport from '@/assets/images/customer_support.png';
import MobileSignal from '@/assets/images/mobile_signal.png';
import Xmark from '@/assets/images/xmark.png';
import Heart from '@/assets/images/heart.png';
import Wifi from '@/assets/images/wifi.png';
import StatusBar from '@/assets/images/status_bar.png';
import { request } from '@/utils/request';

import {
  Avatar as TAvatar,
  Button as TButton,
  Icon as TIcon,
  List as TList,
  ListItem as TListItem,
  Tag as TTag,
  Tabs as TTabs,
  TabPanel as TTabPanel,
} from 'tdesign-vue-next';
import { computed, onMounted, ref } from 'vue';
import type { ServiceItem } from '../CardService/utils/type';
import type { CardInfo, IServiceItem } from './type';

const props = defineProps<{
  serviceList?: IServiceItem[];
  cardInfo?: CardInfo;
  otherTab?: string;
}>();
const brandInfo = ref({
  brandLogo: '',
  brandName: '',
});

const initData = async () => {
  getBrandInfo();
};

// 获取品牌信息
const getBrandInfo = async () => {
  try {
    const res = await request.get({
      url: '/get-brand-info',
    });
    brandInfo.value = res?.data;
  } catch {
    console.error('获取品牌信息失败');
  }
};

// 卡片区
const cards = computed(() => {
  const res: any[] = [];
  if (Array.isArray(props.cardInfo?.sceneList) && props.cardInfo.sceneList.length > 0) {
    props.cardInfo.sceneList.forEach((scene) => {
      if (scene?.sceneType == 1) {
        res.push({
          title: '小程序',
          sceneType: scene?.sceneType,
          desc: scene?.miniProgram?.sceneTag,
          images: scene?.miniProgram?.imageList,
        });
      } else if (scene.sceneType == 2) {
        res.push({
          title: '视频号',
          sceneType: scene?.sceneType,
          desc: scene?.finder?.sceneTag,
          images: scene?.finder?.imageList,
        });
      }
    });
  }
  res.sort((a, b) => a?.sceneType - b?.sceneType);
  return res;
});
// 用于保存分类的原始顺序
const categoryOrderMap = ref<Map<string, number>>(new Map());

// 修改normalizedServiceData计算属性，使其使用internalData而不是props.serviceData
const normalizedServiceData = computed(() => {
  if (!props?.serviceList?.length) {
    return []; // 默认不显示任何分类，包括"无分类"
  }

  // 从serviceList中提取不同的分类
  const classifyMap = new Map();

  // 确保所有分类都被添加到orderMap中
  props?.serviceList.forEach((service: ServiceItem) => {
    const classifyName = service?.serviceClassifyName || '无分类';

    // 如果这是一个新分类，添加到orderMap
    if (!categoryOrderMap.value.has(classifyName)) {
      categoryOrderMap.value.set(classifyName, categoryOrderMap.value.size);
    }

    if (!classifyMap.has(classifyName)) {
      classifyMap.set(classifyName, {
        name: classifyName,
        services: [],
      });
    }

    const category = classifyMap.get(classifyName);
    category.services.push({
      id: service.serviceId,
      name: service.serviceName,
      linkType: service.jumpInfo.jumpType === 1 ? '小程序' : '网页',
      link:
        service.jumpInfo.jumpType === 1
          ? service?.jumpInfo?.miniProgram?.path
          : service?.jumpInfo?.web?.path,
      originalService: service,
    });
  });

  // 获取"无分类"和其他分类
  const defaultCategory = classifyMap.get('无分类');
  classifyMap.delete('无分类');

  // 按照categoryOrderMap中的顺序排序其他分类
  const sortedCategories = Array.from(classifyMap.values()).sort((a, b) => {
    const orderA = categoryOrderMap.value.get(a.name) ?? Number.MAX_SAFE_INTEGER;
    const orderB = categoryOrderMap.value.get(b.name) ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });

  // 只有当"无分类"中有服务时，才将其添加到末尾
  if (defaultCategory && defaultCategory.services.length > 0) {
    sortedCategories.push(defaultCategory);
  }

  return sortedCategories;
});

// tabs
const tabs = computed(() => {
  const defaultTabs = [{ label: '服务', key: 'service', disabled: false }];
  if (props?.otherTab) {
    defaultTabs.push({ label: props.otherTab, key: 'other', disabled: true });
  }
  return defaultTabs;
});

function scrollHandler(event) {}
function onScroll(e) {}
const currentTime = computed(() => {
  const date = new Date();
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${hours}:${minutes}`;
});

onMounted(() => {
  initData();
});
</script>

<style scoped>
.tab-item-active {
  position: absolute;
  width: 17.2308px;
  height: 1.2308px;
  background-color: #000;
  border-radius: 8px;
  left: 10.34px;
  bottom: 0px;
}
.iphone-view {
  font-family: 'PingFang SC', 'Arial', 'Microsoft YaHei', sans-serif;
  background: #f2f2f2;
  border-radius: 16px;
  border: 1px solid #ccc;
  position: relative;
  overflow: hidden;
  padding-bottom: 20px;
  position: relative;
}
.top-status {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 20px;
  /* border-top: 1px solid #f0f0f0; */
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  z-index: 1000;
}
.top-status div {
  width: 82px;
  height: 3.15px;
  background: black;
  border-radius: 61px;
}
.miniapp-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #222;
}
.header-time {
  font-weight: 600;
}
.header-icons {
  display: flex;
  align-items: center;
  gap: 4px;
}
.header-icon {
  display: inline-block;
  background: #222;
  border-radius: 2px;
}
.header-signal {
  width: 12px;
  height: 10px;
  border-radius: 1px;
}
.header-wifi {
  width: 16px;
  height: 10px;
  background: none;
  border-bottom: 2px solid #222;
  border-radius: 0 0 8px 8px;
  margin-left: 2px;
}
.header-battery {
  width: 20px;
  height: 8px;
  background: none;
  border: 1.5px solid #222;
  border-radius: 2px;
  position: relative;
  margin-left: 2px;
}
.header-battery::after {
  content: '';
  position: absolute;
  right: -3px;
  top: 2.5px;
  width: 3px;
  height: 3px;
  background: #222;
  border-radius: 1.5px;
}
.close-btn {
  color: #222;
}
.top-info-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 9.8px 0 9.8px;
  gap: 16px;
}
.avatar-wrap {
  flex-shrink: 0;
}
.top-info-main {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2px;
  justify-content: flex-end;
}
.top-info-main-left {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}
.top-info-main-right {
  display: flex;
  /* flex-direction: column; */
  align-items: flex-end;
  /* gap: 8px; */
}
.top-title-row {
  display: flex;
  align-items: center;
  gap: 12px;
  justify-content: space-between;
}
.main-title {
  color: #222;
  line-height: 1.2;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.like-btn {
  background: #fff;
  border-radius: 20px;
  color: #222;
  align-items: center;
  border: 1px solid #eee;
  box-shadow: 0 1px 4px #f0f1f2;
  gap: 4px;
  cursor: pointer;
  vertical-align: middle;
}
:deep(.t-button__text) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}
.top-tags-row {
  display: flex;
  align-items: center;
  gap: 6px;
}
.top-tag {
  font-size: 13px;
  color: #8a9399;
  background: #f0f6ff;
  border-radius: 4px;
  padding: 2px 8px;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  gap: 2px;
}
.tag-avatar {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #eee;
}
.tag-text {
  margin-left: 2px;
  color: rgba(0, 0, 0, 0.5);
}
.tag-list {
  padding: 0;
}
.tag-list-item {
  margin-right: 4px;
}
.card-image .card-image-item:nth-child(2) {
  margin-left: -4px;
  border: 2px solid #fff;
  z-index: 2;
  border-radius: 2px;
}

.card-item {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  padding: 7.38px;
  min-width: 0;
  box-shadow: none;
  gap: 10px;
}
.card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}
.card-content .title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}
.card-content .sub {
  color: rgba(0, 0, 0, 0.5);
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.card-icon {
  flex-shrink: 0;
}
.mini-title {
  font-size: 9.23px;
  color: #222;
  line-height: 1.1;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.card-desc {
  color: #8a9399;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.tabs-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0;
  padding-right: 8px;
  background: #fff;
  position: relative;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  overflow: hidden;
}
.tabs-row .tabs-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}
.tabs {
  flex: 1;
  min-width: 0;
}
.my-order-btn {
  margin-left: 8px;
  font-size: 14px;
  padding: 0 18px;
  height: 40px;
  display: flex;
  align-items: center;
  background: #fff;
  color: #222;
  border: 1px solid #e6ecf3;
  border-radius: 20px;
  gap: 4px;
  cursor: pointer;
}
.my-order-btn svg {
  margin-right: 4px;
}
.service-list {
  background: #fff;
  /* border-radius: 0 0 16px 16px; */
  box-shadow: 0 2px 16px #f0f1f2;
  padding: 0;
}
.service-title {
  display: flex;
  padding: 6.15px 9.85px;
  align-items: center;
  align-self: stretch;
  color: #b3b3b3;
  font-size: 8.62px;
}
.service-item {
  font-size: 10.4px;
  color: #1a1a1a;
  padding: 9.85px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  min-height: 48px;
  background: #fff;
  justify-content: space-between;
}
.service-main {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  justify-content: space-between;
}
.service-main-left {
  display: flex;
  align-items: center;
  gap: 8px;
}
.service-name {
  color: #222;
}
.service-desc {
  font-size: 10.4px;
  color: #bbb;
  margin-left: 0;
}
.service-img {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  margin-left: 3px;
  object-fit: cover;
  background: #f4f7fa;
}
.service-arrow {
  color: #bbb;
  font-size: 18px;
}
.service-more {
  background: #f9f9f9;
  color: #bbb;
  font-size: 14px;
  padding: 12px 16px;
  border-bottom: none;
}
.service-more-text {
  color: #bbb;
}
.service-link {
  font-size: 15px;
  color: #222;
  padding: 0 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  min-height: 48px;
  background: #fff;
  justify-content: space-between;
}
.service-link:last-child {
  border-bottom: none;
}
.empty-service {
  transform: translate(-50%, -50%);
}
:deep(.t-empty) {
  background: transparent;
}
:deep(.t-empty__image) {
  background: transparent;
}
:deep(.t-tabs__nav-item.t-is-active) {
  color: #1a1a1a;
}
:deep(.t-tabs__bar) {
  background-color: #1a1a1a !important;
}
:deep(.t-tabs__nav-item-text-wrapper) {
  font-size: 15px;
}
:deep(.tag-list-item) {
  margin-right: 0px;
}
:deep(.t-tabs__nav-item-text-wrapper) {
  font-size: 9.23px;
}
:deep(.t-tabs__nav-item.t-size-m) {
  height: 27.8px;
}
:deep(.t-tabs__nav-item-wrapper) {
  margin: 0;
}
:deep(.t-tabs__bar.t-is-top) {
  height: 2.4px;
  width: 34px;
}
:deep(.t-avatar--round) {
  border-radius: 2px;
}
:deep(.t-empty__title) {
  color: #d9d9d9;
}
</style>
