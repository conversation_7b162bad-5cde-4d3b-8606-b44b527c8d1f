<template>
  <div
    :class="
      props?.data?.showQRCode === 'right' ? 'rounded-[16px] bg-[#fff] p-[32px]' : ''
    "
  >
    <div
      v-if="props?.data?.showQRCode === 'right'"
      class="publish-title text-[20px] font-[500] mb-[40px]"
    >
      请扫描二维码在手机端预览名片，确认无误后再操作发布
    </div>
    <div
      :class="
        props?.data?.showQRCode === 'right'
          ? 'flex items-center justify-center rounded-[16px] '
          : ''
      "
    >
      <div
        :class="
          previewState === 'ShowPreviewAndBar'
            ? 'p-[16px] shadow-[0px_0px_8px_0px_rgba(0,0,0,0.1)] rounded-[16px] relative'
            : ''
        "
      >
        <div
          v-if="previewState !== 'onlyPreview'"
          @click="close = true"
          class="w-[24px] h-[48px] rounded-l-[8px] absolute left-0 top-[50%] translate-x-[-24px] translate-y-[-24px] bg-[#fff] shadow-[-4px_0px_8px_0px_rgba(0,0,0,0.1)] flex justify-center items-center cursor-pointer"
        >
          <t-icon name="chevron-right"></t-icon>
        </div>
        <div
          v-if="previewState === 'closePreview'"
          @click="close = false"
          class="w-[106px] h-[48px] rounded-[8px] bg-[#fff] ml-[166px] mt-[270px] py-[14px] px-[8px] text-[14px] shadow-[0px_0px_8px_0px_rgba(0,0,0,0.1)] flex justify-center items-center cursor-pointer"
        >
          <t-icon name="chevron-right"></t-icon>
          <span>查看预览图</span>
        </div>
        <div v-show="previewState !== 'closePreview'">
          <RenderView
            :card-info="previewCardInfo || cardInfo"
            :other-tab="props?.data?.otherTab || otherTab"
            :service-list="serviceList"
          />
          <!-- <RenderLikeView v-else-if="index === 2" :data="serviceConfig" /> -->
          <div
            v-show="props.data?.showQRCode === 'pop'"
            class="text-[#4848DD] text-center my-[12px] cursor-pointer text-[14px] w-[240px] overflow-hidden"
            @click="openDialog"
          >
            手机扫码体验
          </div>
        </div>
      </div>
      <t-dialog
        v-if="props.data?.showQRCode === 'pop'"
        v-model:visible="visible"
        :cancel-btn="null"
        :confirm-btn="null"
        :on-close-btn-click="closeDialog"
        header="手机体验码"
        width="960px"
      >
        <PreviewQRCode
          title="手机扫码体验"
          :visible="visible"
          :query-q-r-code-state="queryQRCodeState"
          :QRState="QRState"
          :qr-code-data="qrCodeData"
          :generate-q-r-code="generateQRCode"
          :preview-src="previewSrc"
        />
      </t-dialog>
      <!-- <div class="goto-card">
                  <t-icon
                    @click="leftClickHandle"
                    name="chevron-left"
                    :class="{ 'goto-disabled': index === 1 }"
                  ></t-icon>
                  商家名片页 {{ index }}/2
                  <t-icon
                    @click="rightClickHandle"
                    name="chevron-right"
                    :class="{ 'goto-disabled': index === 2 }"
                  ></t-icon>
                </div> -->
      <div v-if="props?.data?.showQRCode === 'right'" class="ml-[64px]">
        <PreviewQRCode
          :visible="true"
          :query-q-r-code-state="queryQRCodeState"
          :QRState="QRState"
          :qr-code-data="qrCodeData"
          :generate-q-r-code="generateQRCode"
          :preview-src="previewSrc"
        />
      </div>
    </div>
    <div v-if="props.data?.page === 'common-publish'" class="flex pt-[50px]">
      <div class="w-[160px] text-[16px]">发布设置</div>
      <t-radio-group
        class="text-[16px]"
        :default-value="IPublishType.IMMEDIATELY_PUBLISH"
        @change="changeHandler"
      >
        <t-radio :value="IPublishType.IMMEDIATELY_PUBLISH"> 审核通过后自动发布 </t-radio>
        <t-radio :value="IPublishType.SCHEDULED_PUBLISH"> 定时发布 </t-radio>
      </t-radio-group>
    </div>
    <div
      v-show="publishType === IPublishType.SCHEDULED_PUBLISH"
      v-if="props.data?.page === 'common-publish'"
      class="pt-[24px]"
    >
      <div class="flex">
        <div class="w-[160px] text-[16px]">发布时间</div>
        <t-date-picker
          class="w-[628px]"
          v-model="pickDate"
          enable-time-picker
          :default-value="dayjs().format('YYYY-MM-DD HH:mm')"
          format="YYYY-MM-DD HH:mm"
          :disable-date="
            (date) => {
              // const now = dayjs();
              const minTime = dayjs().add(1, 'day');
              const maxTime = dayjs().add(89, 'day');
              const selectedDate = dayjs(date);

              if (selectedDate.format('YYYY-MM-DD') < minTime.format('YYYY-MM-DD')) {
                return true;
              }
              if (selectedDate.format('YYYY-MM-DD') > maxTime.format('YYYY-MM-DD')) {
                return true;
              }

              return false;
            }
          "
          :time-picker-props="timePickerProps"
          @pick="onPick"
        />
        <!-- {{ JSON.stringify(dayjs().subtract(0, 'day').format()) }} -->
      </div>
      <div class="publish-describe pt-[12px]">
        如到达指定的发布时间后，仍未审核通过，将在审核通过后自动发布
      </div>
    </div>
  </div>

  <div
    v-if="props?.data?.showQRCode === 'right'"
    class="w-full py-[16px] px-[32px] bg-[#fff] flex gap-[8px] rounded-[16px] mt-[8px]"
  >
    <t-button :disabled="QRState !== 2" @click="submitHandle">
      {{ props.data?.page === 'common-publish' ? '确认提交' : '确认发布' }}
    </t-button>
    <t-button theme="default" @click="gotoHandle" class="">
      {{ props.data?.page === 'common-publish' ? '返回修改' : '返回' }}
    </t-button>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, defineProps } from 'vue';
import RenderView from './BusinessView.vue';
import QRCode from 'qrcode';
import { useRoute, useRouter } from 'vue-router';
import { request } from '@/utils/request';
import { useGlobalStateStore, useObjectRegistry } from '@tencent/dsl-page';

import {
  CardInfo,
  IBusinessCardPage,
  ILatestServicePublishProcess,
  IPrevidewCodeData,
  IPublishType,
  PublishState,
  QRCodeStateEnum,
} from './type';
import PreviewQRCode from './PreviewQRCode.vue';
import dayjs from 'dayjs';
import { removeNullFields } from './helpers';
import { watch } from 'vue';
const props = defineProps<{
  data?: IBusinessCardPage;
}>();

const visible = ref(false);
const index = ref(1);
const QRState = ref<QRCodeStateEnum>(0);
const qrCodeData = ref<IPrevidewCodeData>({
  previewId: '',
  previewCode: '',
  expireTime: '',
});
const cardInfo = ref<CardInfo>({});
const otherTab = ref('');
const previewSrc = ref('');
const route = useRoute();
const router = useRouter();
const close = ref<boolean>(false);
// 获取processId
const processId = route.params?.processId;
const publishState = route.query?.source as PublishState;
// 新手发布单不能定时发布
// const isNewbie = route.query?.newbie;
// 发布设置
const publishType = ref<IPublishType>(IPublishType.IMMEDIATELY_PUBLISH);
const pickDate = ref(dayjs().add(1, 'day'));

// 预览窗口展示状态
const previewState = computed(() => {
  if (!props?.data?.closeable) {
    return 'onlyPreview';
  }
  if (close.value) {
    return 'closePreview';
  }
  return 'ShowPreviewAndBar';
});

const timePickerProps = computed(() => ({
  disableTime: () => {
    const now = dayjs();
    // const maxTime = now.add(2, 'hour');
    const selectedDate = dayjs(pickDate.value);

    const selectedDateOnly = selectedDate.format('YYYY-MM-DD');
    const minDateOnly = now.add(1, 'day').format('YYYY-MM-DD');
    // const maxDateOnly = maxTime.format('YYYY-MM-DD');

    if (selectedDateOnly === minDateOnly) {
      const currentHour = now.hour();
      const currentMinute = now.minute();

      return {
        hour: Array.from({ length: currentHour }, (_, i) => i),
        minute:
          selectedDate.hour() === currentHour
            ? Array.from({ length: currentMinute }, (_, i) => i)
            : [],
      };
    }
    // if (selectedDateOnly === maxDateOnly) {
    //   const maxHour = maxTime.hour();
    //   const maxMinute = maxTime.minute();

    //   return {
    //     hour: Array.from({ length: 24 - maxHour - 1 }, (_, i) => maxHour + i + 1),
    //     minute:
    //       selectedDate.hour() === maxHour
    //         ? Array.from({ length: 60 - maxMinute }, (_, i) => maxMinute + i)
    //         : [],
    //   };
    // }

    return {};
  },
  format: 'HH:mm',
  enableSeconds: false,
}));

// 最新的服务发布单
const latestServicePublishProcess = ref<ILatestServicePublishProcess>({});
const serviceConfig = ref();
const onPick = (date) => {
  pickDate.value = dayjs(date).format('YYYY-MM-DD HH:mm:00');
  // console.log('pickDate.value', pickDate.value);
};

const dataObj: any = computed(() => {
  if (!props.data || props?.data === undefined) {
    // MessagePlugin.warning('请提供有效的JSON数据');
    return {};
  }
  if (typeof props.data === 'object') {
    return props.data;
  }
  if (typeof props.data === 'string') {
    try {
      return JSON.parse(props.data);
    } catch (e) {
      // MessagePlugin.warning('JSON格式错误，请检查输入');
      console.error('JSON解析错误:', e);
      return null;
    }
  }
  return {};
});

const serviceList = computed(() => {
  if (props.data?.page === 'dashboard') {
    return serviceConfig.value?.serviceList;
  }
  // 未传入数据时通过请求拉取
  if (!dataObj.value?.serviceList || dataObj.value?.serviceList.length === 0) {
    // MessagePlugin.warning('请提供有效的JSON数据');
    return latestServicePublishProcess.value?.process?.content?.serviceList;
  }
  if (Array.isArray(dataObj.value?.serviceList)) {
    // 清除脏数据
    return dataObj.value?.serviceList;
  }
  return [];
});

const changeHandler = async (checkedValues: IPublishType) => {
  publishType.value = checkedValues;
};

// 获取最新的名片服务发布单
const getLatestServicePublishProcess = async () => {
  try {
    const res = await request.get({
      url: '/get-latest-service-publish-process',
    });
    latestServicePublishProcess.value = res?.data;
  } catch (error) {
    console.error('最新的名片服务发布单获取失败:', error);
  }
};
// 获取服务配置
const getServiceConfig = async () => {
  try {
    const res = await request.get({
      url: '/get-service-config',
    });
    serviceConfig.value = res?.data?.serviceConfig;
  } catch (error) {
    console.error('获取服务配置失败:', error);
  }
};

const previewCardInfo = ref<CardInfo>();

// 基础信息页面获取当前名片数据
if(route.name==='CardConfig'){
// 从state拿数据构造formData，预览二维码
  const  { getObject } = useObjectRegistry()
  const fn = getObject('utils.prepareSaveCardInfo')
  const globalState = useGlobalStateStore();
  const cardBrief = globalState.getStoreSync('#/definitions/cardBrief/state');
  const latestCardInfoPublish = globalState.getStoreSync('#/definitions/LatestCardInfoPublish/state');
  const merchantServiceCategory = globalState.getStoreSync('#/definitions/merchantServiceCategory/state');
  const formCheckboxStatus = globalState.getStoreSync('#/definitions/formCheckboxStatus/state');
  const customerService = globalState.getStoreSync('#/definitions/customerService/state');
  const cardConfigSence = globalState.getStoreSync('#/definitions/cardConfigSence/state');
  const check = globalState.getStoreSync('#/definitions/formCheckboxStatus/state') 

  // 构造formData
  const formData = {
    cardBrief,
    latestCardInfoPublish,
    merchantServiceCategory,
    formCheckboxStatus,
    customerService,
    cardConfigSence,
  } as any;

  // if(check.sceneMiniProgramChecked||check.sceneFinderChecked){
  //   formData.cardConfigSence = {};
  //   if(check.sceneMiniProgramChecked){
  //     formData.cardConfigSence.miniProgram = cardConfigSence.miniProgram;
  //   }
  //   if(check.sceneFinderChecked){
  //     formData.cardConfigSence.finder = cardConfigSence.finder
  //   }
  // }else if(formData.cardConfigSence){
  //     delete formData.cardConfigSence
  //   }
  const {content:qrCodeCardInfo} = fn (formData)

  previewCardInfo.value = qrCodeCardInfo
  
  watch(()=>globalState.getStoreSync('#/definitions/formCheckboxStatus/state'),(newState)=>{
    // 构造formData
    const newFormData = {
      ...formData,
      formCheckboxStatus:newState
    } 
  const {content:qrCodeCardInfo} = fn (newFormData)
  previewCardInfo.value = qrCodeCardInfo
  })
}





// 生成二维码
const generateQRCode = async () => {
  try {
    let res;
    if (props.data?.page === 'card-publish' || props.data?.page === 'common-publish') {
      // 获取发布预览码
      res = await request.get({
        url: '/get-publish-preview-code',
        params: {
          processId,
        },
      });
    } else {
      // 获取草稿预览码
      res = await request.post({
        url: '/get-draw-preview-code',
        params: {
          cardInfo:
            previewCardInfo.value || removeNullFields(cardInfo.value),
          serviceConfig: {
            serviceList:
              serviceList.value?.filter(
                (item) => !item?.serviceId?.startsWith('empty_')
              ) || latestServicePublishProcess.value?.process?.content?.serviceList,
          },
        },
      });
    }
    qrCodeData.value = res?.data;
    QRState.value = 1;
    QRCode.toDataURL(
      res?.data?.previewCode,
      { errorCorrectionLevel: 'H' },
      (err: any, url: string) => {
        if (err) console.error(err);
        previewSrc.value = url;
        console.log('二维码生成成功:', url);
      }
    );
  } catch (error) {
    console.error('生成二维码失败:', error);
    // MessagePlugin.error('二维码生成失败，请稍后再试')
    return;
  }
};
// 查询二维码状态
const queryQRCodeState = async () => {
  try {
    const res = await request.get({
      url: '/get-preview-code-scan-state',
      params: {
        previewId: qrCodeData.value?.previewId,
      },
    });
    QRState.value = res?.data?.state;
  } catch (error) {
    console.error('查询二维码状态失败:', error);
    // MessagePlugin.error('查询二维码状态失败，请稍后再试')
  }
};

// 发布
const submitHandle = async () => {
  try {
    // 老手
    if (props.data?.page === 'common-publish') {
      let pickDateStr = '';
      if (publishType.value === IPublishType.SCHEDULED_PUBLISH) {
        pickDateStr = dayjs(pickDate.value).unix().toString();
      }
      // 保存发布配置
      await request.post({
        url: '/save-publish-config',
        params: {
          processId,
          publishType: publishType.value,
          scheduleTime: pickDateStr,
        },
      });
      // 提交名片基础信息发布单
      if (publishState === 'cardInfo') {
        await request.post({
          url: '/submit-card-info-publish-process',
          params: {
            processId,
          },
        });
      } else {
        // 提交名片服务发布单
        await request.post({
          url: '/submit-service-publish-process',
          params: {
            processId,
          },
        });
      }
    } else if (props.data?.page === 'card-publish') {
      // 新手
      // 保存发布配置后发布
      await request.post({
        url: '/save-publish-config',
        params: {
          processId: processId,
          publishType: 1,
          scheduleTime: '',
        },
      });
      // 提交新手引导发布单
      await request.post({
        url: '/submit-tutorial-publish-process',
        params: {
          processId,
        },
      });
    } 
    if (publishState === 'cardInfo' || publishState === 'serviceConfig') {
      router.push(`/home/<USER>/service-state/${publishState}/${processId}`);
      return;
    }
    router.push(`/home/<USER>/service-state/publish/${processId}`);
    return;
  } catch (error) {
    console.error(error);
    return;
  } finally {
  }
};

const gotoHandle = () => {
  if (publishState === 'serviceConfig') {
    router.push({
      path: '/home/<USER>/service-config/preview',
      query: { source: 'serviceEdit' },
    });
  } else {
    router.back();
  }
};
// 获取名片信息
const getCardInfo = async () => {
  if (props?.data?.cardInfo) {
    // console.log('传入的cardInfo:::', props.data.cardInfo);
    return props?.data?.cardInfo;
  }
  // 编辑页面，预览现网的数据
  if(route.name==='CardView'){
    const res = await request.get({
      url: '/get-card-info',
    });
    cardInfo.value = res?.data?.cardInfo;
    return;
  }
  try {
    // 老手
    if (props.data?.page === 'dashboard') {
      const res = await request.get({
        url: '/get-card-info',
      });
      cardInfo.value = res?.data?.cardInfo;
    } else {
      const res = await request.get({
        url: '/get-latest-card-info-publish-process',
      });
      cardInfo.value = res?.data?.process?.content;
    }
    // console.log('页面:::', props.data.page);
    // console.log('名片信息666', cardInfo.value);
  } catch (error) {
    console.error('获取名片信息失败', error);
  }
};

// 获取优惠状态
const preferentialState = async () => {
  try {
    const brandMemberAccessState = await request.get({
      url: '/get-brand-member-access-state',
    });
    if (brandMemberAccessState?.data?.state === 1) {
      otherTab.value = '会员';
      return;
    }
    const shakeCouponAccessState = await request.get({
      url: '/get-shake-coupon-access-state',
    });
    if (shakeCouponAccessState?.data?.state === 1) {
      otherTab.value = '优惠';
      return;
    }
  } catch (error) {
    console.error('获取优惠状态失败', error);
    otherTab.value = '';
  }
};
onMounted(() => {
  getCardInfo();
  preferentialState();
  getLatestServicePublishProcess();
  getServiceConfig();
});

// 打开弹窗
const openDialog = () => {
  visible.value = true;
};

// 关闭弹窗
const closeDialog = () => {
  visible.value = false;
};

// function leftClickHandle() {
//   index.value = index.value === 1 ? 2 : 1
// }

// function rightClickHandle() {
//   index.value = index.value === 1 ? 2 : 1
// }
</script>

<style scoped>
.publish-title {
  color: rgba(0, 0, 0, 0.9);
}
.publish-describe {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.3);
}
:deep(.t-radio__label) {
  font-size: 16px;
}
</style>
