export const removeNullFields = (obj: any): any => {
  if (typeof obj !== 'object' || obj === null) {
    return obj; // 如果不是对象或数组，直接返回
  }

  if (Array.isArray(obj)) {
    // 处理数组：递归处理每个元素，并过滤掉 null
    return obj
      .map(removeNullFields) // 递归处理每个元素
      .filter((item) => item !== null); // 过滤掉 null
  }

  // 处理普通对象
  return Object.fromEntries(
    Object.entries(obj)
      .map(([key, value]) => {
        if (typeof value === 'object' && value !== null) {
          return [key, removeNullFields(value)]; // 递归处理嵌套对象
        }
        return [key, value !== null ? value : undefined]; // 替换 null 为 undefined（后续过滤）
      })
      .filter(([_, value]) => value !== undefined) // 过滤掉 undefined（原 null 被替换为 undefined）
  );
};
