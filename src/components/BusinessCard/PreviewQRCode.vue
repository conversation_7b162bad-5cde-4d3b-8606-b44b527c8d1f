<template>
  <div>
    <div v-show="props?.title" class="text-[#000000E5] text-[20px] text-center">
      {{ props?.title }}
    </div>
    <div class="text-center mt-[8px] text-[16px] title-text">
      请使用品牌成员的微信扫描二维码，实时查看名片内容
    </div>
    <div class="text-center text-[16px] title-text mb-[16px]">
      二维码有效期至 {{ expireTime }}，超时请重新获取
    </div>
    <div
      class="flex flex-col items-center justify-center w-[200px] h-[200px] mx-auto relative"
    >
      <t-loading v-if="loading" />
      <t-image
        v-else-if="props?.previewSrc"
        :src="props?.previewSrc"
        alt="二维码"
        class="w-[200px] h-[200px]"
      />
      <div
        v-show="props.QRState == 2"
        class="expire-preview absolute w-[200px] h-[200px] left-0 top-0 z-10 flex items-center justify-center"
      >
        <div class="flex flex-col items-center font-normal">
          <img class="w-[56px] h-[56px]" :src="Success" alt="" />
          <p class="mt-[24px]">扫码成功</p>
          <p>请在手机端预览检查</p>
        </div>
      </div>
      <div
        v-show="props?.QRState == 3"
        class="expire-preview absolute w-[200px] h-[200px] left-0 top-0 z-10 flex items-center justify-center"
      >
        <span>二维码已过期</span>
      </div>
    </div>
    <div
      @click="reCodeHandler"
      class="text-[#4848DD] text-[16px] text-center cursor-pointer mt-[16px]"
    >
      重新获取
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import { CardInfo, IPrevidewCodeData, IServiceConfig, QRCodeStateEnum } from './type';
import Success from '@/assets/images/success.png?url';
import { dayjs } from '@tencent/xpage-web-sdk';

const props = defineProps<{
  visible: boolean;
  title?: string;
  previewSrc: string;
  queryQRCodeState: () => void;
  generateQRCode: () => void;
  QRState: QRCodeStateEnum;
  qrCodeData: IPrevidewCodeData;
}>();

const timeIdRef = ref();
const loading = ref(false);

// const isExpire = computed(() => {
//   return new Date(qrCodeData.value.expireTime) < new Date();
// });

const reCodeHandler = async () => {
  loading.value = true;
  try {
    await props?.generateQRCode();
    await loopQuery();
  } finally {
    loading.value = false;
  }
};

const expireTime = computed(() => {
  if (!props?.qrCodeData?.expireTime?.trim()) {
    return '--';
  }
  return dayjs(Number(props?.qrCodeData?.expireTime) * 1000).format('MM月DD日 HH:mm');
});

onMounted(() => {
  timeIdRef.value && clearInterval(timeIdRef.value);
  // if (props.visible) {
  //   loading.value = true;
  //   timeIdRef.value = setInterval(async () => {
  //     try {
  //       await props?.queryQRCodeState();
  //     } finally {
  //       loading.value = false;
  //     }
  //   }, 5000);
  //   props.generateQRCode();
  // props.queryQRCodeState().finally(() => {
  //   loading.value = false;
  // });
  // }
});

const loopQuery = () => {
  timeIdRef.value && clearInterval(timeIdRef.value);
  timeIdRef.value = setInterval(() => {
    try {
      props.queryQRCodeState();
    } finally {
      loading.value = false;
    }
  }, 3000);
};

watch(
  () => props.visible,
  async (newVal) => {
    if (newVal) {
      reCodeHandler();
      // loading.value = true;
      // try {
      //   await props.generateQRCode();
      //   loopQuery();
      // } catch (error) {
      //   console.error(error);
      // }
    } else {
      timeIdRef.value && clearInterval(timeIdRef.value);
    }
  },
  { immediate: true }
);

watch(
  () => props.QRState,
  () => {
    if (props.QRState !== 1) {
      timeIdRef.value && clearInterval(timeIdRef.value);
    }
  }
);

onUnmounted(() => {
  timeIdRef.value && clearInterval(timeIdRef.value);
});
</script>

<style scoped>
.title-text {
  color: rgba(0, 0, 0, 0.5);
}
.expire-preview {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.9);
  background-color: rgba(255, 255, 255, 0.95);
}
</style>
