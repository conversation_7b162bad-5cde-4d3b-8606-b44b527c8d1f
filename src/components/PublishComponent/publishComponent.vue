<template>
  <t-dialog
    header="发布设置"
    width="960px"
    :visible.sync="props.visible"
    confirm-btn="确认修改"
    :on-close="closeHandle"
    :on-confirm="submitHandle"
    :destroy-on-close="true"
  >
    <div class="flex pt-[32px]">
      <div class="w-[160px] text-[16px]">发布设置</div>
      <t-radio-group
        class="text-[16px]"
        :default-value="IPublishType.IMMEDIATELY_PUBLISH"
        @change="changeHandler"
      >
        <t-radio :value="IPublishType.IMMEDIATELY_PUBLISH"> 审核通过后自动发布 </t-radio>
        <t-radio :value="IPublishType.SCHEDULED_PUBLISH"> 定时发布 </t-radio>
      </t-radio-group>
    </div>
    <div v-show="publishType === IPublishType.SCHEDULED_PUBLISH" class="flex pt-[32px]">
      <div class="w-[160px] text-[16px]">发布时间</div>
      <t-date-picker
        v-model="pickDate"
        class="w-[596px]"
        enable-time-picker
        :default-value="dayjs().format('YYYY-MM-DD HH:mm')"
        format="YYYY-MM-DD HH:mm"
        :disable-date="
          (date) => {
            // const now = dayjs();
            const minTime = dayjs().add(1, 'day');
            const maxTime = dayjs().add(89, 'day');
            const selectedDate = dayjs(date);

            if (selectedDate.format('YYYY-MM-DD') < minTime.format('YYYY-MM-DD')) {
              return true;
            }
            if (selectedDate.format('YYYY-MM-DD') > maxTime.format('YYYY-MM-DD')) {
              return true;
            }

            return false;
          }
        "
        :time-picker-props="timePickerProps"
        @pick="onPick"
      />
      <!-- {{ JSON.stringify(dayjs().subtract(0, 'day').format()) }} -->
    </div>
    <div
      v-show="publishType === IPublishType.SCHEDULED_PUBLISH"
      class="publish-describe pt-[12px] pl-[160px]"
    >
      如到达指定的发布时间后，仍未审核通过，将在审核通过后自动发布
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { computed, ref, defineProps } from 'vue';
import dayjs from 'dayjs';
import { IPublishType } from '../BusinessCard/type';
import { PublishProps } from './type';
import { request } from '@/utils/request';
import { MessagePlugin } from 'tdesign-vue-next';

// eslint-disable-next-line vue/valid-define-props
const props = defineProps<PublishProps>();

// 发布设置
const publishType = ref<IPublishType>(IPublishType.IMMEDIATELY_PUBLISH);
// const pickDate = ref(dayjs().format());
const pickDate = ref(dayjs().add(1, 'day'));

const changeHandler = async (checkedValues: IPublishType) => {
  publishType.value = checkedValues;
};

const timePickerProps = computed(() => ({
  disableTime: () => {
    const now = dayjs();
    // const maxTime = now.add(2, 'hour');
    const selectedDate = dayjs(pickDate.value);

    const selectedDateOnly = selectedDate.format('YYYY-MM-DD');
    const minDateOnly = now.add(1, 'day').format('YYYY-MM-DD');
    // const maxDateOnly = maxTime.format('YYYY-MM-DD');

    if (selectedDateOnly === minDateOnly) {
      const currentHour = now.hour();
      const currentMinute = now.minute();

      return {
        hour: Array.from({ length: currentHour }, (_, i) => i),
        minute:
          selectedDate.hour() === currentHour
            ? Array.from({ length: currentMinute }, (_, i) => i)
            : [],
      };
    }
    // if (selectedDateOnly === maxDateOnly) {
    //   const maxHour = maxTime.hour();
    //   const maxMinute = maxTime.minute();

    //   return {
    //     hour: Array.from({ length: 24 - maxHour - 1 }, (_, i) => maxHour + i + 1),
    //     minute:
    //       selectedDate.hour() === maxHour
    //         ? Array.from({ length: 60 - maxMinute }, (_, i) => maxMinute + i)
    //         : [],
    //   };
    // }

    return {};
  },
  format: 'HH:mm',
  enableSeconds: false,
}));

const closeHandle = () => {
  props.setVisible(false);
};

// 发布
const submitHandle = async () => {
  try {
    // 老手
    let pickDateStr = '';
    if (publishType.value === IPublishType.SCHEDULED_PUBLISH) {
      pickDateStr = dayjs(pickDate.value).unix().toString();
    }
    // 保存发布配置
    await request.post({
      url: '/save-publish-config',
      params: {
        processId: props.processId,
        publishType: publishType.value,
        scheduleTime: pickDateStr,
      },
    });
    MessagePlugin.success('修改成功');
  } catch (error) {
    console.log(error);
    MessagePlugin.success('修改失败');
  } finally {
    props.setVisible(false);
  }
};

const onPick = (date) => {
  pickDate.value = dayjs(date).format('YYYY-MM-DD HH:mm:00');
  // console.log('pickDate.value', pickDate.value);
};
</script>

<style scoped>
.publish-title {
  color: rgba(0, 0, 0, 0.9);
}
.publish-describe {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.3);
}
:deep(.t-radio__label) {
  font-size: 16px;
}
:deep(.t-button__text) {
  color: red;
}
</style>
