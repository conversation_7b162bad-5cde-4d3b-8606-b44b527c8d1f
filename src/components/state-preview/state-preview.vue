<template>
  <div class="state-preview">
    <img v-if="statusValue !== -1" :src="stateImage" alt="状态预览" class="state-image" />
    <p class="state-text">
      {{ stateText }}
    </p>
    <p v-if="stateDesc !== ''" class="state-desc">
      {{ stateDesc }}
    </p>
    <t-button
      v-if="
        statusValue === ProcessState.AUDITING && !(isFirst && typeValue === 'publish')
      "
      theme="default"
      class="back-button"
      @click="goBack"
    >
      返回
    </t-button>
    <t-button
      v-if="
        statusValue === ProcessState.REJECTED && !(isFirst && typeValue === 'publish')
      "
      class="back-button"
      @click="goBack"
    >
      {{ rejectButtonText }}
    </t-button>
    <t-button
      v-if="
        statusValue === ProcessState.PUBLISHED && !(isFirst && typeValue === 'publish')
      "
      theme="default"
      @click="finishHandle"
    >
      完成
    </t-button>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { request } from '@/utils/request';

// 导入状态图片
import auditingImg from '@/assets/images/auditing.png?url';
import rejectImg from '@/assets/images/reject.png?url';
import finished from '@/assets/images/finished.png?url';
import { ProcessState } from '../CardService/utils/type';
import { MessagePlugin } from 'tdesign-vue-next';

const route = useRoute();
const router = useRouter();
const statusValue = ref(-1);
const typeValue = ref('service');
const loading = ref(false);
const processId = ref('');
const isFirst = ref(false);

onMounted(async () => {
  console.log('route.params', route);

  try {
    const res = await request.request({
      url: '/get-latest-tutorial-publish-process',
      method: 'GET',
    });

    if (res && res.code === 0 && res.data?.process) {
      const { processId: id } = res.data?.process;

      if (id && res.data.process.state !== ProcessState.PUBLISHED) {
        isFirst.value = true;
      }
    } else {
      isFirst.value = false;
    }
  } catch (error) {
    isFirst.value = false;
  }
  // service / config / publish / serviceConfig / cardInfo
  typeValue.value = String(route.params.type);

  // 获取 processId
  if (route.params.processId) {
    processId.value = String(route.params.processId);
    await fetchProcessState();
  } else {
    statusValue.value = -1;
  }
});

// 根据类型获取对应的流程状态
const fetchProcessState = async () => {
  if (!processId.value) return;

  loading.value = true;
  try {
    // 根据类型调用不同的 API
    const apiUrlMap: Record<string, string> = {
      service: '/get-service-publish-process',
      config: '/get-card-info-publish-process',
      publish: '/get-tutorial-publish-process',
      serviceConfig: '/get-service-publish-process',
      cardInfo: '/get-card-info-publish-process',
    };

    const apiUrl = apiUrlMap[typeValue.value] || apiUrlMap.service;

    const response = await request.request({
      url: apiUrl,
      method: 'GET',
      params: {
        processId: processId.value,
      },
    });
    console.log('response', response);

    if (response?.code === 0 && response?.data?.process) {
      statusValue.value = response.data.process.state;
    } else {
      MessagePlugin.error('获取状态信息失败');
      statusValue.value = -1;
    }
  } catch (error) {
    console.error('获取状态信息失败:', error);
    MessagePlugin.error('获取状态信息失败');
    statusValue.value = -1;
  } finally {
    loading.value = false;
  }
};

// 根据状态ID计算显示的图片
const stateImage = computed(() => {
  console.log('statusValue.value', statusValue.value);

  switch (statusValue.value) {
    case ProcessState.AUDITING:
    case ProcessState.APPROVED:
    case ProcessState.AUDIT_PASS:
    case ProcessState.PUBLISHING:
      return auditingImg;
    case ProcessState.REJECTED:
    case ProcessState.CANCELED:
      return rejectImg;
    case ProcessState.PUBLISHED:
      return finished;
    default:
      return auditingImg;
  }
});

// 根据类型和状态ID计算显示的文本
const stateText = computed(() => {
  console.log('typeValue.value', typeValue.value);

  // 处理 isFirst 且 typeValue 为 publish 的特殊情况
  if (typeValue.value === 'publish') {
    return statusValue.value === 8 ? '发布成功' : '发布中';
  }
  let prefix = '';

  if (typeValue.value === 'service' || typeValue.value === 'serviceConfig') {
    prefix = '名片服务';
  } else if (typeValue.value === 'config' || typeValue.value === 'cardInfo') {
    prefix = '基础信息';
  }

  switch (statusValue.value) {
    case ProcessState.AUDITING:
      return `${prefix}审核中`;
    case ProcessState.REJECTED:
      return `${prefix}审核驳回`;

    case ProcessState.CANCELED:
      return `${prefix}已作废`;
    case ProcessState.PUBLISHED:
      return `${prefix}发布成功`;
    case ProcessState.PUBLISHING:
    case ProcessState.AUDIT_PASS:
    case ProcessState.APPROVED:
      return `${prefix}发布中`;
    default:
      return '';
  }
});

// 根据类型和状态ID计算显示的描述文本
const stateDesc = computed(() => {
  // 处理 isFirst 且 typeValue 为 publish 的特殊情况
  if (isFirst.value && typeValue.value === 'publish') {
    return '发布成功后可在"商家名片>名片管理"中查看名片内容';
  }
  switch (statusValue.value) {
    case ProcessState.AUDITING:
      return isFirst.value
        ? '审核通过后，将在名片管理配置首页更新审核状态'
        : '审核通过后，将按照设置的发布时间自动发布';
    case ProcessState.REJECTED:
      return '请前往名片配置详情页查看驳回原因';
    default:
      return '';
  }
});

// 计算驳回按钮文本
const rejectButtonText = computed(() => {
  if (typeValue.value === 'service' || typeValue.value === 'serviceConfig') {
    return '修改服务';
  }
  if (typeValue.value === 'config' || typeValue.value === 'cardInfo') {
    return '修改信息';
  }
  return '';
});

// 返回按钮点击事件
const goBack = () => {
  console.log('typeValue.value', typeValue.value);
  if (typeValue.value === 'service' || typeValue.value === 'serviceConfig') {
    if (isFirst.value) {
      router.push('/home/<USER>/service-config/config');
    } else {
      router.push('/home/<USER>/service-config/preview');
    }
  }
  if (typeValue.value === 'config' || typeValue.value === 'cardInfo') {
    if (isFirst.value) {
      router.push(`/home/<USER>/card-config`);
    } else {
      router.push(`/home/<USER>/card-view`);
    }
  }
  if (typeValue.value === 'publish') {
    router.push('/home/<USER>/card-dashboard');
  }
};

const finishHandle = () => {
  router.push('/home/<USER>/card-detail');
};
</script>

<style scoped>
.state-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.state-image {
  width: 72px;
  height: 72px;
}

.state-text {
  font-weight: bold;
  margin-top: 20px;
  margin-bottom: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  font-style: Medium;
  font-size: 24px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: center;
}

.state-desc {
  margin-bottom: 20px;
  font-family: PingFang SC;
  font-weight: 400;
  font-style: Regular;
  font-size: 16px;
  leading-trim: NONE;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.5);
}

.back-button {
  height: 40;
  border-radius: 8px;
  padding-top: 9px;
  padding-right: 24px;
  padding-bottom: 9px;
  padding-left: 24px;
}
</style>
