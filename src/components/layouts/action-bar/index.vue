<template>
  <div class="card-link-action-bar" :class="barClass" :style="barStyle">
    <!-- 上方状态文案 -->
    <div v-if="$slots.status || statusText" class="card-link-action-bar__status-area">
      <slot name="status">
        <span v-if="statusText" class="card-link-action-bar__status-text">
          {{ statusText }}
        </span>
      </slot>
    </div>

    <!-- 下方操作按钮 -->
    <div v-if="$slots.actions || actions?.length" class="card-link-action-bar__actions-area">
      <slot name="actions">
        <t-space :size="spacing" :align="actionsAlign">
          <t-button
            v-for="action in actions"
            :key="action.key"
            v-bind="getActionProps(action)"
            @click="handleAction(action)"
          >
            <component :is="action.icon" v-if="action.icon" class="card-link-mr-xs" />
            {{ action.text }}
          </t-button>
        </t-space>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Button as TButton, Space as TSpace } from 'tdesign-vue-next';
import type { ActionBarProps, ActionBarEmits, ActionItem } from './types';

defineOptions({
  name: 'CardLinkActionBar',
});

const props = withDefaults(defineProps<ActionBarProps>(), {
  spacing: 'medium',
  actions: () => [],
  showBorder: false,
  sticky: false,
  actionsAlign: 'end',
});

const emit = defineEmits<ActionBarEmits>();

const barClass = computed(() => [
  {
    'card-link-action-bar--border': props.showBorder,
    'card-link-action-bar--sticky': props.sticky,
    'card-link-action-bar--shadow': props.sticky,
  },
]);

const barStyle = computed(() => {
  const styles: Record<string, string> = {};

  if (props.backgroundColor) {
    styles.backgroundColor = props.backgroundColor;
  }

  return styles;
});

const handleAction = (action: ActionItem) => {
  if (action.handler) {
    action.handler();
  }
  emit('action', action.key, action);
};

const getActionProps = (action: ActionItem) => {
  const { key, text, handler, icon, props: customProps, ...rest } = action;
  return {
    theme: action.theme || 'default',
    variant: action.variant || 'base',
    size: action.size || 'medium',
    loading: action.loading || false,
    disabled: action.disabled || false,
    ...customProps,
    ...rest,
  };
};
</script>

<style lang="less" scoped>
@import '../../styles/variables.less';

.card-link-action-bar {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px 32px;
  background-color: #fff;
  border-radius: 16px;
  box-sizing: border-box;

  &--border {
    border: 1px solid @card-link-action-bar-border-color;
  }

  &--shadow {
    box-shadow: @card-link-shadow-1;
  }

  // 状态文案区域
  &__status-area {
    display: flex;
    align-items: center;
    text-align: center;
  }

  // 操作按钮区域
  &__actions-area {
    display: flex;
    justify-content: flex-start;

    &--end {
      justify-content: flex-end;
    }

    &--center {
      justify-content: center;
    }

    &--start {
      justify-content: flex-start;
    }
  }

  // 状态文本
  &__status-text {
    color: rgba(0, 0, 0, 0.9);
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
  }

  // 粘性定位
  &--sticky {
    position: sticky;
    bottom: 0;
    z-index: @card-link-zindex-affix;
  }

  // 响应式适配
  @media (max-width: @card-link-screen-sm) {
    padding: @card-link-spacer;
    gap: 12px;

    &__status-area {
      text-align: center;
    }

    &__actions-area {
      justify-content: center !important;
    }
  }
}
</style>
