import type { Component } from 'vue';

export interface ActionItem {
  key: string;
  text: string;
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  variant?: 'base' | 'outline' | 'dashed' | 'text';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  icon?: Component;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  props?: Record<string, any>;
  handler?: () => void | Promise<void>;
}

export interface ActionBarProps {
  spacing?: 'small' | 'medium' | 'large';
  actions?: ActionItem[];
  statusText?: string;
  showBorder?: boolean;
  sticky?: boolean;
  backgroundColor?: string;
  // 操作按钮对齐方式
  actionsAlign?: 'start' | 'center' | 'end';
}

export interface ActionBarEmits {
  action: [key: string, action: ActionItem];
}

export type ComponentSize = 'small' | 'medium' | 'large';
