<template>
  <div class="card-link-page-header">
    <!-- 标题区域 -->
    <div class="card-link-page-header__title-area">
      <div>
        <!-- 返回按钮 -->
        <div v-if="showBackButton" class="card-link-page-header__back" @click="handleBack">
          <t-icon name="chevron-left" />
          <span>{{ backText }}</span>
        </div>

        <!-- 页面标题 -->
        <h1 v-if="title" class="card-link-page-header__title">
          <slot name="title">
            {{ title }}
          </slot>
        </h1>

        <!-- 页面描述 -->
        <p v-if="description || $slots.description" class="card-link-page-header__description">
          <slot name="description">
            {{ description }}
          </slot>
        </p>
      </div>

      <!-- 额外内容 -->
      <div v-if="$slots.extra || extra" class="card-link-page-header__extra">
        <slot name="extra">
          <span v-if="extra">{{ extra }}</span>
        </slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Icon as TIcon } from 'tdesign-vue-next';
import type { PageHeaderProps, PageHeaderEmits } from './types';

defineOptions({
  name: 'CardLinkPageHeader',
});

withDefaults(defineProps<PageHeaderProps>(), {
  backText: '返回',
  showBackButton: false,
});

const emit = defineEmits<PageHeaderEmits>();

const handleBack = () => {
  emit('back');
};
</script>

<style lang="less" scoped>
@import '../../styles/variables.less';

.card-link-page-header {
  // 标题区域
  &__title-area {
    .card-link-flex-between();
  }

  // 标题
  &__title {
    color: rgba(0, 0, 0, 0.9);
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
  }

  // 描述
  &__description {
    color: rgba(0, 0, 0, 0.5);
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
    margin-top: 8px;
  }

  // 返回按钮
  &__back {
    .card-link-flex-vertical-center();
    gap: @card-link-spacer-xs;
    color: green;
    cursor: pointer;
    transition: color @card-link-transition-duration-base @card-link-transition-timing-function;

    &:hover {
      color: blue;
    }
  }

  // 额外内容
  &__extra {
    .card-link-flex-vertical-center();
    align-self: flex-start;
    gap: @card-link-spacer;
  }

  // 面包屑
  &__breadcrumb {
    margin-bottom: @card-link-spacer;
  }

  // 响应式适配
  @media (max-width: @card-link-screen-sm) {
    padding: @card-link-spacer;

    &__title-area {
      flex-direction: column;
      align-items: flex-start;
      gap: @card-link-spacer;
    }

    &__title {
      font-size: @card-link-font-size-l;
    }
  }
}
</style>
