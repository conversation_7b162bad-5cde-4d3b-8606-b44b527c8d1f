<template>
  <div class="card-link-page-layout" :class="layoutClass">
    <div class="main-area">
      <!-- 页面头部区域 -->
      <header v-if="$slots.header" class="card-link-page-layout__header">
        <slot name="header" />
      </header>

      <!-- 主要内容区域 -->
      <main class="card-link-page-layout__content" :class="contentClass" :style="contentStyle">
        <slot name="content">
          <slot />
        </slot>
      </main>
    </div>

    <!-- 操作栏区域 -->
    <footer v-if="$slots.actions" class="card-link-page-layout__actions" :class="actionsClass">
      <slot name="actions" />
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

defineOptions({
  name: 'MainPageContainer',
});

interface MainPageContainerProps {
  contentClass?: string;
  actionsClass?: string;
  layoutClass?: string;
  // 布局模式
  layoutMode?: 'responsive' | 'fixed' | 'auto';
  // 最大宽度（仅在 fixed 和 auto 模式下生效）
  maxWidth?: string | number;
  // 最小宽度（仅在 fixed 和 auto 模式下生效）
  minWidth?: string | number;
  // 内容区域内边距
  contentPadding?: string | number;
}

const props = withDefaults(defineProps<MainPageContainerProps>(), {
  contentClass: '',
  actionsClass: '',
  layoutClass: '',
  layoutMode: 'fixed',
  maxWidth: '1224px',
  minWidth: '960px',
  contentPadding: '0px',
});

// 计算布局类名
const layoutClass = computed(() => [
  props.layoutClass,
  'card-link-page-layout--container',
  `card-link-page-layout--${props.layoutMode}`,
]);

// 计算内容区域样式
const contentStyle = computed(() => {
  const styles: Record<string, string> = {};

  // 根据布局模式设置宽度
  if (props.layoutMode === 'responsive') {
    styles.width = '100%';
  } else if (props.layoutMode === 'fixed') {
    styles.width = '100%';
  } else if (props.layoutMode === 'auto') {
    const maxWidth = typeof props.maxWidth === 'number' ? `${props.maxWidth}px` : props.maxWidth;
    const minWidth = typeof props.minWidth === 'number' ? `${props.minWidth}px` : props.minWidth;
    styles.maxWidth = maxWidth;
    styles.minWidth = minWidth;
    styles.width = '100%';
  }

  // 设置内边距
  if (props.contentPadding) {
    const padding = typeof props.contentPadding === 'number' ? `${props.contentPadding}px` : props.contentPadding;
    styles.padding = padding;
  }

  return styles;
});
</script>

<style lang="less" scoped>
@import '../../styles/variables.less';

.card-link-page-layout {
  .main-area {
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 32px;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.04);
    background: #fff;
    min-height: 640px;
    margin-bottom: 8px;
  }

  // 页面头部区域
  &__header {
    flex-shrink: 0;
  }

  // 页面内容区域
  &__content {
    flex: 1;
    margin: 0 auto;
    box-sizing: border-box;

    &--no-padding {
      padding: 0;
    }

    &--full-width {
      max-width: none !important;
      min-width: auto !important;
      width: 100% !important;
    }
  }

  // 布局模式样式
  &--responsive {
    .card-link-page-layout__content {
      width: 100%;
      padding: 16px;
    }
  }

  // 页面操作栏区域
  &__actions {
    flex-shrink: 0;
  }

  // 响应式适配
  @media (max-width: @card-link-screen-md) {
    &__content {
      padding: @card-link-spacer;
      min-width: auto;
    }
  }
}
</style>
