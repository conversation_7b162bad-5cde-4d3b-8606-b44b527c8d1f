import type { ActionItem } from '../action-bar/types';

export type { ActionItem };

export interface PageLayoutProps {
  title?: string;
  description?: string;
  showBackButton?: boolean;
  backText?: string;
  contentClass?: string;
  actionsClass?: string;
  showDefaultActions?: boolean;
  saveText?: string;
  confirmText?: string;
  cancelText?: string;
  saveLoading?: boolean;
  confirmLoading?: boolean;
  showSave?: boolean;
  showConfirm?: boolean;
  showCancel?: boolean;
  actions?: ActionItem[];
  spacing?: 'small' | 'medium' | 'large';
  statusText?: string;
  layoutClass?: string;
  extra?: string;
}

export interface PageLayoutEmits {
  save: [];
  confirm: [];
  cancel: [];
  back: [];
  action: [key: string, action: ActionItem];
}

export type LayoutSize = 'small' | 'medium' | 'large';
