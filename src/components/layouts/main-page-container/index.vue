<template>
  <BaseLayout :content-class="contentClass" :actions-class="actionsClass" :layout-class="layoutClass">
    <!-- 头部区域 -->
    <template #header>
      <slot name="header">
        <!-- 当没有自定义头部时，使用内置的 PageHeader -->
        <PageHeader
          v-if="title || description || showBackButton"
          :title="title"
          :description="description"
          :show-back-button="showBackButton"
          :back-text="backText"
          :extra="extra"
          @back="handleBack"
        >
          <template #title>
            <slot name="title">
              {{ title }}
            </slot>
          </template>
          <template #description>
            <slot name="description">
              {{ description }}
            </slot>
          </template>
          <template v-if="showAddButton" #extra>
            <t-button @click="handleAdd"> 添加 </t-button>
          </template>
        </PageHeader>
      </slot>
    </template>

    <!-- 内容区域 -->
    <template #content>
      <slot name="content">
        <slot />
      </slot>
    </template>

    <!-- 操作栏区域 -->
    <template #actions>
      <slot name="actions">
        <!-- 当没有自定义操作栏时，使用内置的 ActionBar -->
        <ActionBar
          v-if="actions?.length || statusText || $slots['actions-status'] || $slots['actions-buttons']"
          :actions="actions"
          :spacing="spacing"
          :status-text="statusText"
          :show-border="showActionBarBorder"
          :sticky="stickyActions"
          :actions-align="actionsAlign"
          @action="handleAction"
        >
          <!-- 上方状态文案 -->
          <template #status>
            <slot name="actions-status">
              <span v-if="statusText" class="action-status-text">
                {{ statusText }}
              </span>
            </slot>
          </template>

          <!-- 下方操作按钮 -->
          <template v-if="$slots['actions-buttons']" #actions>
            <slot name="actions-buttons" />
          </template>
        </ActionBar>
      </slot>
    </template>
  </BaseLayout>
</template>

<script setup lang="ts">
import { Button as TButton } from 'tdesign-vue-next';
import BaseLayout from './base.vue';
import PageHeader from '../page-header';
import ActionBar from '../action-bar';
import type { ActionItem } from './types';

defineOptions({
  name: 'MainPageContainer',
});

interface MainPageContainerProps {
  // 布局相关
  contentClass?: string;
  actionsClass?: string;
  layoutClass?: string;

  // PageHeader 相关
  title?: string;
  description?: string;
  showAddButton?: boolean;
  showBackButton?: boolean;
  backText?: string;
  extra?: string;

  // ActionBar 相关
  actions?: ActionItem[];
  statusText?: string;
  spacing?: 'small' | 'medium' | 'large';
  showActionBarBorder?: boolean;
  stickyActions?: boolean;
  actionsAlign?: 'start' | 'center' | 'end';

  // 默认操作按钮
  showSave?: boolean;
  showConfirm?: boolean;
  showCancel?: boolean;
  saveText?: string;
  confirmText?: string;
  cancelText?: string;
  saveLoading?: boolean;
  confirmLoading?: boolean;
  cancelLoading?: boolean;
}

interface MainPageContainerEmits {
  add: [];
  back: [];
  save: [];
  confirm: [];
  cancel: [];
  action: [key: string, action: ActionItem];
}

withDefaults(defineProps<MainPageContainerProps>(), {
  contentClass: '',
  actionsClass: '',
  layoutClass: '',
  backText: '返回',
  saveText: '保存',
  confirmText: '确认',
  cancelText: '取消',
  spacing: 'medium',
  showActionBarBorder: false,
  stickyActions: false,
  actionsAlign: 'end',
  showDefaultActions: false,
  showSave: true,
  showConfirm: true,
  showCancel: true,
  saveLoading: false,
  confirmLoading: false,
  cancelLoading: false,
  actions: () => [],
});

const emit = defineEmits<MainPageContainerEmits>();

const handleAdd = () => {
  emit('add');
};

const handleBack = () => {
  emit('back');
};

const handleAction = (key: string, action: ActionItem) => {
  if (action.handler) {
    action.handler();
  }
  emit('action', key, action);
};
</script>

<style lang="less" scoped>
.action-status-text {
  color: #666;
  font-size: 14px;
}
</style>
