/**
 * 服务组件相关常量定义
 */
export const CONST = {
  /** 提示文案 */
  MSG: {
    CATEGORY_SERVICE_LIMIT: '每个分类下最多添加5个服务',
    TOTAL_SERVICE_LIMIT: '最多添加15个服务项，请删除或修改其他服务',
    CATEGORY_LIMIT: '最多添加3个分类，请删除或修改其他分类',
    DELETE_CATEGORY: '服务分类移除后，该分类下的服务将移动到无分类列表中',
    DELETE_SERVICE_MIN: '名片服务列表最少需要配置2个服务',
    DELETE_SERVICE: '删除服务后，该服务将从用户的名片服务列表消失',
    EMPTY_SERVICE_HINT: '请添加至少2个服务',
    NO_DATA_HINT: '未添加服务',
    CATEGORY_EXISTS: '该分类名称已存在',
    CATEGORY_MAX_LIMIT: '每个分类下最多添加5个服务',
    SERVICE_MAX_LIMIT: '共有15个服务，不支持再添加',
    DRAG_FAILED_SERVICE_ID: '拖拽失败：服务ID为空',
    DRAG_FAILED_SERVICE_NOT_FOUND: '移动失败：未找到服务',
    DRAG_CATEGORY_MAX: '分类下已有服务达到上限，不能再添加',
    CATEGORY_NAME_TOO_LONG: '分类名称不可超过6个字符',
    CATEGORY_NAME_EMOJI: '分类名称不支持表情符号',
    CATEGORY_NAME_PLACEHOLDER: '输入分类名称，不超过6个字符',
    SERVICE_NAME_TOO_LONG: '服务名称不可超过8个字符',
    SERVICE_NAME_EMOJI: '服务名称不支持表情符号',
    SERVICE_NAME_PLACEHOLDER: '输入服务名称，不超过8个字符',
    MINIPROGRAM_PATH_START_SLASH: '小程序路径不能以/开头',
    INVALID_WEB_URL: '请输入有效的网址',
    DUPLICATE_MINIPROGRAM: '存在相同的小程序跳转路径',
    DUPLICATE_WEB_PATH: '网页链接已存在',
  },
  /** 成功提示 */
  SUCCESS: {
    CATEGORY_ADDED: '已添加服务分类',
    CATEGORY_MODIFIED: '已修改服务分类',
    CATEGORY_REMOVED: '已移除',
    SERVICE_MODIFIED: '已修改服务',
    SERVICE_ADDED: '已添加服务',
    SERVICE_DELETED: '已删除服务',
  },
  /** 错误提示 */
  ERROR: {
    GET_SERVICE: '获取服务数据失败',
    NO_CATEGORY: '不能提交"无分类"，请添加至少一个自定义分类',
    EMPTY_CATEGORY: '存在未配置服务的分类',
  },
  /** 按钮文本 */
  BTN: {
    CONFIRM: '确认',
    CANCEL: '取消',
    I_KNOW: '我知道了',
    CONFIRM_DELETE: '确认删除',
    CONFIRM_REMOVE: '确认移除',
  },
  /** 业务常量 */
  LIMIT: {
    MAX_CATEGORIES: 3,
    MAX_SERVICES_PER_CATEGORY: 5,
    MAX_TOTAL_SERVICES: 15,
    MIN_SERVICES: 2,
  },
  /** CSS类名 */
  CLASS: {
    DRAGGING_SOURCE: 'dragging-source',
    DROP_TARGET: 'drop-target',
    CATEGORY_DRAGGING: 'category-dragging',
    SPACER_ACTIVE: 'spacer-active',
    SPACER_VISIBLE: 'spacer-visible',
  },
  /** 拖拽操作相关常量 */
  DRAG: {
    DEFAULT_CATEGORY: '无分类',
    DRAG_TYPE_ATTR: 'data-drag-type',
    CATEGORY_TYPE: 'category',
    EVENT_TIMEOUT: 300, // 事件绑定的超时时间
  },
  /** 状态提示文案 */
  STATUS: {
    DRAFT: '你有一个草稿中的？',
    DRAFT_ACTION: '继续修改',
    AUDITING: '名片服务审核中，预计将在1-3 个工作日内审核完成',
    AUDITING_ACTION: '查看',
    APPROVED_ACTION: '查看',
    REJECTED: '名片服务审核驳回，请修改后重新提交',
    REJECTED_ACTION: '查看',
  },

  BAR_STATUS: {
    SERVICE: {
      DRAFT: '名片服务编辑中',
      DRAFT_ACTION: '继续修改',
      AUDITING: '名片服务审核中，预计1-3 个工作日内审核完成',
      AUDITING_ACTION: '查看',
      APPROVED_ACTION: '查看',
      REJECTED: '名片服务审核驳回，请修改后重新提交',
      REJECTED_ACTION: '查看',
    },
    CONFIG: {
      DRAFT: '基础信息编辑中',
      DRAFT_ACTION: '继续修改',
      AUDITING: '基础信息审核中，预计1-3 个工作日内审核完成',
      AUDITING_ACTION: '查看',
      APPROVED_ACTION: '查看',
      REJECTED: '基础信息审核驳回，请修改后重新提交',
      REJECTED_ACTION: '查看',
    },
  },
};
