export type JumpInfo = {
  jumpType: number;
  miniProgram?: {
    appid: string;
    path: string;
  };
  web?: {
    path: string;
  };
};

export interface ServiceItem {
  serviceId?: string;
  serviceName: string;
  serviceClassifyName?: string;
  jumpInfo: {
    jumpType: number;
    miniProgram?: {
      appid: string;
      path: string;
    };
    web?: {
      path: string;
    };
  };
}

export interface ServiceData {
  serviceList?: ServiceItem[];
  processId?: string;
  data?: string;
}

export interface NormalizedServiceItem {
  id: string;
  name: string;
  linkType: string;
  link: string;
  originalService?: ServiceItem;
}

export interface NormalizedCategory {
  name: string;
  services: NormalizedServiceItem[];
}

export type NormalizedServiceData = NormalizedCategory[];

/**
 * 拖拽状态类型
 */
export interface DragState {
  // 当前拖拽的类型：分类拖拽或服务拖拽
  dragType: 'category' | 'service' | null;

  // 分类拖拽信息
  categoryInfo: DragCategoryInfo | null;

  // 服务拖拽信息
  serviceInfo: DragServiceInfo | null;

  // 当前激活的分隔符索引
  activeSpacer: number | null;
}

/**
 * 分类拖拽信息
 */
export interface DragCategoryInfo {
  // 分类名称
  categoryName: string | null;

  // 分类索引
  categoryIndex: number | null;

  // 原始索引
  fromIndex: number | null;
}

/**
 * 服务拖拽信息
 */
export interface DragServiceInfo {
  // 服务ID
  serviceId: string | null;

  // 服务名称
  serviceName: string | null;

  // 来源分类名称
  fromCategoryName: string | null;

  // 来源分类索引
  fromCategoryIndex: number | null;

  // 服务在分类内的索引
  serviceIndex: number | null;
}

// 详细名片服务格式
export interface DetailedServiceItem {
  name: string;
  desc?: string;
  avatar?: string;
}

export interface DetailedBusinessCardCategory {
  title: string;
  services: DetailedServiceItem[];
}

export type DetailedBusinessCard = DetailedBusinessCardCategory[];

export enum ProcessState {
  INIT = 0, // 初始状态
  DRAFT = 1, // 草稿
  AUDITING = 2, // 待审核
  AUDIT_PASS = 3, // 审核通过
  REJECTED = 4, // 审核驳回
  CANCELED = 5, // 已作废
  APPROVED = 6, // 待发布
  PUBLISHING = 7, // 发布中
  PUBLISHED = 8, // 已完成
}

export interface ProcessData {
  state: number;
  scheduleTime?: string;
  publishTime?: string;
  content?: {
    serviceList?: ServiceItem[];
  };
  publishType?: number;
}

/**
 * 按钮可见性配置
 */
export interface ButtonsVisibleConfig {
  confirm: boolean;
  save: boolean;
  cancel: boolean;
  return: boolean;
}

/**
 * 验证结果类型
 */
export interface ValidationResult {
  valid: boolean;
  message: string;
}

/**
 * 删除服务按钮配置类型
 */
export interface DeleteServiceButtonConfig {
  content: string;
  theme: 'default' | 'danger';
}

/**
 * 移动服务操作结果类型
 */
export interface MoveServiceResult {
  updatedData: ServiceData;
  success: boolean;
  message: string;
}

/**
 * API 响应基础类型
 */
export interface ApiResponse<T = unknown> {
  success: boolean;
  error: string | null;
  data?: T;
}

/**
 * 服务发布单 API 响应类型
 */
export interface ServicePublishResponse {
  processId?: string;
  error: string | null;
  isLoading: boolean;
}

/**
 * 最新发布单 API 响应类型
 */
export interface LatestServicePublishResponse {
  process?: {
    processId: string;
    state: number;
    content?: { serviceList?: ServiceItem[] };
  };
  processId?: string;
  isEdit?: boolean;
  state?: number;
  error: string | null;
}

/**
 * 服务加载响应类型
 */
export interface ServiceDataResponse {
  data: ServiceData;
  error: string | null;
  isLoading: boolean;
}

/**
 * 新手状态 API 响应类型
 */
export interface TutorialStatusResponse {
  isFirst: boolean;
  error: string | null;
}
