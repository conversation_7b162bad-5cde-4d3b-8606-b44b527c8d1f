import { request } from '../../../utils/request';
import { DataManager } from '../utils';
import {
  type ServiceData,
  ProcessState,
  type ServiceDataResponse,
  type ServicePublishResponse,
  type LatestServicePublishResponse,
  type TutorialStatusResponse,
  type ApiResponse,
} from './type';
import { MessagePlugin } from 'tdesign-vue-next';
/**
 * 名片服务API服务类
 * 处理所有与后端API交互的逻辑
 */
export class CardServiceApi {
  /**
   * 获取当前服务配置数据
   * @returns Promise<ServiceDataResponse>
   */
  static async fetchCurrentServiceData(): Promise<ServiceDataResponse> {
    try {
      const currentServiceConfig = await request.request({
        url: '/get-service-config',
        method: 'GET',
      });

      if (
        currentServiceConfig &&
        currentServiceConfig.code === 0 &&
        currentServiceConfig.data?.serviceConfig
      ) {
        // 为每个服务生成唯一ID
        const serviceList = currentServiceConfig.data.serviceConfig.serviceList || [];
        const servicesWithIds = DataManager.ensureUniqueIds(serviceList);

        return {
          data: { serviceList: servicesWithIds },
          error: null,
          isLoading: false,
        };
      }
      return {
        data: { serviceList: [] },
        error: null,
        isLoading: false,
      };
    } catch (error) {
      console.error('获取服务数据失败:', error);
      MessagePlugin.error('获取当前服务数据失败');

      return {
        data: { serviceList: [] },
        error: error instanceof Error ? error.message : '获取服务数据失败',
        isLoading: false,
      };
    }
  }

  /**
   * 创建名片服务发布单
   * @returns Promise<ServicePublishResponse> 返回发布单ID
   */
  static async createServicePublishProcess(): Promise<ServicePublishResponse> {
    try {
      const res = await request.request({
        url: '/create-service-publish-process',
        method: 'POST',
      });

      if (res && res.code === 0) {
        return {
          processId: res.data.processId,
          error: null,
          isLoading: false,
        };
      }
      return {
        error: res?.data?.msg || '创建名片服务发布单失败',
        isLoading: false,
      };
    } catch (error) {
      console.error('创建名片服务发布单失败:', error);
      MessagePlugin.error('创建名片服务发布单失败');
      return {
        error: error instanceof Error ? error.message : '创建名片服务发布单失败',
        isLoading: false,
      };
    }
  }

  /**
   * 获取最新的名片服务发布单
   * @returns Promise<LatestServicePublishResponse> 返回最新的发布单
   */
  static async fetchLatestServicePublishProcess(): Promise<LatestServicePublishResponse> {
    try {
      // 调用查询最新的名片服务发布单
      const latestResult = await request.request({
        url: '/get-latest-service-publish-process',
        method: 'GET',
      });

      if (latestResult && latestResult.code === 0 && latestResult.data?.process) {
        const { processId, state } = latestResult.data?.process;

        if (processId) {
          const isEdit = latestResult.data.process.state === ProcessState.INIT;

          return {
            process: latestResult.data.process,
            processId,
            isEdit,
            state,
            error: null,
          };
        }
        // 没有有效的发布单
        return {
          error: null,
        };
      }
      return {
        error: latestResult?.data?.msg || '获取最新名片服务发布单失败',
      };
    } catch (error) {
      MessagePlugin.error('获取最新名片服务发布单失败');

      console.error('获取最新名片服务发布单失败:', error);
      return {
        error: error instanceof Error ? error.message : '获取最新名片服务发布单失败',
      };
    }
  }

  /**
   * 获取最新的新手发布单状态
   * @returns Promise<TutorialStatusResponse> 返回是否是新手
   */
  static async fetchTutorialStatus(): Promise<TutorialStatusResponse> {
    try {
      const res = await request.request({
        url: '/get-latest-tutorial-publish-process',
        method: 'GET',
      });

      if (res && res.code === 0 && res.data?.process) {
        const { processId: id } = res.data?.process;

        // 有id且不是废弃的
        if (id && res.data.process.state !== ProcessState.PUBLISHED) {
          // 不是第一次
          return {
            isFirst: true,
            error: null,
          };
        }
        // 是第一次
        return {
          isFirst: false,
          error: null,
        };
      }
      return {
        isFirst: false,
        error: res?.data?.msg || '获取新手发布单状态失败',
      };
    } catch (error) {
      console.error('获取新手发布单状态失败:', error);
      MessagePlugin.error('获取新手发布单状态失败');

      return {
        isFirst: false,
        error: error instanceof Error ? error.message : '获取新手发布单状态失败',
      };
    }
  }

  /**
   * 提交名片服务发布单
   * @param processId 发布单ID
   * @returns Promise<ApiResponse>
   */
  static async submitServicePublishProcess(processId: string): Promise<ApiResponse> {
    try {
      // 调用确认配置接口
      const result = await request.request({
        url: '/submit-service-publish-process',
        method: 'POST',
        params: {
          processId,
        },
      });

      if (result && result.code === 0) {
        return {
          success: true,
          error: null,
        };
      }
      return {
        success: false,
        error: result?.data?.msg || '确认配置失败，请重试',
      };
    } catch (error) {
      console.error('确认配置时出错:', error);
      MessagePlugin.error('确认配置失败，请重试');

      return {
        success: false,
        error: error instanceof Error ? error.message : '确认配置失败，请重试',
      };
    }
  }

  /**
   * 保存名片服务发布单
   * @param processId 发布单ID
   * @param serviceData 服务数据
   * @returns Promise<ApiResponse>
   */
  static async saveServicePublishProcess(
    processId: string,
    serviceData: ServiceData
  ): Promise<ApiResponse> {
    try {
      // 准备数据
      const saveData = DataManager.prepareDataForSave(serviceData);

      // 调用保存接口
      const result = await request.request({
        url: '/save-service-publish-process',
        method: 'POST',
        params: { processId, content: saveData },
      });

      if (result && result.code === 0) {
        return {
          success: true,
          error: null,
        };
      }
      return {
        success: false,
        error: result?.data?.msg || '保存失败，请重试',
      };
    } catch (error) {
      console.error('保存数据时出错:', error);
      MessagePlugin.error('保存失败，请重试');

      return {
        success: false,
        error: error instanceof Error ? error.message : '保存失败，请重试',
      };
    }
  }

  /**
   * 撤销名片服务发布单
   * @param processId 发布单ID
   * @returns Promise<ApiResponse>
   */
  static async cancelServicePublishProcess(processId: string): Promise<ApiResponse> {
    try {
      // 调用撤销配置接口
      const result = await request.request({
        url: '/cancel-service-publish-process',
        method: 'POST',
        params: { processId },
      });

      if (result && result.code === 0) {
        return {
          success: true,
          error: null,
        };
      }
      return {
        success: false,
        error: result?.data?.msg || '撤销配置失败，请重试',
      };
    } catch (error) {
      console.error('撤销配置时出错:', error);
      MessagePlugin.error('撤销配置失败，请重试');

      return {
        success: false,
        error: error instanceof Error ? error.message : '撤销配置失败，请重试',
      };
    }
  }
}
