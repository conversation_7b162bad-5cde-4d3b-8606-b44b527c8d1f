<template>
  <t-dialog
    :visible="dialogVisible"
    :header="isModify ? '修改服务分类' : '添加服务分类'"
    width="960px"
    :footer="false"
    :close-on-overlay-click="false"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="add-category-dialog">
      <t-form
        id="categoryForm"
        ref="categoryForm"
        :data="formData"
        :rules="rules"
        label-align="left"
        label-width="20%"
        @validate="onValidate"
        @submit="handleConfirm"
      >
        <t-form-item label="分类" name="categoryName">
          <t-input
            v-model="formData.categoryName"
            :placeholder="CONST.MSG.CATEGORY_NAME_PLACEHOLDER"
          />
        </t-form-item>
        <div class="dialog-footer">
          <t-button variant="outline" @click="handleCancel"> 取消 </t-button>
          <t-button
            :theme="isValid ? 'primary' : 'default'"
            :disabled="!isValid"
            type="submit"
            form="categoryForm"
          >
            {{ isModify ? '确认修改' : '确认添加' }}
          </t-button>
        </div>
      </t-form>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch, computed } from 'vue';
import {
  Dialog as TDialog,
  Form as TForm,
  FormItem as TFormItem,
  Input as TInput,
  Button as TButton,
  ValidateTriggerType,
} from 'tdesign-vue-next';
import { validateCategoryName, containsEmoji } from '../utils';
import { CONST } from '../utils/constants';

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  isModify: {
    type: Boolean,
    default: false,
  },
  editCategoryName: {
    type: String,
    default: '',
  },
  existingCategories: {
    type: Array as () => string[],
    default: () => [],
  },
});

const emit = defineEmits(['update:visible', 'cancel', 'confirm']);

// 使用计算属性处理v-model
const dialogVisible = computed(() => props.visible);

// 表单数据
const formData = reactive({
  categoryName: '',
});

const isValid = ref(false);
const categoryForm = ref<InstanceType<typeof TForm> | null>(null);

const checkCategoryName = (name: string): void => {
  const result = validateCategoryName(name);
  isValid.value = result.valid;
};

// 表单验证规则
const rules = {
  categoryName: [
    {
      required: true,
      message: '分类名称必填',
      type: 'error' as const,
      trigger: 'input' as ValidateTriggerType,
      validator: (val: string) => {
        if (!val || val.trim() === '') {
          isValid.value = false;
          return false;
        }
        return true;
      },
    },
    {
      validator: (val: string) => {
        if (containsEmoji(val)) {
          isValid.value = false;
          return false;
        }
        return true;
      },
      message: CONST.MSG.CATEGORY_NAME_EMOJI,
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
    {
      validator: (val: string) => {
        if (val && val.replace(/\s/g, '').length > 6) {
          isValid.value = false;
          return false;
        }
        return true;
      },
      message: CONST.MSG.CATEGORY_NAME_TOO_LONG,
      type: 'error' as const,
      trigger: 'change' as ValidateTriggerType,
    },
    {
      validator: (val: string) => {
        if (props.isModify && val === props.editCategoryName) {
          return true;
        }

        const exists = props.existingCategories.some(
          (name) => name === val && name !== props.editCategoryName
        );

        if (exists) {
          isValid.value = false;
          return false;
        }
        return true;
      },
      message: CONST.MSG.CATEGORY_EXISTS,
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
  ],
};

// 表单验证回调
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onValidate = (result: any) => {
  if (result.validateResult === true) {
    console.log('验证成功');
    checkCategoryName(formData.categoryName);
  } else {
    console.log('验证失败:', result.firstError);
    isValid.value = false;
  }
};

// 取消添加
const handleCancel = () => {
  formData.categoryName = '';
  isValid.value = false;
  emit('cancel');
  emit('update:visible', false);
};

// 确认添加或修改
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleConfirm = (context: any) => {
  if (context.validateResult !== true) {
    isValid.value = false;
    return;
  }

  const validation = validateCategoryName(formData.categoryName);
  if (!validation.valid) {
    isValid.value = false;
    return;
  }

  const name = formData.categoryName.trim();
  emit('confirm', name);
  emit('update:visible', false);
  formData.categoryName = '';
  isValid.value = false;
};

// 监听分类名称变化，实时更新isValid
watch(
  () => formData.categoryName,
  (newVal) => {
    if (!newVal || newVal.trim() === '' || containsEmoji(newVal) || newVal.length > 6) {
      isValid.value = false;
      return;
    }
    // 通过validateCategoryName进行完整校验
    checkCategoryName(newVal);
  }
);

watch(
  () => props.visible,
  (newValue) => {
    if (newValue) {
      // 修改
      if (props.isModify && props.editCategoryName) {
        formData.categoryName = props.editCategoryName;
        checkCategoryName(formData.categoryName);
      } else {
        // 添加
        formData.categoryName = '';
        isValid.value = false;
      }
    }
  }
);
</script>

<!-- 添加默认导出 -->
<script lang="ts">
export default {
  name: 'AddCategoryDialog',
};
</script>

<style scoped>
@import '../style/index.css';
</style>
