<template>
  <t-dialog
    class="card-service-dialog"
    :visible="dialogVisible"
    :header="isModify ? '修改服务' : '添加服务'"
    width="960px"
    :footer="false"
    :close-on-overlay-click="false"
    @update:visible="(val) => emit('update:visible', val)"
  >
    <div class="add-service-dialog">
      <t-form
        id="serviceForm"
        ref="serviceForm"
        :data="serviceData"
        :rules="rules"
        label-align="left"
        label-width="20%"
        @validate="onValidate"
        @submit="handleConfirm"
      >
        <t-form-item label="服务名称" name="serviceName" class="w95">
          <t-input
            v-model="serviceData.serviceName"
            :placeholder="CONST.MSG.SERVICE_NAME_PLACEHOLDER"
          />
        </t-form-item>

        <t-form-item label="服务链接类型">
          <div class="radio-group">
            <t-radio-group v-model="serviceData.jumpInfo.jumpType">
              <t-radio :value="1"> 小程序 </t-radio>
              <t-radio :value="4"> 网页 </t-radio>
            </t-radio-group>
          </div>
        </t-form-item>

        <template v-if="serviceData.jumpInfo.jumpType === 1">
          <t-form-item label="跳转小程序" name="miniProgramAppid">
            <t-select
              v-model="serviceData.jumpInfo.miniProgram.appid"
              placeholder="选择已关联的AppID"
              clearable
              filterable
              :loading="singleLoading"
              class="w95"
              @search="remoteMethodSingle"
              @popup-visible-change="handlePopupVisibleChange"
              @change="
                (val) => {
                  checkDuplicateMiniProgram();
                  updateFormValidity();
                }
              "
            >
              <template #empty>
                <span v-if="searchKeyword" class="empty">
                  无相关结果，如需添加请前往<a
                    href="https://pay.weixin.qq.com/xdc/brandweb/index/appid/appid-manage"
                    target="_blank"
                    >AppID管理</a
                  >
                </span>
                <span v-else class="empty">
                  暂无数据，如需添加请前往<a
                    href="https://pay.weixin.qq.com/xdc/brandweb/index/appid/appid-manage"
                    target="_blank"
                    >AppID管理</a
                  >
                </span>
              </template>
              <t-option
                v-for="item in filteredMiniprogramList"
                :key="item.appid"
                :value="item.appid"
                :label="item.appName"
              >
                <template #default>
                  <div>
                    <div v-html="highlightKeyword(item.appName)" />
                    <div class="appid-text">
                      AppID: <span v-html="highlightKeyword(item.appid)" />
                    </div>
                  </div>
                </template>
              </t-option>
            </t-select>
            <div class="form-tip">
              如需添加未关联的AppID，请前往<a
                href="https://pay.weixin.qq.com/xdc/brandweb/index/appid/appid-manage"
                target="_blank"
                >AppID管理</a
              >
            </div>
          </t-form-item>

          <t-form-item
            label="跳转路径"
            name="miniProgramPath"
            :class="{ 't-is-error': miniProgramPathInvalid }"
            :tips="
              miniProgramPathInvalid
                ? CONST.MSG.MINIPROGRAM_PATH_START_SLASH
                : miniProgramDuplicateError
                  ? CONST.MSG.DUPLICATE_MINIPROGRAM
                  : ''
            "
            :status="
              miniProgramPathInvalid || miniProgramDuplicateError ? 'error' : undefined
            "
          >
            <t-input
              v-model="serviceData.jumpInfo.miniProgram.path"
              placeholder="输入跳转路径"
              class="w95"
              @input="checkMiniProgramPath"
              @change="
                () => {
                  checkDuplicateMiniProgram();
                  updateFormValidity();
                }
              "
            />
            <div class="form-tip">用户点击该服务会跳转到当前路径的小程序</div>
          </t-form-item>
        </template>

        <template v-else>
          <t-form-item
            label="跳转链接"
            name="webPath"
            class="w95"
            :tips="
              webPathInvalid
                ? CONST.MSG.INVALID_WEB_URL
                : webPathDuplicateError
                  ? CONST.MSG.DUPLICATE_WEB_PATH
                  : ''
            "
            :class="{ 't-is-error': webPathInvalid }"
            :status="webPathInvalid || webPathDuplicateError ? 'error' : undefined"
          >
            <t-input
              v-model="serviceData.jumpInfo.web.path"
              placeholder="输入跳转链接"
              @input="checkWebPath"
              @blur="checkWebPath"
              @change="
                () => {
                  checkDuplicateWebPath();
                  updateFormValidity();
                }
              "
            />
            <div class="form-tip">用户点击该服务会跳转到当前网页链接</div>
          </t-form-item>
        </template>
      </t-form>
    </div>
    <div class="dialog-footer">
      <t-button variant="outline" @click="handleCancel"> 取消 </t-button>
      <t-button
        :theme="isValid ? 'primary' : 'default'"
        :disabled="!isValid"
        @click="handleConfirm({ validateResult: true })"
      >
        {{ isModify ? '确认修改' : '确认添加' }}
      </t-button>
    </div>
  </t-dialog>
</template>

<script setup lang="ts">
import { reactive, computed, watch, ref, defineOptions } from 'vue';
import { request } from '../../../utils/request';

import { type ServiceItem } from '../utils/type';
import {
  Dialog as TDialog,
  Form as TForm,
  FormItem as TFormItem,
  Input as TInput,
  Radio as TRadio,
  RadioGroup as TRadioGroup,
  Select as TSelect,
  Option as TOption,
  Button as TButton,
  ValidateTriggerType,
} from 'tdesign-vue-next';
import {
  containsEmoji,
  validateServiceName,
  validateMiniProgramPath,
  validateWebUrl,
} from '../utils';
import { CONST } from '../utils/constants';

defineOptions({
  name: 'AddServiceDialog',
});

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  categoryName: {
    type: String,
    required: true,
  },
  isModify: {
    type: Boolean,
    default: false,
  },
  editService: {
    type: Object as () => ServiceItem | null,
    default: null,
  },
  existingServices: {
    type: Array as () => Array<{
      name: string;
      originalService?: {
        serviceId: string;
        jumpInfo?: {
          jumpType: number;
          miniProgram?: { appid: string; path: string };
          web?: { path: string };
        };
      };
    }>,
    default: () => [],
  },
});

const emit = defineEmits(['update:visible', 'cancel', 'confirm']);

// 使用计算属性处理v-model
const dialogVisible = computed(() => props.visible);
const singleLoading = ref(false);

// 校验状态
const isValid = ref(false);
const serviceForm = ref<InstanceType<typeof TForm> | null>(null);
const miniProgramPathInvalid = ref(false);
const webPathInvalid = ref(false);
const miniProgramDuplicateError = ref(false);
const webPathDuplicateError = ref(false);

// 创建新服务对象
const createEmptyService = () => ({
  serviceId:
    props.isModify && props.editService
      ? props.editService.serviceId
      : Date.now().toString(),
  serviceName: '',
  serviceClassifyName: props.categoryName,
  jumpInfo: {
    jumpType: 1, // 默认为小程序
    miniProgram: {
      appid: '',
      path: '',
    },
    web: {
      path: '',
    },
  },
});

const serviceData = reactive(createEmptyService());

// 校验服务名称并更新isValid
const checkServiceName = (name: string): void => {
  const result = validateServiceName(name);
  updateFormValidity();
};

const checkMiniProgramPath = () => {
  const result = validateMiniProgramPath(serviceData.jumpInfo.miniProgram.path || '');
  miniProgramPathInvalid.value = !result.valid;
  checkDuplicateMiniProgram();
  updateFormValidity();
};

const checkWebPath = () => {
  const result = validateWebUrl(serviceData.jumpInfo.web.path || '');
  webPathInvalid.value = !result.valid;
  checkDuplicateWebPath();
  updateFormValidity();
};

// 校验小程序appid+路径是否重复
const checkDuplicateMiniProgram = () => {
  const currentAppid = serviceData.jumpInfo.miniProgram.appid;
  const currentPath = serviceData.jumpInfo.miniProgram.path;

  // 如果appid或path为空，不进行重复校验
  if (!currentAppid || !currentPath) {
    miniProgramDuplicateError.value = false;
    updateFormValidity();
    return;
  }

  // 查找现有服务中是否有相同的appid和path组合
  const duplicate = props.existingServices.some((service) => {
    // 如果是修改模式，跳过自身
    if (
      props.isModify &&
      props.editService &&
      service.originalService?.serviceId === props.editService.serviceId
    ) {
      return false;
    }

    const jumpInfo = service.originalService?.jumpInfo;
    // 只有当jumpType为1(小程序)且appid和path都匹配时，才算重复
    return (
      jumpInfo?.jumpType === 1 &&
      jumpInfo.miniProgram?.appid === currentAppid &&
      jumpInfo.miniProgram?.path === currentPath
    );
  });

  miniProgramDuplicateError.value = duplicate;
  updateFormValidity();
};

// 校验网页链接是否重复
const checkDuplicateWebPath = () => {
  const currentPath = serviceData.jumpInfo.web.path;

  // 如果path为空，不进行重复校验
  if (!currentPath) {
    webPathDuplicateError.value = false;
    updateFormValidity();
    return;
  }

  // 查找现有服务中是否有相同的网页路径
  const duplicate = props.existingServices.some((service) => {
    // 如果是修改模式，跳过自身
    if (
      props.isModify &&
      props.editService &&
      service.originalService?.serviceId === props.editService.serviceId
    ) {
      return false;
    }

    const jumpInfo = service.originalService?.jumpInfo;
    // 只有当jumpType为4(网页)且path匹配时，才算重复
    return jumpInfo?.jumpType === 4 && jumpInfo.web?.path === currentPath;
  });

  webPathDuplicateError.value = duplicate;
  updateFormValidity();
};

// 更新表单有效性
const updateFormValidity = () => {
  // 校验服务名称
  const nameValidation = validateServiceName(serviceData.serviceName);

  // 检查必填字段是否都已填写
  const hasServiceName = !!serviceData.serviceName.trim();

  // 根据跳转类型检查不同的必填字段
  let hasRequiredFields = false;
  if (serviceData.jumpInfo.jumpType === 1) {
    // 小程序类型：需要检查appid和path
    hasRequiredFields =
      !!serviceData.jumpInfo.miniProgram.appid && !!serviceData.jumpInfo.miniProgram.path;
  } else {
    // 网页类型：需要检查web.path
    hasRequiredFields = !!serviceData.jumpInfo.web.path;
  }

  // 更新表单有效性状态 - 考虑重复性校验结果和所有必填字段
  isValid.value =
    nameValidation.valid &&
    hasServiceName &&
    hasRequiredFields &&
    !miniProgramPathInvalid.value &&
    !webPathInvalid.value &&
    !miniProgramDuplicateError.value &&
    !webPathDuplicateError.value;
};

// 小程序列表数据
const miniprogramList = ref<Array<{ appid: string; appName: string }>>([]);
// 过滤后的小程序列表
const filteredMiniprogramList = ref<Array<{ appid: string; appName: string }>>([]);
// 搜索关键词
const searchKeyword = ref('');

// 获取当前服务数据
const fetchMiniprogram = async () => {
  try {
    const res = await request.request({
      url: '/query-brand-miniprogram-list',
      method: 'GET',
    });
    if (res.code === 0 && res.data?.brandAppidRelationList) {
      miniprogramList.value = res.data.brandAppidRelationList;
      filteredMiniprogramList.value = [...miniprogramList.value];
    }
  } catch (error) {
    console.log('error', error);
  }
};

// 高亮搜索关键词
const highlightKeyword = (text: string) => {
  if (!searchKeyword.value) return text;

  const regex = new RegExp(`(${searchKeyword.value})`, 'gi');

  return text.replace(regex, '<span class="highlight-keyword">$1</span>');
};

// 远程搜索方法
const remoteMethodSingle = (search: string) => {
  searchKeyword.value = search;
  singleLoading.value = true;

  setTimeout(() => {
    if (search) {
      filteredMiniprogramList.value = miniprogramList.value.filter(
        (item) =>
          item.appName.toLowerCase().includes(search.toLowerCase()) ||
          item.appid.toLowerCase().includes(search.toLowerCase())
      );
    } else {
      filteredMiniprogramList.value = [...miniprogramList.value];
    }
    singleLoading.value = false;
  }, 300);
};

// 处理下拉框显示/隐藏状态变化
const handlePopupVisibleChange = (visible: boolean) => {
  // 当下拉框关闭时，重置搜索状态
  if (!visible) {
    searchKeyword.value = '';
    filteredMiniprogramList.value = [...miniprogramList.value];
  }
};

// 如果是修改模式且有传入editService，则初始化表单数据
watch(
  () => [props.visible, props.isModify, props.editService],
  () => {
    fetchMiniprogram();
    isValid.value = false;
    miniProgramPathInvalid.value = false;
    webPathInvalid.value = false;
    miniProgramDuplicateError.value = false;
    webPathDuplicateError.value = false;

    if (props.visible && props.isModify && props.editService) {
      // 深拷贝编辑的服务数据
      const editData = JSON.parse(JSON.stringify(props.editService));
      console.log('当前修改的服务editData', props.editService);

      if (editData.jumpInfo) {
        editData.jumpInfo.jumpType = editData.jumpInfo.jumpType === 1 ? 1 : 4;
      }

      // 将编辑数据赋值给表单
      Object.assign(serviceData, editData);

      // 修改模式下，需要验证初始数据
      setTimeout(() => {
        checkServiceName(serviceData.serviceName);
        if (serviceData.jumpInfo.jumpType === 1) {
          checkMiniProgramPath();
          checkDuplicateMiniProgram();
        } else {
          checkWebPath();
          checkDuplicateWebPath();
        }
        updateFormValidity();
      }, 0);
    } else if (props.visible && !props.isModify) {
      // 添加模式，重置表单
      resetForm();
    }
  },
  { immediate: true }
);

// 监听服务名称变化，实时更新isValid
watch(
  () => serviceData.serviceName,
  (newVal) => {
    checkServiceName(newVal);
  }
);

// 监听跳转类型变化
watch(
  () => serviceData.jumpInfo.jumpType,
  (newType) => {
    // 切换类型时重置对应的错误状态
    if (newType === 1) {
      webPathDuplicateError.value = false;
      webPathInvalid.value = false;
      setTimeout(() => checkDuplicateMiniProgram(), 0);
    } else {
      miniProgramDuplicateError.value = false;
      miniProgramPathInvalid.value = false;
      setTimeout(() => checkDuplicateWebPath(), 0);
    }
    updateFormValidity();
  }
);

// 表单验证规则
const rules = {
  serviceName: [
    {
      required: true,
      message: '服务名称必填',
      type: 'error' as const,
      trigger: 'input' as ValidateTriggerType,
    },
    {
      validator: (val: string) => {
        if (containsEmoji(val)) {
          isValid.value = false;
          return false;
        }
        return true;
      },
      message: CONST.MSG.SERVICE_NAME_EMOJI,
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
    {
      validator: (val: string) => {
        if (val && val.replace(/\s/g, '').length > 8) {
          isValid.value = false;
          return false;
        }
        return true;
      },
      message: CONST.MSG.SERVICE_NAME_TOO_LONG,
      type: 'error' as const,
      trigger: 'change' as ValidateTriggerType,
    },
    {
      validator: (val: string) => {
        if (
          props.isModify &&
          props.editService &&
          val === props.editService.serviceName
        ) {
          return true;
        }

        const exists = props.existingServices.some(
          (service) =>
            service.name === val &&
            (!props.editService || service.name !== props.editService.serviceName)
        );

        if (exists) {
          isValid.value = false;
          return false;
        }
        return true;
      },
      message: '服务名称已存在',
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
  ],
  miniProgramPath: [
    {
      validator: (val: string) => {
        if (!val) return true;
        const result = validateMiniProgramPath(val);
        miniProgramPathInvalid.value = !result.valid;
        return result.valid;
      },
      message: CONST.MSG.MINIPROGRAM_PATH_START_SLASH,
      trigger: 'input' as ValidateTriggerType,
      type: 'error' as const,
    },
  ],
  miniProgramAppid: [
    {
      validator: () => {
        checkDuplicateMiniProgram();
        return !miniProgramDuplicateError.value;
      },
      message: CONST.MSG.DUPLICATE_MINIPROGRAM,
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
  ],
  webPath: [
    {
      required: true,
      message: '跳转链接必填',
      type: 'error' as const,
      trigger: 'input' as ValidateTriggerType,
    },
    {
      validator: (val: string) => {
        if (!val) return true;
        const result = validateWebUrl(val);
        webPathInvalid.value = !result.valid;
        return result.valid;
      },
      message: CONST.MSG.INVALID_WEB_URL,
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
    {
      validator: () => {
        checkDuplicateWebPath();
        return !webPathDuplicateError.value;
      },
      message: CONST.MSG.DUPLICATE_WEB_PATH,
      trigger: 'change' as ValidateTriggerType,
      type: 'error' as const,
    },
  ],
};

// 表单验证回调
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const onValidate = (result: any) => {
  if (result.validateResult === true) {
    console.log('验证成功');
    updateFormValidity();
  } else {
    console.log('验证失败:', result.firstError);
    isValid.value = false;
  }
};

// 取消添加
const handleCancel = () => {
  emit('cancel');
  emit('update:visible', false);
  resetForm();
  isValid.value = false;
  miniProgramPathInvalid.value = false;
  webPathInvalid.value = false;
  miniProgramDuplicateError.value = false;
  webPathDuplicateError.value = false;
};

// 确认添加
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleConfirm = (context: any) => {
  if (context.validateResult !== true) {
    console.log('验证失败:', context.firstError);
    return;
  }

  // 手动触发验证
  if (serviceData.jumpInfo.jumpType === 1) {
    // 小程序路径验证
    const result = validateMiniProgramPath(serviceData.jumpInfo.miniProgram.path || '');
    miniProgramPathInvalid.value = !result.valid;
    checkDuplicateMiniProgram();
    if (!result.valid || miniProgramDuplicateError.value) {
      return;
    }
  } else {
    // 网页链接验证
    const result = validateWebUrl(serviceData.jumpInfo.web.path || '');
    webPathInvalid.value = !result.valid;
    checkDuplicateWebPath();
    if (!result.valid || webPathDuplicateError.value) {
      return;
    }
  }

  // 更新有效性状态
  updateFormValidity();
  if (!isValid.value) {
    return;
  }

  // 复制当前服务数据
  const newService = JSON.parse(JSON.stringify(serviceData));

  // 添加分类名称
  newService.serviceClassifyName = props.categoryName;

  newService.jumpInfo.jumpType = newService.jumpInfo.jumpType === 1 ? 1 : 4;

  emit('confirm', newService);
  emit('update:visible', false);
  resetForm();
  isValid.value = false;
  miniProgramPathInvalid.value = false;
  webPathInvalid.value = false;
  miniProgramDuplicateError.value = false;
  webPathDuplicateError.value = false;
};

// 重置表单
const resetForm = () => {
  Object.assign(serviceData, createEmptyService());
  isValid.value = false;
};
</script>

<!-- 添加默认导出 -->
<script lang="ts">
export default {
  name: 'AddServiceDialog',
};
</script>

<style scoped>
@import '../style/index.css';
.card-service-dialog {
  background-color: #000 !important;
}
.card-service-dialog /deep/ .t-dialog {
  background-color: red;
  height: 560px !important;
}
</style>

<style>
.card-service-dialog .t-dialog {
  height: 560px !important;
}
.card-service-dialog .t-dialog--default {
  padding: 32px !important;
}
.card-service-dialog .t-dialog__body {
  height: 100%;
}

.card-service-dialog .add-service-dialog {
  height: 80%;
}
</style>
