<template>
  <!-- {{ buttonsVisible }} {{ isfirst }} {{ isDataChanged }} {{ state }} -->
  <div class="bottom-actions" :class="{ 'bottom-actions-hidden': isDragging }">
    <t-button
      v-if="buttonsVisible.confirm"
      class="confirm-btn"
      :theme="
        (state === 4 ? isDataChanged && serviceCount >= 2 : serviceCount >= 2)
          ? 'primary'
          : 'default'
      "
      :disabled="state === 4 ? !isDataChanged || serviceCount < 2 : serviceCount < 2"
      @click="handleConfirm"
    >
      确认配置
    </t-button>
    <t-button
      v-if="buttonsVisible.save"
      class="save-btn"
      theme="default"
      variant="outline"
      :disabled="!isDataChanged"
      @click="handleSave"
    >
      保存
    </t-button>
    <t-popconfirm
      theme="default"
      :visible="showCancelConfirm"
      overlay-class-name="custom-popconfirm-container"
      content="撤销配置后，本次修改内容将不再保存"
      :confirm-btn="{
        content: '确认撤销',
        theme: 'danger',
      }"
      cancel-btn="取消"
      placement="top"
      @visibleChange="onCancelConfirmChange"
    >
      <t-button
        v-if="buttonsVisible.cancel"
        class="cancel-btn"
        theme="default"
        variant="outline"
        @click="showCancelPopconfirm($event)"
      >
        撤销配置
      </t-button>
    </t-popconfirm>
    <t-button
      v-if="buttonsVisible.return"
      class="return-btn"
      theme="default"
      variant="outline"
      @click="handleBack"
    >
      返回
    </t-button>
  </div>
</template>

<script setup lang="ts">
import {
  defineProps,
  defineEmits,
  defineOptions,
  withDefaults,
  ref,
  computed,
  onMounted,
  onUnmounted,
} from 'vue';

import {
  Button as TButton,
  MessagePlugin,
  Popconfirm as TPopconfirm,
  type PopconfirmVisibleChangeContext,
} from 'tdesign-vue-next';
import { useRoute, useRouter } from 'vue-router';
import { CardServiceApi } from '../utils/service';
import type { NormalizedCategory, ServiceData } from '../utils/type';

defineOptions({
  name: 'BottomActions',
});

const router = useRouter();
const route = useRoute();
const showCancelConfirm = ref(false);
const isConfigPreview = ref(route.params.type || 'preview');

// 点击外部关闭弹窗的处理函数
const handleOutsideClick = (e: MouseEvent) => {
  // 获取当前点击的元素
  const target = e.target as HTMLElement;

  // 只有当点击的是弹窗内部元素时不关闭弹窗
  if (target.closest('.t-popconfirm')) {
    return;
  }

  // 检查并关闭弹窗
  if (showCancelConfirm.value) {
    showCancelConfirm.value = false;
  }
};

// 在组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener('click', handleOutsideClick);
});

// 在组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick);
});

// 计算所有服务的数量
const serviceCount = computed(() =>
  props.normalizedServiceData.reduce((sum, category) => sum + category.services.length, 0)
);

interface ButtonsVisibleConfig {
  confirm: boolean;
  save: boolean;
  cancel: boolean;
  return: boolean;
}

const props = withDefaults(
  defineProps<{
    isEdit: boolean;
    isfirst: boolean;
    state: number;
    isDragging: boolean;
    isDataChanged: boolean;
    isPreview: boolean;
    processId: string;
    normalizedServiceData: NormalizedCategory[];
    internalData: ServiceData;
    buttonsVisible?: ButtonsVisibleConfig;
  }>(),
  {
    buttonsVisible: () => ({
      confirm: false,
      save: false,
      cancel: false,
      return: false,
    }),
  }
);
const emit = defineEmits<{
  'update-data': [updatedData: ServiceData];
  'fetch-data': [];
  'reset-data-changed': [];
  'cancel-success': [];
  'back-preview': [];
}>();
// 确认按钮点击事件处理
const handleConfirm = async () => {
  // 检查是否存在为空的分类
  const emptyCategories = checkEmptyCategories();
  if (emptyCategories.length > 0) {
    // 显示提示信息，告知用户需要移除空分类
    const categoryNames = emptyCategories.join('、');
    MessagePlugin.warning(`请先移除没有配置服务的分类：${categoryNames}`);
    return;
  }

  try {
    // 如果数据已更改，先调用保存接口
    if (props.isDataChanged) {
      const saveResult = await CardServiceApi.saveServicePublishProcess(
        props.processId,
        props.internalData
      );

      if (!saveResult.success) {
        MessagePlugin.error(saveResult.error || '保存失败，无法提交审核');
        return;
      }
    }

    if (isConfigPreview.value === 'preview') {
      const saveResult = await CardServiceApi.saveServicePublishProcess(
        props.processId,
        props.internalData
      );

      if (!saveResult.success) {
        MessagePlugin.error(saveResult.error || '保存失败，无法提交审核');
        return;
      }
      router.push({
        path: `/home/<USER>/common-publish/${props.processId}`,
        query: {
          source: 'serviceConfig',
        },
      });
    } else {
      // 调用确认配置接口
      const result = await CardServiceApi.submitServicePublishProcess(props.processId);

      if (result.success) {
        console.log('确认配置成功，是新手吗？', props.isfirst, isConfigPreview.value);

        // 数据已保存，重置变更状态
        emit('reset-data-changed');
        // 老手
        router.push({
          path: `/home/<USER>/service-state/service/${props.processId}`,
        });
      } else {
        MessagePlugin.error(result.error || '确认配置失败，请重试');
      }
    }
  } catch (error) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] 确认配置时出错:`, error);
    MessagePlugin.error('确认配置失败，请重试');
  }
};

// 保存按钮点击事件处理
const handleSave = async () => {
  // 移除对空分类的校验，允许保存包含空分类的配置
  try {
    // 调用保存接口
    const result = await CardServiceApi.saveServicePublishProcess(
      props.processId,
      props.internalData
    );

    if (result.success) {
      MessagePlugin.success('保存成功');
    } else {
      MessagePlugin.error(result.error || '保存失败，请重试');
    }
  } catch (error) {
    console.error('保存数据时出错:', error);
    MessagePlugin.error('保存失败，请重试');
  }
};

// 检查是否存在没有实际服务的分类，返回空分类的名称列表
const checkEmptyCategories = (): string[] => {
  console.log('开始检查分类服务情况：', props.normalizedServiceData);
  const emptyCategories: string[] = [];

  for (const category of props.normalizedServiceData) {
    if (category.name === '无分类') {
      continue;
    }

    // 检查该分类是否有服务
    if (category.services.length === 0) {
      emptyCategories.push(category.name);
      continue;
    }

    // 检查该分类是否只包含empty_服务
    const hasRealService = category.services.some(
      (service) => service.originalService?.serviceName
    );

    if (!hasRealService) {
      emptyCategories.push(category.name);
    }
  }

  return emptyCategories;
};

const showCancelPopconfirm = (e: MouseEvent) => {
  e.stopPropagation(); // 阻止事件冒泡
  showCancelConfirm.value = true;
};

const onCancelConfirmChange = (
  visible: boolean,
  context?: PopconfirmVisibleChangeContext
) => {
  if (context && context.trigger === 'confirm') {
    handleCancel();
  } else if (context && context.trigger === 'cancel') {
    showCancelConfirm.value = false;
  }
};

// 撤销配置按钮点击事件处理
const handleCancel = async () => {
  showCancelConfirm.value = false;

  try {
    // 调用撤销配置接口
    const result = await CardServiceApi.cancelServicePublishProcess(props.processId);

    if (result.success) {
      MessagePlugin.success('撤销配置成功');
      emit('cancel-success');
    } else {
      MessagePlugin.error(result.error || '撤销配置失败，请重试');
    }
  } catch (error) {
    console.error('撤销配置时出错:', error);
    MessagePlugin.error('撤销配置失败，请重试');
  }
};

// 返回按钮逻辑
const handleBack = () => {
  if (isConfigPreview.value === 'preview') {
    router.push({
      path: `/home/<USER>/service-config/preview`,
    });
    emit('back-preview');
  } else {
    router.push({
      path: `/home/<USER>/card-detail`,
    });
  }
};
</script>

<!-- 添加默认导出 -->
<script lang="ts">
export default {
  name: 'BottomActions',
};
</script>

<style scoped>
@import '../style/index.css';
</style>
