<template>
  <!-- state:{{ state }}---show:{{ show }}---isAlertClosed:{{ isAlertClosed }}---auditReason:{{
    auditReason
  }}
  --- {{ publishType }} --- {{ scheduleTime }} --- {{ auditPassTime }} --- {{ type }} ---
  {{ isPreviewMode }} --- {{ isServiceDetail }} -->
  <div
    v-if="
      state !== null &&
      state !== 0 &&
      show &&
      (state !== ProcessState.PUBLISHED || !isAlertClosed) &&
      !hide
    "
    class="status-alert"
    :class="[
      getStatusClass(state),
      { 'status-config': props.type === 'config' || props.type === 'configView' },
    ]"
  >
    <div v-if="state !== null && getStatusIcon(state) !== ''" class="status-icon">
      <img :src="getStatusIcon(state)" />
    </div>
    <div class="status-content" :class="{ 'status-content-reject': showAuditReason }">
      <!-- 草稿 -->
      <template v-if="state === ProcessState.DRAFT">
        {{ statusText || getDefaultText() }}
        <a class="status-link" @click="viewEdit" v-if="statusAction">{{
          statusAction
        }}</a>
      </template>
      <!-- 审核中 -->
      <template v-else-if="state === ProcessState.AUDITING">
        {{ statusText || `${getDefaultText()}` }}

        <span v-if="scheduleTime && publishType === 2">
          ，已设置{{ formatTimestamp(scheduleTime) }}定时发布</span
        >
        <a
          v-if="statusAction && !isServiceDetail"
          class="status-link"
          @click="handleAction(clickDetail ? 'edit-publish' : 'view-details')"
        >
          {{ clickDetail ? editPublishText : statusAction }}
        </a>
      </template>
      <!-- 驳回 -->
      <template v-else-if="state === ProcessState.REJECTED">
        <div class="reject-content">
          <div>{{ statusText || getDefaultText() }}</div>
          <div v-if="auditReason && showAuditReason" class="audit-reason">
            <p v-for="(line, index) in auditReasons" :key="index">
              {{ line }}
            </p>
          </div>
        </div>
        <span
          class="status-link"
          @click="viewRejected"
          v-if="statusAction && !clickDetail"
        >
          {{ statusAction }}
        </span>
      </template>
      <!-- 审核通过 3 7 -->
      <template v-else-if="isApproved(state)">
        {{ statusText || type === 'service' ? '名片服务' : '基础信息' }}
        <span v-if="scheduleTime && scheduleTime !== '0' && publishType === 2">
          审核通过，将于{{ formatTimestamp(scheduleTime) }}定时发布</span
        >
        <span v-if="auditPassTime && auditPassTime !== '0' && publishType === 1">
          已于{{ formatTimestamp(auditPassTime, true) }}审核通过，审核通过后自动发布</span
        >
        <span v-if="auditPassTime && auditPassTime !== '0' && publishType === 0">
          已于{{ formatTimestamp(auditPassTime, true) }}审核通过</span
        >
        <span v-if="auditPassTime && auditPassTime === '0'"> 审核通过</span>
        <!-- 发布中没有修改发布设置按钮 -->
        <a
          v-if="statusAction && state !== ProcessState.PUBLISHING && !isServiceDetail"
          class="status-link"
          @click="handleAction(clickDetail ? 'edit-publish' : 'view-details')"
        >
          {{ clickDetail ? editPublishText : statusAction }}
        </a>
      </template>
      <!-- 待发布6 -->
      <template v-else-if="state === ProcessState.APPROVED">
        {{ statusText || getDefaultText() }}
        <!-- 定时发布scheduleTime有效 -->
        <span v-if="scheduleTime && scheduleTime !== '0' && publishType === 2"
          >审核通过，将于{{ formatTimestamp(scheduleTime) }}定时发布</span
        >
        <!-- 立即发布且auditPassTime有效 -->
        <span v-if="auditPassTime && auditPassTime !== '0' && publishType === 1">
          已于{{ formatTimestamp(auditPassTime, true) }}审核通过，审核通过后自动发布</span
        >
        <!-- 0 且auditPassTime有效 -->

        <span v-if="auditPassTime && auditPassTime !== '0' && publishType === 0">
          已于{{ formatTimestamp(auditPassTime, true) }}审核通过，审核通过后自动发布</span
        >
        <!-- 不管publishType是什么 auditPassTime 为 0 都显示审核通过 -->
        <span v-if="auditPassTime && auditPassTime === '0'"> 审核通过</span>

        <a
          v-if="statusAction && !isServiceDetail"
          class="status-link"
          @click="handleAction(clickDetail ? 'edit-publish' : 'view-details')"
        >
          {{ clickDetail ? editPublishText : statusAction }}
        </a>
      </template>
      <!-- 发布8 -->
      <template v-else-if="state === ProcessState.PUBLISHED">
        {{ statusText || getDefaultText() }}
        <span v-if="auditPassTime && publishType === 1"
          >已于{{
            formatTimestamp(auditPassTime, true)
          }}审核通过，审核通过后自动发布</span
        >
        <span v-if="publishType === 2">
          <span v-if="isPassTimeAfterScheduleTime"
            >，由于审核通过时间超过设定发布时间，于审核通过后自动发布</span
          >
          <span v-else>，按设定时间在{{ formatTimestamp(scheduleTime) }}发布成功</span>
        </span>
        <span v-if="auditPassTime && publishType === 0"
          >已于{{ formatTimestamp(auditPassTime, true) }}审核通过</span
        >
        <!-- {{ scheduleTime ? formatTimestamp(finishTime) + '发布' : '' }} -->
        <button class="close-btn" @click="closeStatusAlert">×</button>
      </template>
    </div>
  </div>
  <publishComponent
    :visible="showPublishDialog"
    :process-id="processId"
    :set-visible="(val) => (showPublishDialog = val)"
  />
</template>

<script setup lang="ts">
import { ref, defineProps, computed, onMounted, watch } from 'vue';
import { CONST } from '../utils/constants';
import { formatTimestamp } from '../utils';
import { ProcessState } from '../utils/type';
import { useRoute, useRouter } from 'vue-router';
import publishComponent from '@/components/PublishComponent/publishComponent.vue';
import draftIcon from '../../../assets/images/draft.png';
import auditingIcon from '../../../assets/images/auditing.png';
import auditing1Icon from '../../../assets/images/auditing1.png';
import rejectIcon from '../../../assets/images/reject.png';
import finishedIcon from '../../../assets/images/finished.png';

const props = defineProps<{
  type: 'service' | 'config' | 'configView';
  status: {
    state: number;
    text?: string;
    audit?: {
      auditor?: string;
    };
    auditor?: string;
    scheduleTime?: string;
    finishTime?: string;
    auditPassTime?: string;
    publishType?: number;
    isfirst: boolean;
  };
  show?: boolean;
  processId?: string;
}>();
const showPublishDialog = ref(false);
const route = useRoute();
const router = useRouter();
const isAlertClosed = ref(false);
const hide = ref(false);
const clickDetail = ref(false);

// localStorage的key
const STORAGE_KEY = 'status_alert_closed_map';

// 判断是否为预览模式
const isPreviewMode = computed(() => {
  console.log('route', props.type, route);

  if (props.type === 'service') {
    const routeType = route.params.type || '';
    return routeType === 'preview';
  }
  const routePath = route.path || '';
  return routePath === '/home/<USER>/card-view';
});

console.log(props.status, '------');
// 状态数据
const state = ref(props.status.state);
const statusText = ref(props.status.text);
const auditReason = ref(props.status?.audit?.auditor);
const scheduleTime = ref(props.status.scheduleTime);
const auditPassTime = ref(props.status.auditPassTime);
const publishType = ref(props.status.publishType);
const showAuditReason = ref(false);
const statusAction = ref('');
const isfirst = ref(props.status.isfirst);

// 获取关闭状态映射
const getClosedStatusMap = (): Record<string, boolean> => {
  const mapStr = localStorage.getItem(STORAGE_KEY);
  if (mapStr) {
    try {
      return JSON.parse(mapStr);
    } catch (e) {
      console.error('解析关闭状态映射失败:', e);
      return {};
    }
  }
  return {};
};

// 保存关闭状态映射
const saveClosedStatusMap = (map: Record<string, boolean>): void => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(map));
};

// 判断审核通过时间是否大于定时发布时间
const isPassTimeAfterScheduleTime = computed(() => {
  if (!auditPassTime.value || !scheduleTime.value) return false;
  return Number(auditPassTime.value) > Number(scheduleTime.value);
});

// 服务详情
const isServiceDetail = ref(false);
// 根据状态获取操作按钮文本
function getStatusAction(state: number): string | null {
  // 立即发布不显示按钮, 详情时显示查看按钮
  if (publishType.value !== 2 && props.type === 'config') {
    return null;
  }

  // 服务列表显示查看
  if (publishType.value !== 2 && props.type === 'service' && isPreviewMode.value) {
    // isServiceDetail.value = false;
    return '查看';
  }
  // 服务详情不显示按钮
  if (publishType.value !== 2 && clickDetail.value) {
    return null;
  }

  // 新人不显示按钮
  if (isfirst.value) {
    showAuditReason.value = true;
    return null;
  }

  let typeKey: 'SERVICE' | 'CONFIG' = 'SERVICE';
  if (props.type === 'configView' || props.type === 'config') {
    typeKey = 'CONFIG';
  } else {
    typeKey = 'SERVICE';
  }

  switch (state) {
    case ProcessState.DRAFT:
      return CONST.BAR_STATUS[typeKey]?.DRAFT_ACTION || null;
    case ProcessState.AUDITING:
      return CONST.BAR_STATUS[typeKey]?.AUDITING_ACTION || null;
    case ProcessState.REJECTED:
      return CONST.BAR_STATUS[typeKey]?.REJECTED_ACTION || null;
    case ProcessState.APPROVED:
    case ProcessState.AUDIT_PASS:
    case ProcessState.PUBLISHING:
      return CONST.BAR_STATUS[typeKey]?.APPROVED_ACTION || null;
    default:
      return null;
  }
}

// 编辑发布设置文本
const editPublishText = '修改发布设置';

// 计算审核驳回原因，按换行符分隔
const auditReasons = computed(() => {
  if (!auditReason.value) return [];
  return auditReason.value.split('\n');
});

// 根据type和state获取默认文本
const getDefaultText = () => {
  let typeKey: 'SERVICE' | 'CONFIG' = 'SERVICE';
  if (props.type === 'configView' || props.type === 'config') {
    typeKey = 'CONFIG';
  } else {
    typeKey = 'SERVICE';
  }

  switch (state.value) {
    case ProcessState.DRAFT:
      return CONST.BAR_STATUS[typeKey]?.DRAFT || '';
    case ProcessState.AUDITING:
      return CONST.BAR_STATUS[typeKey]?.AUDITING || '';
    case ProcessState.REJECTED:
      return CONST.BAR_STATUS[typeKey]?.REJECTED || '';
    case ProcessState.AUDIT_PASS:
    case ProcessState.PUBLISHING:
      return `${props.type === 'service' ? '名片服务' : '基础信息'}审核已于`;
    case ProcessState.PUBLISHED:
    case ProcessState.APPROVED:
      return `${props.type === 'service' ? '名片服务' : '基础信息'}`;
    default:
      return '';
  }
};

// 判断是否为已审核通过状态
const isApproved = (state: number): boolean =>
  state === ProcessState.AUDIT_PASS || state === ProcessState.PUBLISHING;

// 状态图标映射
const getStatusIcon = (state: number): string => {
  switch (state) {
    case ProcessState.DRAFT:
      return draftIcon;
    case ProcessState.AUDITING:
      return clickDetail.value ? auditingIcon : auditing1Icon;
    case ProcessState.REJECTED:
      return rejectIcon;
    case ProcessState.PUBLISHED:
    case ProcessState.AUDIT_PASS:
    case ProcessState.PUBLISHING:
    case ProcessState.APPROVED:
      return finishedIcon;
    default:
      return '';
  }
};

// 状态提示相关方法
const getStatusClass = (state: number): string => {
  switch (state) {
    case ProcessState.DRAFT:
      return 'status-draft';
    case ProcessState.AUDITING:
      return 'status-auditing';

    case ProcessState.APPROVED:
    case ProcessState.AUDIT_PASS:
    case ProcessState.PUBLISHING:
    case ProcessState.PUBLISHED:
      return 'status-approved';
    case ProcessState.REJECTED:
      return 'status-reject';
    default:
      return '';
  }
};

const emit = defineEmits(['continue-edit', 'view-details', 'close']);

const handleAction = (action: 'continue-edit' | 'view-details' | 'edit-publish') => {
  clickDetail.value = true;
  // if (action === 'view-details') {
  //   isServiceDetail.value = true;
  // }

  if (action === 'edit-publish') {
    editPublish();
  } else {
    if (props.type === 'config' || props.type === 'configView') {
      router.push({
        path: `/home/<USER>/card-config`,
      });
      return;
    }
    emit(action);
  }
};
const editPublish = () => {
  showPublishDialog.value = true;
};

const viewEdit = () => {
  if (props.type === 'config' || props.type === 'configView') {
    router.push({
      path: `/home/<USER>/card-config`,
    });
  } else {
    clickDetail.value = true;
    emit('close');
    emit('continue-edit');
  }
};

const viewRejected = () => {
  if (props.type === 'configView') {
    router.push({
      path: `/home/<USER>/card-config`,
    });
  } else {
    clickDetail.value = true;
    showAuditReason.value = true;
    emit('continue-edit');
  }
};

const closeStatusAlert = () => {
  // 将关闭状态保存到localStorage中的map
  if (state.value === ProcessState.PUBLISHED) {
    const closedMap = getClosedStatusMap();
    closedMap[props.processId] = true;
    saveClosedStatusMap(closedMap);
  }
  isAlertClosed.value = true;
  emit('close');
};

// 检查localStorage中的关闭状态
const checkAlertClosedStatus = () => {
  // 基础信息草稿中不显示 bar
  hide.value = false;

  if (props.type === 'config' && state.value === ProcessState.DRAFT) {
    hide.value = true;
    return;
  }
  if (state.value === ProcessState.PUBLISHED) {
    const closedMap = getClosedStatusMap();
    isAlertClosed.value = !!closedMap[props.processId];
  } else {
    isAlertClosed.value = false;
  }
};

// 初始化时检查路由类型
onMounted(() => {
  isPreviewMode.value && (showAuditReason.value = false);
  console.log('当前路由模式:', isPreviewMode.value ? '预览模式' : '配置模式');
  if (props.type === 'config') {
    clickDetail.value = true;
    showAuditReason.value = true;
  } else {
    clickDetail.value = false;
  }

  // showAuditReason.value = false;

  // 检查是否应该显示提示条
  checkAlertClosedStatus();
});

watch(
  () => props.show,
  (newVal: boolean | undefined, oldVal: boolean | undefined) => {
    if (newVal && !oldVal) {
      clickDetail.value = false;
      showAuditReason.value = false;
      // 重新检查关闭状态
      checkAlertClosedStatus();
    }
  },
  { immediate: false }
);
watch(
  () => props.status,
  (newStatus) => {
    console.log('newStatus', newStatus);

    state.value = newStatus.state;
    statusText.value = newStatus.text;
    auditReason.value = newStatus?.auditor || newStatus?.audit?.auditor || '';
    scheduleTime.value = newStatus.scheduleTime;
    auditPassTime.value = newStatus.auditPassTime;
    publishType.value = newStatus.publishType || 0;
    statusAction.value = getStatusAction(state.value) || '';
    isfirst.value = props.status.isfirst;
    // 检查关闭状态
    checkAlertClosedStatus();
  },
  { immediate: true, deep: true }
);

// 监听id或状态变化，重新检查关闭状态
watch(
  () => [props.processId, props.status.state],
  () => {
    checkAlertClosedStatus();
  }
);
</script>

<script lang="ts">
export default {
  name: 'ServiceStatusAlert',
};
</script>

<style scoped>
.status-alert {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 6px;
  margin-bottom: 16px;
  max-width: 100%;
}

.status-config {
  max-width: 960px;
}

.status-draft {
  background-color: #f9f9f9;
  border: 1px solid #e7e7e7;
}

.status-auditing {
  background-color: rgba(42, 158, 255, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.status-reject {
  display: flex;
  justify-content: start;
  align-items: start;
  background-color: rgba(241, 71, 82, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.status-reject .status-content {
  /* justify-content: space-between; */
}

.status-approved {
  background-color: #e1f8ec;
  border: 1px solid #d8eee3;
}

.status-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.status-icon img {
  width: 24px;
  height: 24px;
}

.status-draft .status-icon {
  color: #999999;
}

.status-auditing .status-icon {
  color: #1890ff;
}

.status-approved .status-icon {
  color: #52c41a;
}
.status-content-reject {
  height: auto !important;
}
.status-content {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  height: 24px;
  font-weight: 400;
  font-style: normal;
  line-height: 100%;
  letter-spacing: 0;
}

.status-link {
  color: #4848dd !important;
  margin-left: 8px;
  cursor: pointer;
  text-decoration: none;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
}

.audit-reason {
  margin: 8px 0;
  font-size: 14px;
  color: #666;
}

.audit-reason p {
  margin: 4px 0;
  line-height: 1.5;
}
</style>
