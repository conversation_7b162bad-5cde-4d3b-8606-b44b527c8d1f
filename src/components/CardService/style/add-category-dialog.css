.add-category-dialog {
  padding: 0 var(--card-service-spacing-sm);
}

.add-category-dialog .dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--card-service-spacing-sm);
  margin-top: 100px !important;
}

.dialog-footer :deep(.t-button) {
  border: none;
  width: 112px;
  height: 40px;
  background-color: var(--card-service-bg-button);
}

.dialog-footer :deep(.t-button--theme-primary) {
  background-color: var(--card-service-primary-color) !important;
  border: none;
  color: #fff !important;
}

.dialog-footer :deep(.t-button--theme-primary:hover) {
  color: #fff !important;
}

.dialog-footer :deep(.t-button:hover) {
  color: var(--card-service-text-secondary);
}
.dialog-footer :deep(.t-is-disabled:hover) {
  color: #d8d8d8 !important;
}
