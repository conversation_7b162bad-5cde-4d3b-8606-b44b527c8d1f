.add-service-dialog {
  padding: 0 !important;
}

.AddServiceDialog /deep/ .t-dialog__body {
  padding-bottom: 0 !important;
}

.add-service-dialog :deep(.t-form__controls-content) {
  display: block;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: var(--card-service-spacing-sm);
  margin-top: var(--card-service-spacing-lg);
}

.dialog-footer :deep(.t-button) {
  border: none;
  width: 112px;
  height: 40px;
  background-color: var(--card-service-bg-button);
}

.dialog-footer :deep(.t-button--theme-primary:hover) {
  color: #fff !important;
}

.dialog-footer :deep(.t-button:hover) {
  color: var(--card-service-text-secondary);
}

.dialog-footer :deep(.t-button--theme-primary) {
  background-color: var(--card-service-primary-color) !important;
  border: none;
}

.radio-group {
  display: flex;
  padding: 9px 0;
}

.form-tip {
  margin-top: var(--card-service-spacing-xs);
  font-size: 14px;
  color: var(--card-service-text-hint);
}

.add-service-dialog :deep(.t-input) {
  background-color: var(--card-service-bg-input);
}

.w95 {
  width: 95%;
}

.appid-text {
  font-size: 14px;
  color: #838383;
  margin-top: 2px;
}

.form-tip a {
  color: var(--card-service-link-color);
  cursor: pointer;
}
.empty {
  color: #8d8d8d;
}
.empty a {
  color: #6161e2 !important;
  cursor: pointer;
}

.highlight-keyword {
  color: var(--card-service-primary-color, #6161e2);
  font-weight: bold;
}

:deep(.t-is-error .t-form__controls-content) {
  background-color: #f147520a;
  border-radius: 6px;
}
:deep(.t-is-error .form-tip) {
  background-color: #fff;
}

:deep(.t-is-error .t-input) {
  border: none;
  background-color: transparent;
}

:deep(.t-is-error .t-input:focus) {
  border: none;
}
:deep(.t-is-error .t-input--focused) {
  border: none;
}
:deep(.t-form__label--required:not(.t-form__label--required-right) label::before) {
  display: none;
}

:deep(.t-form__label--required:not(.t-form__label--required-right)) {
  margin-right: 10px;
}

.card-service-dialog /deep/ .t-dialog {
  height: 560px !important;
}
