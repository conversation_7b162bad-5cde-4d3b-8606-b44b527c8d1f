/* CardService组件样式变量 */
@import './variables.css';

/* 主组件样式 */
@import './card-service-view.css';

/* 子组件样式 */
@import './bottom-actions.css';
@import './service-status-alert.css';
@import './add-category-dialog.css';
@import './add-service-dialog.css';

/* 添加服务提示样式 */
.service-hint-wrapper {
  height: 88px;
  align-items: center;
  justify-content: center;
  display: flex;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.service-hint {
  color: #999;
  font-size: 14px;
  margin: 0;
}

.card-service-dialog /deep/ .t-dialog__body {
  padding-bottom: 0 !important;
}

:deep(.t-dialog__body) {
  --card-service-bg-button: #f5f5f5;
  padding-bottom: 0 !important;
}
