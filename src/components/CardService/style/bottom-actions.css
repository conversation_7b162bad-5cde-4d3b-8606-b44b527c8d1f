.bottom-actions {
  margin-top: 24px;
  display: flex;
  gap: 8px;
  transition: all 0.3s;
}

.bottom-actions :deep(.t-button) {
  border: none !important;
  background-color: #f5f5f5 !important;
}
.bottom-actions :deep(.t-button:hover) {
  color: rgba(0, 0, 0, 0.9) !important;
}

.bottom-actions :deep(.t-button--theme-primary) {
  border: none !important;
  background-color: #07c160 !important;
  color: white !important;
}
.bottom-actions :deep(.t-is-disabled:hover) {
  color: #0000001f !important;
}
.bottom-actions :deep(.t-is-disabled) {
  color: #0000001f !important;
}

.bottom-actions :deep(.t-button--theme-primary:hover) {
  color: #fff !important;
  background-color: #36d17c !important;
}

.bottom-actions-hidden {
  opacity: 0;
  transform: translateY(20px);
  pointer-events: none;
}

.confirm-btn,
.cancel-btn,
.save-btn,
.return-btn {
  width: 112px !important;
  height: 40px !important;
  border-radius: 8px !important;
}
