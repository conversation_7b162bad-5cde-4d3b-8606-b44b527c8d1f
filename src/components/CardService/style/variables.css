.card-service {
  /* 主题颜色 */
  --card-service-primary-color: #07c160;
  --card-service-primary-hover: #36d17c;
  --card-service-primary-focus: #36d17c;
  --card-service-primary-active: #07c160;
  --card-service-primary-disabled: #94e3b3;
  --card-service-primary-light: #e8f7ee;

  /* 文字颜色 */
  --card-service-text-primary: #1a1a1a;
  --card-service-text-secondary: rgba(0, 0, 0, 0.9);
  --card-service-text-disabled: #0000001f;
  --card-service-text-hint: rgba(0, 0, 0, 0.3);
  --card-service-link-color: #4848dd;

  /* 背景颜色 */
  --card-service-bg-primary: #fff;
  --card-service-bg-secondary: #f9f9f9;
  --card-service-bg-button: #f5f5f5;
  --card-service-bg-input: #0000000a;

  /* 边框颜色 */
  --card-service-border-color: #e7e7e7;
  --card-service-border-light: #f0f0f0;
  --card-service-border-hover: #dcdcdc;

  /* 状态颜色 */
  --card-service-status-draft-bg: #f9f9f9;
  --card-service-status-draft-border: #e7e7e7;
  --card-service-status-auditing-bg: rgba(42, 158, 255, 0.12);
  --card-service-status-reject-bg: rgba(241, 71, 82, 0.12);
  --card-service-status-approved-bg: #e1f8ec;
  --card-service-status-approved-border: #d8eee3;

  /* 间距 */
  --card-service-spacing-xs: 4px;
  --card-service-spacing-sm: 8px;
  --card-service-spacing-md: 16px;
  --card-service-spacing-lg: 24px;

  /* 圆角 */
  --card-service-border-radius: 8px;
  --card-service-border-radius-sm: 6px;

  /* 字体 */
  --card-service-font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;

  /* 过渡 */
  --card-service-transition: all 0.3s;
  --card-service-transition-fast: all 0.2s;

  /* 兼容TDesign变量 */
  --td-brand-color: var(--card-service-primary-color);
  --td-brand-color-hover: var(--card-service-primary-hover);
  --td-brand-color-focus: var(--card-service-primary-focus);
  --td-brand-color-active: var(--card-service-primary-active);
  --td-brand-color-disabled: var(--card-service-primary-disabled);
  --td-brand-color-light: var(--card-service-primary-light);
}
