/* 显式添加组件主体样式 */
.card-service {
  font-family: var(--card-service-font-family) !important;
  color: var(--card-service-text-primary) !important;
  padding: 0 !important;
  background-color: var(--card-service-bg-primary) !important;
  border-radius: var(--card-service-border-radius) !important;
}

.category-spacer {
  height: 0;
  background-color: var(--card-service-primary-light) !important;
  margin: 10px 0;
  transition: var(--card-service-transition-fast);
  opacity: 0;
  position: relative;
}

.category-spacer::before {
  content: '';
  position: absolute;
  top: -30px;
  bottom: -30px;
  left: 0;
  right: 0;
  z-index: 1;
}

.category-spacer.spacer-active {
  height: 10px;
  background-color: var(--card-service-primary-color) !important;
  opacity: 1;
  border-radius: var(--card-service-border-radius-sm);
}

.category-spacer.spacer-visible {
  height: 10px;
  opacity: 1;
  border-radius: var(--card-service-border-radius-sm);
}

.card-service-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 24px;
}

.card-service-header :deep(.t-button) {
  background-color: var(--card-service-bg-button);
  border: none;
  width: 128px;
  height: 40px;
}

.category-actions :deep(.t-button:hover) {
  color: #4848dd !important;
}

.card-service-header :deep(.t-button:hover) {
  color: var(--card-service-text-secondary);
}
.card-service-header :deep(.t-is-disabled:hover) {
  color: rgba(0, 0, 0, 0.26);
}

.title {
  font-size: 20px;
  font-weight: 500;
  margin: 0;
}

.service-category {
  margin-bottom: var(--card-service-spacing-lg);
  border-radius: var(--card-service-border-radius-sm);
  overflow: hidden;
  transition: var(--card-service-transition);
}

/* 服务分类样式 */
.service-category.category-dragging {
  opacity: 0.6;
  border: 1px dashed #07c160 !important;
  transform: scale(0.98);
}

.category-header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background-color: var(--card-service-bg-secondary);
  cursor: pointer;
  width: 100%;
}

.category-header[draggable='true'] {
  cursor: move;
}

.category-header[draggable='true']:hover::after {
  opacity: 1;
}

.card-service .action-btn:hover {
  background-color: transparent !important;
}

:deep(.t-collapse-panel__icon) {
  display: none;
}

.category-title-wrapper {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: move;
}

/* 确保无分类不可拖拽 */
.service-category[data-category-name='无分类'] .category-title-wrapper {
  cursor: default;
}

.category-title {
  font-weight: 500;
  font-family: var(--card-service-font-family);
  line-height: 1;
  letter-spacing: 0px;
  text-align: center;
  height: 100%;
  font-size: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.category-actions {
  float: right;
  display: flex;
  position: relative;
  z-index: 5;
  align-items: center;
}

.category-actions :deep(.t-button__text) {
  font-weight: 400;
  font-size: 16px;
}

.category-actions :deep(button) {
  border: none;
}

.t-button--variant-text.t-button--theme-primary.t-is-disabled {
  color: var(--card-service-text-disabled) !important;
}

.category-actions :deep(.t-is-disabled) .category-actions :deep(button:hover),
.category-actions :deep(button:active),
.category-actions :deep(button:focus) {
  border: none;
  outline: none;
  box-shadow: none;
  color: var(--card-service-link-color);
  background-color: transparent;
}

.service-item {
  display: flex;
  padding: 12px var(--card-service-spacing-md);
  border-bottom: 1px solid var(--card-service-border-color);
  align-items: center;
}

.column-order {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 16px;
}

.column-name {
  font-size: 16px;
}

.column-link-type {
  font-size: 16px;
}

.column-link {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 16px !important;
}
.column-link .t-text-ellipsis {
  font-size: 16px;
}

:deep(.column-actions > .category-actions) {
  margin-right: -15px;
}

.order-icon {
  cursor: move;
  width: 24px;
  height: 24px;
}

.drag-icon {
  cursor: move;
  color: var(--card-service-border-hover);
  filter: invert(96%) sepia(0%) saturate(0%) hue-rotate(164deg) brightness(93%)
    contrast(88%);
}

.action-btn {
  cursor: pointer;
  font-size: 14px;
  position: relative;
  z-index: 10;
  font-weight: 400;
}

/* 按钮相关样式 */
.modify-btn {
  color: #4848dd !important;
}

.delete-btn {
  color: #4848dd !important;
}

.add-service-btn-wrapper {
  padding-top: 24px;
  background-color: var(--card-service-bg-secondary);
}

.add-service-btn {
  background-color: rgba(0, 0, 0, 0.04);
  border: none;
  width: 112px;
  height: 40px;
  border-radius: var(--card-service-border-radius);
  font-size: 14px;
  color: var(--card-service-text-primary);
  font-weight: 500;
  line-height: 22px;
  font-size: 16px;
  padding: 9px 24px !important;
}
.add-service-btn-primary {
  background-color: rgba(7, 193, 96, 1);
  color: rgba(255, 255, 255, 1);
}

.empty-service-hint {
  padding: var(--card-service-spacing-lg);
  text-align: center;
  color: #999;
  font-size: 14px;
}

.no-data-container {
  padding: 40px 0;
  text-align: center;
  color: #999;
  font-size: 16px;
  border: 1px dashed var(--card-service-border-color);
  border-radius: var(--card-service-border-radius-sm);
  margin-bottom: var(--card-service-spacing-lg);
}

/* 表格样式 */
.t-table {
  width: 100%;
  font-size: 16px;
}

.t-table :deep(.t-table__row) {
  cursor: pointer;
}

.t-table :deep(.t-table__cell) {
  padding: 12px var(--card-service-spacing-md) !important;
}

.table-link {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 400px;
}

.t-table :deep(tr, td) {
  height: 88px;
  background-color: var(--card-service-bg-secondary);
}

.t-table :deep(td) {
  border-bottom: 1px solid var(--card-service-border-light);
}

.t-table :deep(.t-table__header tr) {
  height: 54px;
  font-weight: 800 !important;
}

.t-table :deep(.t-table__header tr th) {
  height: 54px;
  font-weight: 800 !important;
  color: #000000e5 !important;
  font-family: var(--card-service-font-family);
  font-weight: 500;
  line-height: 100%;
  letter-spacing: 0px;
  border-bottom: 1px solid #f7f7f7;
}

/* 自定义排序图标样式 */
.t-table :deep(.t-table__row.t-table__row--draggable) {
  cursor: move;
}

/* 拖动时的样式 */
.t-table :deep(.t-table__row--drag-sorted) {
  background-color: #e8f7ee !important;
}

/* 跨分类拖拽时的样式 */
.t-table :deep(.t-table__row--dragging) {
  opacity: 0.8;
  background-color: #e8f7ee !important;
}

/* 拖拽源分类样式 */
.service-category.dragging-source {
  background-color: rgba(240, 240, 240, 0.5);
}

/* 状态相关样式 */
.service-category.drop-target {
  border: 2px dashed #07c160 !important;
}

.operation-wrapper {
  display: flex;
  gap: var(--card-service-spacing-md);
  justify-content: flex-start;
}

/* 指示可拖拽区域的样式 */
.service-category[draggable='true'] .category-header {
  cursor: move;
}

/* 自定义折叠面板样式 */
:deep(.t-collapse) {
  border: none;
  background: transparent;
}

:deep(.t-collapse-panel) {
  border: none;
  margin-bottom: 0;
}

:deep(.t-collapse-panel__header) {
  padding: 0;
  border: none;
}

:deep(.t-collapse-panel__content) {
  padding: 0;
}

:deep(.t-collapse-panel__header__arrow) {
  margin-right: var(--card-service-spacing-sm);
}
.icon-wrapper {
  width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.icon-wrapper svg {
  width: 100%;
  height: 100%;
}

:deep(.t-collapse-panel__body) {
  border: none;
  padding: 0px 24px 24px 24px;
  background-color: var(--card-service-bg-secondary);
}

:deep(.t-loading) {
  background-color: #f7f7f7 !important;
}

:deep(.t-table__empty) {
  min-height: 88px !important;
}
