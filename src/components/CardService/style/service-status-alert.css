.status-alert {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: var(--card-service-border-radius-sm);
  margin-bottom: var(--card-service-spacing-md);
}

.status-draft {
  background-color: var(--card-service-status-draft-bg);
  border: 1px solid var(--card-service-status-draft-border);
}

.status-auditing {
  background-color: var(--card-service-status-auditing-bg);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.status-reject {
  display: flex;
  justify-content: start;
  align-items: start;
  background-color: var(--card-service-status-reject-bg);
  border: 1px solid rgba(0, 0, 0, 0.04);
}
.status-reject .status-content {
  /* justify-content: space-between; */
  /* align-items: start; */
}
.reject-content {
  margin-top: 4px;
}
.status-approved {
  background-color: var(--card-service-status-approved-bg);
  border: 1px solid var(--card-service-status-approved-border);
}

.status-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
}

.status-icon img {
  width: 24px;
  height: 24px;
}

.status-draft .status-icon {
  color: #999999;
}

.status-auditing .status-icon {
  color: #1890ff;
}

.status-approved .status-icon {
  color: #52c41a;
}

.status-content {
  flex: 1;
  display: flex;
  align-items: center;
  position: relative;
  font-family: var(--card-service-font-family);
  font-weight: 400;
  font-style: normal;
  line-height: 100%;
  letter-spacing: 0;
}

.status-link {
  color: #4848dd !important;
  margin-left: 8px;
  cursor: pointer;
  text-decoration: none;
}

.close-btn {
  position: absolute;
  right: 0;
  top: 0;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
}
