<template>
  <!-- 调试代码 -->
  <!-- <div style="margin-bottom: 100px">
    isPreview：{{ isPreview }}---disabledBtn：{{ disabledBtn }}---lastState：{{
      lastState
    }}---isShowBar：{{ isShowBar }}---isShowButton：{{
      isShowButton
    }}---showStatusAlert：{{ showStatusAlert }} --- isEdit：{{ isEdit }} --- isfirst：{{
      isfirst
    }}
  </div> -->

  <div class="card-service">
    <div class="card-service-header">
      <h3 class="title">名片服务</h3>
      <t-popconfirm
        :visible="showCategoryLimitPopconfirm"
        :content="CONST.MSG.CATEGORY_LIMIT"
        :ok-text="null"
        :confirm-btn="CONST.BTN.I_KNOW"
        placement="bottom"
        :cancel-btn="null"
        overlay-class-name="custom-popconfirm-container"
        @visibleChange="onVisibleChange"
      >
        <t-button
          class="add-category-btn"
          variant="outline"
          :disabled="disabledBtn || (!isEdit && isShowEntryButton)"
          @click="handleAddServiceCategory"
        >
          {{ isPreview && !isShowEntryButton ? '修改' : '添加服务分类' }}
        </t-button>
      </t-popconfirm>
    </div>
    <service-status-alert
      v-if="isShowBar"
      :type="moduleType"
      :status="statusData"
      :process-id="processId"
      :show="showStatusAlert"
      @continue-edit="continueEdit"
      @view-details="viewDetails"
      @edit-publish="editPublish"
      @close="closeStatusAlert"
    />
    <!-- 分类区域 -->
    <div>
      <!-- 首部 spacer -->
      <div class="category-spacer" data-spacer-index="0" />

      <!-- 分类前的空盒子 -->
      <t-collapse
        :expand-icon="false"
        :default-expand-all="true"
        @change="handlePanelChange"
      >
        <div
          v-for="(category, categoryIndex) in normalizedServiceData"
          :key="categoryIndex"
        >
          <div
            class="service-category"
            :data-category-index="categoryIndex"
            :data-category-name="category.name"
          >
            <t-collapse-panel
              :value="categoryIndex.toString()"
              :destroy-on-collapse="false"
              :disabled="false"
            >
              <template #header>
                <div class="category-header">
                  <div class="category-title-wrapper">
                    <img
                      v-if="category.name !== '无分类' && isShowButton"
                      :src="MoveSvg"
                      class="drag-icon"
                    />
                    <div class="category-title">
                      <span>{{ category.name }}</span>
                    </div>
                    <span class="icon-wrapper">
                      <ChevronDownIcon
                        v-if="!expandedPanels.includes(categoryIndex.toString())"
                        class="collapse-icon"
                      />
                      <ChevronUpIcon v-else class="collapse-icon" />
                    </span>
                  </div>
                  <div
                    v-if="category.name !== '无分类' && isShowButton"
                    class="category-actions"
                  >
                    <t-button
                      theme="primary"
                      class="action-btn modify-btn"
                      variant="text"
                      :disabled="!isEdit"
                      @click.stop="handleModifyCategory(categoryIndex)"
                    >
                      修改
                    </t-button>

                    <t-popconfirm
                      theme="default"
                      overlay-class-name="custom-popconfirm-container"
                      :visible="
                        showDeleteCategoryPopconfirm &&
                        currentCategoryIndex === categoryIndex
                      "
                      :content="CONST.MSG.DELETE_CATEGORY"
                      :confirm-btn="{
                        content: CONST.BTN.CONFIRM_REMOVE,
                        theme: 'danger',
                      }"
                      placement="top"
                      @visibleChange="onDeleteCategoryVisibleChange"
                    >
                      <t-button
                        theme="primary"
                        class="action-btn delete-btn"
                        variant="text"
                        :disabled="!isEdit"
                        @click.stop="handleDeleteCategory(categoryIndex)"
                      >
                        移除
                      </t-button>
                    </t-popconfirm>
                  </div>
                </div>
              </template>

              <!-- 服务列表 -->
              <t-table
                :data="category.services"
                :columns="columns"
                row-key="id"
                :drag-sort="isEdit ? 'row' : undefined"
                hover
                :empty="
                  getTotalServicesCount < CONST.LIMIT.MIN_SERVICES
                    ? CONST.MSG.EMPTY_SERVICE_HINT
                    : CONST.MSG.NO_DATA_HINT
                "
                @drag-sort="handleDragSort($event, categoryIndex)"
              >
                <template v-if="isShowButton" #sort>
                  <img :src="MoveSvg" :class="isEdit ? 'order-icon' : 'drag-icon'" />
                </template>

                <template v-if="isShowButton" #operation="{ rowIndex }">
                  <div class="category-actions">
                    <t-button
                      theme="primary"
                      class="action-btn modify-btn"
                      variant="text"
                      :disabled="!isEdit"
                      @click="handleModifyService(categoryIndex, rowIndex)"
                    >
                      修改
                    </t-button>
                    <t-popconfirm
                      theme="default"
                      overlay-class-name="custom-popconfirm-container"
                      :visible="
                        showDeleteServicePopconfirm &&
                        currentServiceIndex === rowIndex &&
                        currentCategoryIndex === categoryIndex
                      "
                      :content="
                        getDeleteServiceContent(normalizedServiceData, categoryIndex)
                      "
                      :confirm-btn="
                        getDeleteServiceBtnConfig(normalizedServiceData, categoryIndex)
                      "
                      :cancel-btn="
                        getDeleteServiceCancelBtn(normalizedServiceData, categoryIndex)
                      "
                      @visibleChange="onDeleteServiceVisibleChange"
                    >
                      <t-button
                        theme="primary"
                        class="action-btn delete-btn"
                        variant="text"
                        :disabled="!isEdit"
                        @click="handleDeleteService(categoryIndex, rowIndex)"
                      >
                        删除
                      </t-button>
                    </t-popconfirm>
                  </div>
                </template>
              </t-table>

              <!-- 服务数量不足提示 - 当有服务时在表格下方显示 -->
              <div
                v-if="
                  getTotalServicesCount < CONST.LIMIT.MIN_SERVICES &&
                  category.services.length > 0
                "
                class="service-hint-wrapper"
              >
                <p class="service-hint">
                  {{ CONST.MSG.EMPTY_SERVICE_HINT }}
                </p>
              </div>

              <!-- 添加服务按钮 -->
              <div v-if="isEdit" class="add-service-btn-wrapper">
                <t-popconfirm
                  theme="default"
                  overlay-class-name="custom-popconfirm-container"
                  :visible="
                    showAddServiceLimitPopconfirm &&
                    currentCategoryIndex === categoryIndex
                  "
                  :content="getAddServiceContent(normalizedServiceData, categoryIndex)"
                  :confirm-btn="{
                    content: CONST.BTN.I_KNOW,
                    theme: 'default',
                  }"
                  :cancel-btn="null"
                  @visibleChange="onAddServiceVisibleChange"
                >
                  <t-button
                    v-if="isShowButton"
                    class="add-service-btn"
                    :class="[
                      getTotalServicesCount < CONST.LIMIT.MIN_SERVICES
                        ? 'add-service-btn-primary'
                        : '',
                    ]"
                    variant="outline"
                    @click="handleAddServiceClick(categoryIndex)"
                  >
                    添加服务
                  </t-button>
                </t-popconfirm>
              </div>
            </t-collapse-panel>
          </div>

          <!-- 分类后的 spacer，每个分类后面添加一个 -->
          <div
            class="category-spacer"
            :data-spacer-index="categoryIndex + 1"
            v-if="category.name !== '无分类'"
          />
        </div>
      </t-collapse>
    </div>
    <!-- <publishComponent
      :visible="showPublishDialog"
      :process-id="processId"
      :set-visible="(val) => (showPublishDialog = val)"
    /> -->
    <!-- 添加服务分类弹窗 -->
    <add-category-dialog
      :visible="showAddCategoryDialog"
      :is-modify="isModifyCategoryMode"
      :edit-category-name="currentEditCategoryName"
      :existing-categories="normalizedServiceData.map((c) => c.name)"
      @update:visible="showAddCategoryDialog = $event"
      @cancel="closeAddCategoryDialog"
      @confirm="handleCategoryConfirm"
    />
    <!-- 添加服务弹窗 -->
    <add-service-dialog
      :visible="showAddServiceDialog"
      :category-name="currentCategoryName"
      :is-modify="isModifyMode"
      :edit-service="currentEditService"
      :existing-services="normalizedServiceData.flatMap((category) => category.services)"
      @update:visible="showAddServiceDialog = $event"
      @cancel="closeAddServiceDialog"
      @confirm="confirmAddService"
    />

    <!-- 底部操作区域 -->
    <bottom-actions
      :is-edit="isEdit"
      :isfirst="isfirst"
      :state="lastState"
      :is-preview="isPreview"
      :is-dragging="isDragging"
      :is-data-changed="isDataChanged"
      :process-id="processId"
      :normalized-service-data="normalizedServiceData"
      :internal-data="internalData"
      :buttons-visible="buttonsConfig"
      :cancel-disabled="cancelDisabled"
      @reset-data-changed="isDataChanged = false"
      @fetch-data="fetchLastServiceData"
      @cancel-success="handleCancelSuccess"
      @back-preview="handleBackPreview"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineProps, watch, nextTick, onMounted, onUnmounted } from 'vue';

import { useRoute, useRouter } from 'vue-router';
import {
  type ServiceData,
  type ServiceItem,
  type DragState,
  type NormalizedCategory,
  type NormalizedServiceItem,
  ProcessState,
  type LatestServicePublishResponse,
} from './utils/type';
import {
  Button as TButton,
  MessagePlugin,
  Popconfirm as TPopconfirm,
  type PopconfirmVisibleChangeContext,
  Table as TTable,
  type PrimaryTableCol,
  Collapse as TCollapse,
  CollapsePanel as TCollapsePanel,
  LoadingPlugin,
} from 'tdesign-vue-next';
import { ChevronDownIcon, ChevronUpIcon } from 'tdesign-icons-vue-next';
import ServiceStatusAlert from './compontents/ServiceStatusAlert.vue';
import AddCategoryDialog from './compontents/AddCategoryDialog.vue';
import AddServiceDialog from './compontents/AddServiceDialog.vue';
import BottomActions from './compontents/BottomActions.vue';
import {
  checkExistingCategory,
  checkCategoryCount,
  createEmptyCategoryService,
  canAddService,
  getAddServiceContent,
  getDeleteServiceContent,
  getDeleteServiceBtnConfig,
  getDeleteServiceCancelBtn,
  findParentWithClass,
  clearDragStyles,
  setupCategoryDragAttributes,
  moveServiceBetweenCategories,
  sortServiceListByCategoryOrder,
  DragStateManager,
  DataManager,
} from './utils';
import { CONST } from './utils/constants';
import { CardServiceApi } from './utils/service';
import MoveSvg from '../../assets/images/move.svg?url';

// 生成唯一ID
const generateUniqueId = () =>
  `service_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

const emit = defineEmits(['update:serviceData']);

// 定义props和emits
const props = defineProps<{
  serviceData?: ServiceData;
}>();

const router = useRouter();
const route = useRoute();

const currentServiceData = ref<ServiceItem[]>([]);
const latestServiceData = ref<ServiceItem[]>([]);

const isShowBar = ref(true);
const internalData = ref<ServiceData>({ serviceList: [] });
// 新增originalData变量，用于保存初始数据，用于对比是否有变化
const originalData = ref<ServiceData>({ serviceList: [] });

// 标记数据已变更
const setDataChanged = () => {
  isDataChanged.value = true;
};

// 模块类型
const moduleType = ref<'config' | 'service'>('service');

const statusData = computed(() => ({
  state: processData.value.state, // 状态
  text: '', // 自定义文本，''就用组件的默认文本
  auditor: processData.value.audit?.auditor, // 审核驳回原因
  scheduleTime: processData.value.scheduleTime, // 定时发布时间
  auditPassTime: processData.value.auditPassTime, // 审核通过时间
  finishTime: processData.value.finishTime, // 发布完成时间
  publishType: processData.value.publishType, // 定时发布类型
  isfirst: isfirst.value, // 是否为新用户，新用户不展示按钮
}));
const showStatusAlert = ref(true);
const processData = ref<{
  content?: {
    serviceList?: ServiceItem[];
  };
  publishType: number;
  state: number;
  scheduleTime: string;
  auditPassTime: string;
  processId: string;
  finishTime: string;
  audit?: {
    auditor?: string;
  };
}>({
  content: { serviceList: [], state: ProcessState.INIT },
  publishType: 0,
  state: ProcessState.INIT,
  scheduleTime: '',
  auditPassTime: '',
  processId: '',
  finishTime: '',
});
const processId = ref();
const isEdit = ref(false);
const isFromState = ref(false);
const isfirst = ref(true);
const previousRoute = ref('/');
const cancelDisabled = ref(false);
const isShowButton = ref(true);
const isShowEntryButton = ref(false);
// 点击外部关闭弹窗的处理函数
const handleOutsideClick = (e: MouseEvent) => {
  // 获取当前点击的元素
  const target = e.target as HTMLElement;

  // 如果点击的是弹窗内部元素或触发弹窗的按钮，不做处理
  if (
    target.closest('.t-popconfirm') ||
    target.closest('.t-button') ||
    target.tagName === 'BUTTON' ||
    target.classList.contains('t-button')
  ) {
    return;
  }

  // 检查并关闭各个弹窗
  if (showCategoryLimitPopconfirm.value) {
    showCategoryLimitPopconfirm.value = false;
  }

  if (showDeleteCategoryPopconfirm.value) {
    showDeleteCategoryPopconfirm.value = false;
  }

  if (showDeleteServicePopconfirm.value) {
    showDeleteServicePopconfirm.value = false;
  }

  if (showAddServiceLimitPopconfirm.value) {
    showAddServiceLimitPopconfirm.value = false;
  }
};

const handleApiError = (error: unknown, defaultMessage = '操作失败'): string => {
  console.error(defaultMessage, error);
  return error instanceof Error ? error.message : defaultMessage;
};

const updateDataAndState = (data: ServiceData) => {
  internalData.value = data;
  originalData.value = JSON.parse(JSON.stringify(data));
  updateInternalData(internalData.value);
  isDataChanged.value = false;
};

// 获取当前服务数据
const fetchCurrentServiceData = async () => {
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });

  try {
    const response = await CardServiceApi.fetchCurrentServiceData();
    if (!response.error) {
      updateDataAndState(response.data);
      currentServiceData.value = response?.data?.serviceList;
    } else {
      updateDataAndState({ serviceList: [] });
    }
  } catch (error) {
    updateDataAndState({ serviceList: [] });
  } finally {
    loadingInstance.hide();
  }
};

// 创建名片服务发布单
const createService = async (): Promise<string | null> => {
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });

  try {
    const response = await CardServiceApi.createServicePublishProcess();

    if (!response.error && response.processId) {
      isDataChanged.value = false;
      const newProcessId = response.processId;
      updateDataAndState({ serviceList: [] });
      return newProcessId;
    }

    if (response.error) {
    }
    return null;
  } catch (error) {
    return null;
  } finally {
    loadingInstance.hide();
  }
};

// 更新处理状态数据
const updateProcessData = (
  process:
    | {
        processId?: string;
        state?: number;
        content?: {
          serviceList?: ServiceItem[];
        };
        publishType?: number;
        scheduleTime?: string;
        auditPassTime?: string;
        finishTime?: '';

        audit?: {
          auditor?: string;
        };
      }
    | undefined,
  state: number | undefined
) => {
  if (!process) return;

  const safeState = state || ProcessState.INIT;
  console.log('safeState', safeState);
  processData.value = {
    content: {
      serviceList: process?.content?.serviceList || [],
    },
    state: safeState,
    publishType: process?.publishType || 0,
    scheduleTime: process?.scheduleTime || '',
    auditPassTime: process?.auditPassTime || '',
    processId: process?.processId || '',
    finishTime: process?.finishTime || '',
    audit: process?.audit || undefined,
  };
};
const lastState = ref(0);

// 获取服务数据
const fetchLastServiceData = async () => {
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });

  try {
    // 调用查询最新的名片服务发布单
    const response = await CardServiceApi.fetchLatestServicePublishProcess();

    // 如果没有有效响应或发布单，则创建新的服务发布单
    if (!response.process || !response.processId || response.error) {
      processId.value = await createService();
      console.log(
        '%c创建：processId',
        'color: blue; font-weight: bold;',
        processId.value
      );

      isEdit.value = true;
      initCategoryOrderMap();
      return;
    }
    // 有发布单，处理发布单信息
    const { processId: id, state } = response;
    updateProcessData(response.process, state);

    lastState.value = state || ProcessState.INIT;
    latestServiceData.value = response.process.content.serviceList;
    // 236展示撤销
    updateButtonsVisibility({
      confirm:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      save:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      cancel:
        lastState.value === ProcessState.DRAFT ||
        lastState.value === ProcessState.AUDITING ||
        lastState.value === ProcessState.AUDIT_PASS ||
        lastState.value === ProcessState.REJECTED ||
        lastState.value === ProcessState.APPROVED,
      return: true,
    });
    console.log(
      '%c判断状态',
      'color: red; font-weight: bold;',
      '是不是新手：',
      isfirst.value,
      '当前状态：',
      state,
      '是否从状态页返回：',
      previousRoute.value?.includes('service-state')
    );
    processId.value = id;
    console.log(
      '%c获取LastService的processId',
      'color: blue; font-weight: bold;',
      processId.value
    );
    isEdit.value = response.isEdit || false;
    disabledBtn.value = !isEdit.value;
    // 新手撤销的单需要再创建一个 id 用于保存
    if (isfirst.value && state === ProcessState.CANCELED) {
      processId.value = await createService();
    }
    // 处理从状态页返回的情况
    if (previousRoute.value?.includes('service-state')) {
      handleReturnFromStatePage(response, state);
    } else {
      handleNormalNavigation(response, state);
    }

    initCategoryOrderMap();
  } catch (error) {
    processId.value = await createService();
    console.log(
      '%c接口失败后创建的processId',
      'color: blue; font-weight: bold;',
      processId.value
    );
    isEdit.value = true;
    initCategoryOrderMap();
  } finally {
    loadingInstance.hide();
  }
};

// 处理从状态页返回的情况
// 注释掉，因为产品改了暂时不需要进入状态页面
const handleReturnFromStatePage = (
  response: LatestServicePublishResponse,
  state: number | undefined
) => {
  isFromState.value = true;

  // 更新数据显示
  if (response.process?.content?.serviceList) {
    updateDataAndState({
      serviceList: response.process.content.serviceList,
    });
  }

  // 如果是驳回状态，允许编辑
  if (state === ProcessState.REJECTED) {
    isEdit.value = true;
    disabledBtn.value = false;
  }

  // 如果是草稿状态隐藏bar并改为编辑态
  if (state === ProcessState.DRAFT) {
    isEdit.value = true;
    disabledBtn.value = false;
  }

  // 更新状态栏
  updateProcessData(response.process, state);

  // 如果不是编辑态，只显示撤销和返回按钮
  if (!isEdit.value) {
    updateButtonsVisibility({
      confirm: false,
      save: false,
      cancel: true,
      return: true,
    });
  }
};

// 处理正常导航的情况
const handleNormalNavigation = async (
  response: LatestServicePublishResponse,
  state: number | undefined
) => {
  isFromState.value = false;
  // 处理新手用户的情况
  if (isfirst.value) {
    console.log(
      previousRoute.value?.includes('service-state') ? '从状态页返回' : '不是从状态页返回'
    );

    // 如果是新手，全部用latest-
    // 如果是审核中或被拒绝状态，跳转到状态页
    // if (state === ProcessState.AUDITING || state === ProcessState.REJECTED) {
    //   router.push({
    //     path: `/home/<USER>/service-state/service/${processId.value}`,
    //   });
    //   return;
    // }
    // 如果是审核中状态，只显示撤销和返回按钮
    if (state === ProcessState.AUDITING) {
      updateButtonsVisibility({
        confirm: false,
        save: false,
        cancel: true,
        return: true,
      });
    }
    // 如果是驳回状态，允许编辑
    if (state === ProcessState.REJECTED) {
      isEdit.value = true;
    }
    handleReturnFromStatePage(response, state);
  } else {
    // 如果不是新手，请求现网数据
    await fetchCurrentServiceData();
  }

  // 更新状态数据
  if (response.process) {
    updateProcessData(response.process, state);
  }
};

// 获取新手状态
const fetchTutorial = async () => {
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });

  try {
    const response = await CardServiceApi.fetchTutorialStatus();
    isfirst.value = response.isFirst;
  } catch (error) {
    isfirst.value = false;
    handleApiError(error, '获取新手状态失败');
  } finally {
    loadingInstance.hide();
  }
};

// 记录展开的面板
const expandedPanels = ref<string[]>([]);

// 用于保存分类的原始顺序
const categoryOrderMap = ref<Map<string, number>>(new Map());

// 统一的拖拽状态
const dragState = ref<DragState>({
  dragType: null,
  categoryInfo: null,
  serviceInfo: null,
  activeSpacer: null,
});

// 是否正在进行拖拽操作
const isDragging = ref(false);

// 初始化分类顺序映射
const initCategoryOrderMap = () => {
  const map = new Map<string, number>();

  if (internalData.value?.serviceList) {
    let order = 0;
    // 用Set记录已添加的分类，避免重复
    const addedCategories = new Set<string>();

    internalData.value.serviceList.forEach((service: ServiceItem) => {
      const categoryName = service.serviceClassifyName || '无分类';

      // 跳过"无分类"，它不参与排序
      if (categoryName === '无分类') {
        return;
      }

      if (!addedCategories.has(categoryName)) {
        map.set(categoryName, order);
        order += 1;
        addedCategories.add(categoryName);
      }
    });
  }

  categoryOrderMap.value = map;
};

// 移动服务到指定分类 - 重构为使用数据模型而不是DOM
const moveServiceToCategory = (
  serviceId: string | null,
  targetCategoryName: string,
  targetCategoryIndex: number
) => {
  console.log(
    '调用moveServiceToCategory',
    serviceId,
    targetCategoryName,
    targetCategoryIndex
  );

  if (!serviceId) {
    MessagePlugin.error(CONST.MSG.DRAG_FAILED_SERVICE_ID);
    isDragging.value = false;
    return;
  }

  // 使用提取的工具函数移动服务
  const { updatedData, success, message } = moveServiceBetweenCategories(
    serviceId,
    targetCategoryName,
    internalData.value
  );

  if (!success) {
    MessagePlugin.error(message);
    isDragging.value = false;
    return;
  }

  // 更新数据，保持serviceList原始顺序不变
  updateInternalData(updatedData);

  // 重置拖拽状态
  dragState.value.serviceInfo = null;
  dragState.value.dragType = null;

  // 恢复底部按钮显示
  isDragging.value = false;

  // 等待DOM更新后，重新设置拖拽事件
  nextTick(() => {
    setTimeout(() => {
      setupAllDragEvents();
      console.log('分类移动完成，已重新绑定所有拖拽事件');
    }, CONST.DRAG.EVENT_TIMEOUT);
  });
};

// 设置分类标题的拖拽事件
const setupCategoryDragEvents = () => {
  // 移除旧的事件监听器
  document.removeEventListener('dragstart', categoryDragStartHandler);
  document.removeEventListener('dragend', categoryDragEndHandler);

  // 只有在可编辑状态下才设置拖拽属性
  if (isEdit.value) {
    // 设置分类头部拖拽属性
    setupCategoryDragAttributes();

    // 添加全局事件监听器
    document.addEventListener('dragstart', categoryDragStartHandler);
    document.addEventListener('dragend', categoryDragEndHandler);
  }
};

// 分类拖拽开始处理
function categoryDragStartHandler(e: DragEvent) {
  // 如果不可编辑，则不处理拖拽
  if (!isEdit.value) return;

  const target = e.target as HTMLElement;

  // 检查是否是分类拖拽
  if (target.getAttribute(CONST.DRAG.DRAG_TYPE_ATTR) !== CONST.DRAG.CATEGORY_TYPE) {
    return;
  }

  // 检查是否点击在修改或删除按钮上
  if (target.closest('.action-btn') || target.classList.contains('action-btn')) {
    return;
  }

  // 防止冒泡，确保不会触发表格行拖拽
  e.stopPropagation();

  // 获取分类信息
  const categoryEl = findParentWithClass(target, 'service-category');
  if (!categoryEl) return;

  const categoryName = categoryEl.dataset.categoryName || '';
  const categoryIndex = parseInt(categoryEl.dataset.categoryIndex || '0', 10);

  if (categoryName === CONST.DRAG.DEFAULT_CATEGORY) {
    return; // 不允许拖拽"无分类"
  }

  console.log('开始分类拖拽:', categoryName);

  // 设置拖拽数据
  dragState.value = {
    dragType: 'category',
    categoryInfo: {
      categoryName,
      categoryIndex,
      fromIndex: categoryIndex,
    },
    serviceInfo: null,
    activeSpacer: null,
  };

  // 设置正在拖拽状态，隐藏底部按钮
  isDragging.value = true;

  // 添加视觉标记 - 给整个分类添加样式
  DragStateManager.setDraggingStyle(categoryEl, true);

  setupDragImage(e, categoryEl);
  DragStateManager.setupCategorySpacersVisibility(categoryIndex);
}

// 设置拖拽图像
function setupDragImage(e: DragEvent, sourceEl: HTMLElement) {
  if (!e.dataTransfer) return;

  e.dataTransfer.effectAllowed = 'move';
  e.dataTransfer.setData('text/plain', `category:${sourceEl.dataset.categoryName || ''}`);

  try {
    const dragImage = sourceEl.cloneNode(true) as HTMLElement;
    dragImage.style.width = `${sourceEl.offsetWidth}px`;
    dragImage.style.opacity = '0.8';
    dragImage.style.position = 'absolute';
    dragImage.style.border = 'none';
    dragImage.style.top = '-1000px';
    dragImage.style.backgroundColor = '#ebfaf2';
    dragImage.style.color = '#000';
    dragImage.style.boxShadow = '0 4px 8px rgba(0,0,0,0.3)';
    dragImage.style.zIndex = '-1';
    dragImage.style.borderRadius = '8px';

    const allElements = dragImage.querySelectorAll('*');
    allElements.forEach((el) => {
      if (el instanceof HTMLElement) {
        el.style.color = '#000';
        el.style.backgroundColor = 'transparent';
        if (el.tagName === 'TABLE' || el.classList.contains('t-table')) {
          el.style.backgroundColor = '#ebfaf2';
        }

        if (
          el.tagName === 'TH' ||
          el.tagName === 'TD' ||
          el.classList.contains('t-table__th') ||
          el.classList.contains('t-table__td')
        ) {
          el.style.backgroundColor = 'transparent';
          el.style.borderColor = 'rgba(255,255,255,0.3)';
        }
      }
    });

    const buttons = dragImage.querySelectorAll('button, .t-button');
    buttons.forEach((btn) => {
      if (btn instanceof HTMLElement) {
        btn.style.color = '#4848DD';
      }
    });

    // 添加到文档中以便用作拖拽图像
    document.body.appendChild(dragImage);

    // 设置自定义拖拽图像
    e.dataTransfer.setDragImage(dragImage, 20, 20);

    // 拖拽结束后删除临时元素
    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  } catch (error) {
    console.error('设置拖拽图像时出错:', error);
  }
}

// 分类拖拽结束处理
function categoryDragEndHandler(e: DragEvent) {
  const target = e.target as HTMLElement;

  // 检查是否是分类拖拽
  if (target.getAttribute(CONST.DRAG.DRAG_TYPE_ATTR) !== CONST.DRAG.CATEGORY_TYPE) {
    return;
  }

  // 防止冒泡
  e.stopPropagation();

  // 清除样式
  const categoryEl = findParentWithClass(target, 'service-category');
  if (categoryEl) {
    DragStateManager.setDraggingStyle(categoryEl, false);
  }
}

// 统一设置所有拖拽事件
const setupAllDragEvents = () => {
  // 清除样式和重置状态
  clearDragStyles();
  dragState.value = {
    dragType: null,
    categoryInfo: null,
    serviceInfo: null,
    activeSpacer: null,
  };

  // 只有在可编辑状态下才设置拖拽事件
  if (isEdit.value) {
    // 设置各种拖拽事件
    setupCategoryDropEvents();
    setupCategorySpacerEvents();
    setupCategoryDragEvents();
    setupTableRowDragEvents();
    rebindTableDragEvents();
  }
};

// 设置表格行的拖拽事件
const setupTableRowDragEvents = () => {
  // 移除旧的事件监听器
  document.removeEventListener('dragstart', handleTableRowDragStart);

  // 只有在可编辑状态下才添加事件监听器
  if (isEdit.value) {
    // 添加新的事件监听器
    document.addEventListener('dragstart', handleTableRowDragStart);
  }
};

// 处理表格行拖拽开始
function handleTableRowDragStart(e: DragEvent) {
  // 如果不可编辑，则不处理拖拽
  if (!isEdit.value) return;

  // 检查拖拽目标是否是分类头部
  const target = e.target as HTMLElement;

  // 检查是否是分类拖拽
  const isCategoryDrag =
    target.getAttribute(CONST.DRAG.DRAG_TYPE_ATTR) === CONST.DRAG.CATEGORY_TYPE;

  if (isCategoryDrag) {
    console.log('识别到分类拖拽，忽略服务拖拽处理');
    return;
  }

  // 如果已经有分类拖拽进行中，不处理服务拖拽
  if (dragState.value.dragType === 'category') {
    return;
  }

  // 检查是否是表格行拖拽
  const rowEl =
    target.closest('tr.t-table__tr') ||
    target.closest('tbody > tr') ||
    target.closest('tr');

  if (!rowEl) {
    return;
  }

  try {
    // 从当前数据模型获取最新信息，而不依赖DOM
    const categoryEl = target.closest('.service-category') as HTMLElement;

    if (!categoryEl) return;

    // 从DOM获取初始分类信息，只用于查找对应数据
    const domCategoryName = categoryEl.dataset.categoryName || '';

    // 根据分类名查找完整的分类数据
    const categoryIndex = normalizedServiceData.value.findIndex(
      (cat: NormalizedCategory) => cat.name === domCategoryName
    );
    if (categoryIndex === -1) {
      console.error(`找不到分类: ${domCategoryName}`);
      return;
    }

    const category = normalizedServiceData.value[categoryIndex];

    // 尝试不同的查找方式获取表格行
    let tableRows: Element[] = [];
    // 尝试多种选择器，直到找到表格行
    const selectors = [
      'tbody > tr',
      '.t-table__body tr',
      'table.t-table--layout-fixed tr',
      'table tr',
    ];

    for (const selector of selectors) {
      const rows = categoryEl.querySelectorAll(selector);
      if (rows.length > 0) {
        tableRows = Array.from(rows);
        break;
      }
    }

    // 找出行索引
    const tableEl = categoryEl.querySelector('.t-table');
    if (!tableEl) return;

    // 查找行索引
    let rowIndex = -1;
    if (rowEl) {
      rowIndex = tableRows.findIndex((row) => row === rowEl);
    }

    // 如果无法确定行索引，使用一个默认值
    if (rowIndex === -1) {
      rowIndex = 0;
    }

    if (rowIndex === -1 || rowIndex >= category.services.length) {
      console.error('无效的服务行索引');
      return;
    }

    // 获取服务信息
    const currentService = category.services[rowIndex];

    // 存储完整的拖拽信息
    dragState.value = {
      dragType: 'service',
      categoryInfo: null,
      serviceInfo: {
        serviceId:
          currentService.id ||
          (currentService.originalService
            ? currentService.originalService.serviceId
            : null),
        serviceName:
          currentService.name ||
          (currentService.originalService
            ? currentService.originalService.serviceName
            : null),
        fromCategoryName: category.name,
        fromCategoryIndex: categoryIndex,
        serviceIndex: rowIndex,
      },
      activeSpacer: null,
    };

    // 设置正在拖拽状态，隐藏底部按钮
    isDragging.value = true;

    // 标记拖拽源分类
    DragStateManager.setDraggingSourceStyle(categoryEl, true);

    console.log(
      `开始拖拽服务: ${dragState.value.serviceInfo?.serviceName}, 来自分类: ${dragState.value.serviceInfo?.fromCategoryName}`
    );
  } catch (error) {
    console.error('处理服务拖拽时出错:', error);
  }
}

// 在mounted中添加全局拖拽事件
onMounted(() => {
  // 设置所有分类面板为展开状态
  nextTick(() => {
    if (normalizedServiceData.value.length > 0) {
      expandedPanels.value = normalizedServiceData.value.map(
        (_: NormalizedCategory, index: number) => index.toString()
      );
    }
  });

  // 初始化分类顺序映射
  initCategoryOrderMap();

  // 阻止默认行为以允许放置
  document.addEventListener('dragover', (e: Event) => e.preventDefault());

  // 拖拽结束时清理状态
  document.addEventListener('dragend', () => {
    // 清除样式和状态
    DragStateManager.resetAllDragStates();

    // 完全重置拖拽状态
    dragState.value = {
      dragType: null,
      categoryInfo: null,
      serviceInfo: null,
      activeSpacer: null,
    };

    // 恢复底部按钮显示
    isDragging.value = false;

    console.log('拖拽结束，状态已重置', normalizedServiceData.value);
  });

  // 初始设置拖拽事件
  nextTick(() => {
    setTimeout(setupAllDragEvents, CONST.DRAG.EVENT_TIMEOUT);
  });
});
const isPreview = ref(false);
const disabledBtn = ref(false);

const handleBackPreview = async () => {
  updateButtonsVisibility({
    confirm: false,
    save: false,
    cancel: false,
    return: false,
  });
  // 返回刷新状态
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });
  try {
    const response = await CardServiceApi.fetchLatestServicePublishProcess();
    const { processId: id, state } = response;
    processId.value = id;
    lastState.value = state;
    updateProcessData(response.process, state);
    console.log('isPreview.value', isPreview.value);

    console.log('lastState.value', lastState.value);

    if (
      lastState.value === ProcessState.INIT ||
      lastState.value === ProcessState.CANCELED
    ) {
      isShowEntryButton.value = false;
      disabledBtn.value = false;
      isEdit.value = false;
      isShowButton.value = false;
      return;
    }
    updateDataAndState({
      serviceList: currentServiceData.value,
    });

    if (isPreview.value) {
      await fetchCurrentServiceData();
    }

    showStatusAlert.value = false;

    // 显示状态栏
    isShowBar.value = true;
    isShowButton.value = false;
    isEdit.value = false;
    isShowEntryButton.value = false;
    disabledBtn.value = true;
    // 预览页没有任何按钮
    updateButtonsVisibility({
      confirm: false,
      save: false,
      cancel: false,
      return: false,
    });

    // 根据状态调整显示效果
    if (
      lastState.value !== ProcessState.INIT &&
      lastState.value !== ProcessState.CANCELED
    ) {
      isShowBar.value = true;
      disabledBtn.value = true;
    }

    if (lastState.value === ProcessState.PUBLISHED) {
      isShowEntryButton.value = false;
      disabledBtn.value = false;
    }
    if (lastState.value === ProcessState.CANCELED) {
      isShowEntryButton.value = false;
      disabledBtn.value = false;
      isShowButton.value = false;
    }

    nextTick(() => {
      showStatusAlert.value = true;
      isShowBar.value = true;
    });
  } catch (error) {
    MessagePlugin.error('获取数据失败');
  } finally {
    loadingInstance.hide();
  }
};

onMounted(async () => {
  if (route.query.source === 'serviceEdit') {
    isEdit.value = true;
    await fetchLastServiceData();

    updateButtonsVisibility({
      confirm:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      save:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      cancel:
        lastState.value === ProcessState.DRAFT ||
        lastState.value === ProcessState.AUDITING ||
        lastState.value === ProcessState.AUDIT_PASS ||
        lastState.value === ProcessState.REJECTED ||
        lastState.value === ProcessState.APPROVED,
      return: true,
    });
    isEdit.value = true;
    isShowEntryButton.value = true;
    isShowButton.value = true;
    if (lastState.value === ProcessState.DRAFT && isEdit.value) {
      isShowBar.value = false;
      showStatusAlert.value = false;
    }

    return;
  }

  // 注释掉，因为产品改了暂时不需要进入状态页面
  if (window.history.state?.back) {
    previousRoute.value = window.history.state.back;
    console.log('previousRoute.value', previousRoute.value, window.history);
  }
  await fetchTutorial();

  await fetchLastServiceData();

  // 查看态
  if (route.params.type === 'preview') {
    isPreview.value = true;
    isShowBar.value = true;
    isShowButton.value = false;
    showStatusAlert.value = true;

    updateButtonsVisibility({
      confirm: false,
      save: false,
      cancel: false,
      return: false,
    });
    // 只要有bar就展示bar禁用按钮
    if (
      lastState.value !== ProcessState.INIT &&
      lastState.value !== ProcessState.CANCELED
    ) {
      isShowBar.value = true;
      disabledBtn.value = true;
      showStatusAlert.value = true;
    }

    if (lastState.value === ProcessState.PUBLISHED) {
      isShowEntryButton.value = false;
      disabledBtn.value = false;
      isShowBar.value = true;
      showStatusAlert.value = true;
    }
    if (lastState.value === ProcessState.CANCELED) {
      isShowEntryButton.value = false;
      disabledBtn.value = false;
      isShowButton.value = false;
      showStatusAlert.value = false;
      isShowBar.value = false;
    }
    if (lastState.value === ProcessState.DRAFT && isEdit.value) {
      isShowBar.value = false;
      showStatusAlert.value = false;
    }
  } else {
    // 新手作废 编辑态 不显示 bar
    if (lastState.value === ProcessState.CANCELED) {
      isShowEntryButton.value = true;
      disabledBtn.value = false;
      isShowButton.value = true;
      isEdit.value = true;
      isShowBar.value = false;
      showStatusAlert.value = false;
    }

    // 新手草稿 编辑态 不显示 bar
    if (lastState.value === ProcessState.DRAFT) {
      showStatusAlert.value = false;
    }

    // 新手完成 编辑态 显示 bar 点×之后再进来不显示bar
    if (lastState.value === ProcessState.PUBLISHED) {
      isEdit.value = true;
      disabledBtn.value = false;
      isShowBar.value = true;
      showStatusAlert.value = true;
    }
    if (!isEdit.value) {
      updateButtonsVisibility({
        confirm:
          lastState.value !== ProcessState.AUDITING &&
          lastState.value !== ProcessState.AUDIT_PASS &&
          lastState.value !== ProcessState.APPROVED &&
          lastState.value !== ProcessState.PUBLISHING,
        save: false,
        cancel:
          lastState.value === ProcessState.DRAFT ||
          lastState.value === ProcessState.AUDITING ||
          lastState.value === ProcessState.AUDIT_PASS ||
          lastState.value === ProcessState.REJECTED ||
          lastState.value === ProcessState.APPROVED,
        return: true,
      });
    } else {
      updateButtonsVisibility({
        confirm:
          lastState.value !== ProcessState.AUDITING &&
          lastState.value !== ProcessState.AUDIT_PASS &&
          lastState.value !== ProcessState.APPROVED &&
          lastState.value !== ProcessState.PUBLISHING,
        save:
          lastState.value !== ProcessState.AUDITING &&
          lastState.value !== ProcessState.AUDIT_PASS &&
          lastState.value !== ProcessState.APPROVED &&
          lastState.value !== ProcessState.PUBLISHING,
        cancel:
          lastState.value === ProcessState.DRAFT ||
          lastState.value === ProcessState.AUDITING ||
          lastState.value === ProcessState.AUDIT_PASS ||
          lastState.value === ProcessState.REJECTED ||
          lastState.value === ProcessState.APPROVED,
        return: true,
      });
    }
  }

  // 添加全局点击事件监听
  document.addEventListener('click', handleOutsideClick);
});
// 卸载时清理事件监听
onUnmounted(() => {
  // 移除所有事件监听器
  document.removeEventListener('dragover', categoryDragOverHandler);
  document.removeEventListener('dragleave', categoryDragLeaveHandler);
  document.removeEventListener('drop', categoryDropHandler);
  document.removeEventListener('dragover', spacerDragOverHandler);
  document.removeEventListener('dragleave', spacerDragLeaveHandler);
  document.removeEventListener('drop', spacerDropHandler);
  document.removeEventListener('dragstart', categoryDragStartHandler);
  document.removeEventListener('dragend', categoryDragEndHandler);
  document.removeEventListener('dragstart', handleTableRowDragStart);

  // 移除点击事件监听
  document.removeEventListener('click', handleOutsideClick);
});

// 移动分类到新位置
const moveCategoryToPosition = (categoryName: string, targetPosition: number) => {
  if (!categoryName || categoryName === CONST.DRAG.DEFAULT_CATEGORY) {
    return;
  }

  // 获取当前顺序
  const currentOrder = categoryOrderMap.value.get(categoryName);
  if (currentOrder === undefined) {
    return;
  }

  // 创建新的顺序映射
  const newOrderMap = new Map<string, number>();

  // 获取所有分类（除了"无分类"）并按顺序排序
  const entriesArray = Array.from(categoryOrderMap.value.entries()) as [string, number][];
  const categories = entriesArray
    .filter(([name]) => name !== CONST.DRAG.DEFAULT_CATEGORY)
    .sort((a, b) => a[1] - b[1])
    .map(([name]) => name);

  // 如果目标位置超出范围，调整为合法值
  const safeTargetPosition = Math.max(0, Math.min(targetPosition, categories.length - 1));

  // 当前分类在排序列表中的索引
  const currentIndex = categories.indexOf(categoryName);
  if (currentIndex === -1) {
    console.error(`分类 ${categoryName} 不在排序列表中`);
    return;
  }

  // 从列表中移除要移动的分类
  categories.splice(currentIndex, 1);

  // 在目标位置插入要移动的分类
  categories.splice(safeTargetPosition, 0, categoryName);

  // 更新顺序映射
  categories.forEach((name, index) => {
    newOrderMap.set(name, index);
  });

  // 保留"无分类"的原始顺序
  if (categoryOrderMap.value.has(CONST.DRAG.DEFAULT_CATEGORY)) {
    newOrderMap.set(
      CONST.DRAG.DEFAULT_CATEGORY,
      categoryOrderMap.value.get(CONST.DRAG.DEFAULT_CATEGORY)!
    );
  }

  // 更新分类顺序映射
  categoryOrderMap.value = newOrderMap;

  // 更新数据并按分类顺序排序
  const updatedData = DataManager.createOrUpdateData(internalData.value);
  // 使用新函数按分类顺序排序服务列表
  const sortedData = sortServiceListByCategoryOrder(updatedData, categoryOrderMap.value);
  updateInternalData(sortedData);

  // 等待DOM更新后，重新设置拖拽事件
  nextTick(() => {
    setTimeout(() => {
      setupAllDragEvents();

      // 确保分类展开
      if (normalizedServiceData.value.length > 0) {
        expandedPanels.value = normalizedServiceData.value.map(
          (_: NormalizedCategory, index: number) => index.toString()
        );
      }
    }, 500);
  });
};

// 修改normalizedServiceData计算属性，使其使用internalData而不是
const normalizedServiceData = computed(() => {
  // 从serviceList中提取不同的分类
  const classifyMap = new Map();
  const uniqueCategories = new Set<string>();

  if (internalData.value?.serviceList) {
    // 确保所有分类都被添加到orderMap中
    internalData.value.serviceList.forEach((service: ServiceItem) => {
      const classifyName = service.serviceClassifyName || '无分类';

      // 记录唯一的分类名称
      uniqueCategories.add(classifyName);

      // 如果这是一个新分类，添加到orderMap
      if (!categoryOrderMap.value.has(classifyName)) {
        categoryOrderMap.value.set(classifyName, categoryOrderMap.value.size);
      }

      if (!classifyMap.has(classifyName)) {
        classifyMap.set(classifyName, {
          name: classifyName,
          services: [],
        });
      }

      // 跳过空的服务名称，这些可能是分类标记
      if (!service.serviceName) {
        return;
      }

      // 确保服务有唯一ID
      if (!service.serviceId || service.serviceId === '') {
        service.serviceId = generateUniqueId();
      }

      const category = classifyMap.get(classifyName);
      category.services.push({
        id: service.serviceId,
        name: service.serviceName,
        linkType: service.jumpInfo.jumpType === 1 ? '小程序' : '网页',
        link:
          service.jumpInfo.jumpType === 1
            ? service.jumpInfo.miniProgram?.path || ''
            : service.jumpInfo.web?.path || '',
        originalService: service,
      });
    });
  }

  // 获取"无分类"和其他分类
  let defaultCategory = classifyMap.get('无分类');
  classifyMap.delete('无分类');

  // 按照categoryOrderMap中的顺序排序其他分类
  const sortedCategories = Array.from(classifyMap.values()).sort((a, b) => {
    const orderA = categoryOrderMap.value.get(a.name) ?? Number.MAX_SAFE_INTEGER;
    const orderB = categoryOrderMap.value.get(b.name) ?? Number.MAX_SAFE_INTEGER;
    return orderA - orderB;
  });

  // 确保"无分类"始终存在并显示
  if (!defaultCategory) {
    defaultCategory = {
      name: '无分类',
      services: [],
    };
  }
  // 无论是否有服务，都将"无分类"添加到末尾
  sortedCategories.push(defaultCategory);

  return sortedCategories;
});

// 计算所有服务的总数
const getTotalServicesCount = computed(() =>
  // 统计所有分类中服务的总数
  normalizedServiceData.value.reduce(
    (total, category) => total + category.services.length,
    0
  )
);

const isDataChanged = ref(false);

// 底部按钮显示配置
const buttonsConfig = ref({
  confirm: false,
  save: false,
  cancel: false,
  return: false,
});

// 根据不同状态更新按钮显示
const updateButtonsVisibility = (config: {
  confirm?: boolean;
  save?: boolean;
  cancel?: boolean;
  return?: boolean;
}) => {
  buttonsConfig.value = {
    ...buttonsConfig.value,
    ...config,
  };
};

/**
 * 更新内部数据但不发送更新事件
 * @param updatedData 更新后的数据
 */
const updateInternalData = (updatedData: ServiceData): void => {
  const filteredData = { ...updatedData };
  if (filteredData.serviceList) {
    // 按分类名称对服务进行分组
    const servicesByCategory = new Map<string, ServiceItem[]>();

    filteredData.serviceList.forEach((service) => {
      const categoryName = service.serviceClassifyName || '无分类';
      if (!servicesByCategory.has(categoryName)) {
        servicesByCategory.set(categoryName, []);
      }
      servicesByCategory.get(categoryName)!.push(service);
    });

    // 处理每个分类下的服务
    const processedServices: ServiceItem[] = [];

    servicesByCategory.forEach((services, _categoryName) => {
      // 当前分类下的empty_服务
      const emptyServices = services.filter((service) =>
        service.serviceId?.startsWith('empty_')
      );

      // 如果分类下只有一个服务且是empty_，则保留它
      if (services.length === 1 && emptyServices.length === 1) {
        processedServices.push(services[0]);
      } else {
        // 否则过滤掉empty_开头的服务
        const filteredCategoryServices = services.filter(
          (service) => !service.serviceId?.startsWith('empty_')
        );
        processedServices.push(...filteredCategoryServices);
      }
    });

    // 更新过滤后的服务列表
    filteredData.serviceList = processedServices;
  }

  internalData.value = JSON.parse(JSON.stringify(filteredData));

  emit('update:serviceData', JSON.parse(JSON.stringify(filteredData)));
  console.log('发送数据更新事件:', isDataChanged.value, filteredData);

  nextTick(() => {
    initCategoryOrderMap();
  });
};

// 添加分类弹窗状态
const showAddCategoryDialog = ref(false);
const isModifyCategoryMode = ref(false);
const currentEditCategoryName = ref('');

// 打开添加服务分类弹窗
const handleAddServiceCategory = async () => {
  if (isPreview.value && !isShowEntryButton.value) {
    console.log('lastState.value', lastState.value);

    isEdit.value = true;
    isShowButton.value = true;
    isShowEntryButton.value = true;
    disabledBtn.value = false;
    updateButtonsVisibility({
      confirm:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      save:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      cancel:
        lastState.value === ProcessState.DRAFT ||
        lastState.value === ProcessState.AUDITING ||
        lastState.value === ProcessState.AUDIT_PASS ||
        lastState.value === ProcessState.REJECTED ||
        lastState.value === ProcessState.APPROVED,
      return: true,
    });
    // 初始态，创建服务分类
    if (
      lastState.value === ProcessState.CANCELED ||
      lastState.value === ProcessState.PUBLISHED
    ) {
      const response = await CardServiceApi.createServicePublishProcess();
      processId.value = response.processId || '';
    } else {
      await fetchLastServiceData();
      isShowBar.value = true;
      showStatusAlert.value = true;
    }
    closeStatusAlert();
  } else {
    // 检查是否可以添加分类
    if (!checkCategoryCount(internalData.value)) {
      // 显示Popconfirm提示
      showCategoryLimitPopconfirm.value = true;
      return;
    }

    // 重置状态
    isModifyCategoryMode.value = false;
    currentEditCategoryName.value = '';
    showAddCategoryDialog.value = true;
  }
};

// 处理面板展开/折叠事件
const handlePanelChange = (val: string[] | number[] | (string | number)[]) => {
  // 将值转换为字符串数组
  expandedPanels.value = val.map((item) => item.toString());
};

// 关闭添加服务分类弹窗
const closeAddCategoryDialog = () => {
  showAddCategoryDialog.value = false;
};

// 确认添加分类
const confirmAddCategory = (categoryName: string) => {
  if (checkExistingCategory(normalizedServiceData.value, categoryName)) return;

  // 创建新的数据对象
  const updatedData = DataManager.createOrUpdateData(internalData.value);

  const emptyService = createEmptyCategoryService(categoryName);
  // 确保生成的空服务也有唯一ID
  if (!emptyService.serviceId || emptyService.serviceId === '') {
    emptyService.serviceId = generateUniqueId();
  }
  updatedData.serviceList!.unshift(emptyService);

  categoryOrderMap.value.forEach((order, name) => {
    if (name !== '无分类') {
      categoryOrderMap.value.set(name, order + 1);
    }
  });
  categoryOrderMap.value.set(categoryName, 0);

  // 更新到父组件
  updateInternalData(updatedData);
  MessagePlugin.success('已添加服务分类');

  showAddCategoryDialog.value = false;
};

// 确认修改分类
const confirmModifyCategory = (newCategoryName: string) => {
  // 获取当前分类名称
  const oldCategoryName = normalizedServiceData.value[currentCategoryIndex.value].name;

  // 如果名称没有变化，直接关闭弹窗
  if (newCategoryName === oldCategoryName) {
    showAddCategoryDialog.value = false;
    return;
  }

  // 检查新名称是否与其他分类重名
  if (checkExistingCategory(normalizedServiceData.value, newCategoryName)) {
    showAddCategoryDialog.value = false;
    return;
  }

  // 创建新的数据对象
  const updatedData = DataManager.createOrUpdateData(internalData.value);

  // 更新分类名称
  if (updatedData.serviceList) {
    updatedData.serviceList.forEach((service) => {
      if (service.serviceClassifyName === oldCategoryName) {
        service.serviceClassifyName = newCategoryName;
      }
    });
  }

  // 更新分类顺序映射
  if (categoryOrderMap.value.has(oldCategoryName)) {
    const order = categoryOrderMap.value.get(oldCategoryName);
    categoryOrderMap.value.delete(oldCategoryName);
    categoryOrderMap.value.set(newCategoryName, order!);
  }

  // 更新到父组件
  updateInternalData(updatedData);

  // 关闭弹窗并提示
  showAddCategoryDialog.value = false;
  MessagePlugin.success(CONST.SUCCESS.CATEGORY_MODIFIED);
};

// 打开修改分类弹窗
const handleModifyCategory = (index: number) => {
  // 获取当前分类名称
  const categoryName = normalizedServiceData.value[index].name;
  currentEditCategoryName.value = categoryName;
  currentCategoryIndex.value = index;

  // 设置为修改模式
  isModifyCategoryMode.value = true;

  // 打开弹窗
  showAddCategoryDialog.value = true;
};

const handleDeleteCategory = (index: number) => {
  currentCategoryIndex.value = index;
  showDeleteCategoryPopconfirm.value = true;
};

// 确认删除分类
const confirmDeleteCategory = () => {
  // 获取当前分类
  const categoryName = normalizedServiceData.value[currentCategoryIndex.value].name;

  // 创建新的数据对象
  const updatedData = DataManager.createOrUpdateData(internalData.value);

  // 将该分类下的服务移到"无分类"中
  if (updatedData.serviceList) {
    updatedData.serviceList.forEach((service) => {
      if (service.serviceClassifyName === categoryName) {
        // 保留服务的唯一ID
        service.serviceClassifyName = '无分类';
      }
    });
  }

  // 从分类顺序映射中移除该分类
  if (categoryOrderMap.value.has(categoryName)) {
    categoryOrderMap.value.delete(categoryName);
  }

  // 更新到父组件
  updateInternalData(updatedData);
  MessagePlugin.success(CONST.SUCCESS.CATEGORY_REMOVED);

  // 关闭确认弹窗
  showDeleteCategoryPopconfirm.value = false;
};

// 添加服务弹窗状态
const showAddServiceDialog = ref(false);
const isModifyMode = ref(false);
const currentServiceId = ref('');
const currentCategoryIndex = ref(0);
const currentCategoryName = ref('');
const currentEditService = ref<ServiceItem | null>(null);

// 打开添加服务弹窗
const handleAddService = (categoryIndex: number) => {
  const categoryName = normalizedServiceData.value[categoryIndex].name;

  // 设置当前分类
  currentCategoryIndex.value = categoryIndex;
  currentCategoryName.value = categoryName;
  isModifyMode.value = false;
  currentEditService.value = null;
  showAddServiceDialog.value = true;
};

// 关闭添加服务弹窗
const closeAddServiceDialog = () => {
  showAddServiceDialog.value = false;
};

// 确认添加服务
const confirmAddService = (service: ServiceItem) => {
  // 创建新的数据对象
  const updatedData = DataManager.createOrUpdateData(internalData.value);
  setDataChanged();

  // 修改模式
  if (isModifyMode.value && currentServiceId.value) {
    const serviceIndex = updatedData.serviceList!.findIndex(
      (item) => item.serviceId === currentServiceId.value
    );

    if (serviceIndex !== -1) {
      // 保持原有的serviceId
      service.serviceId = currentServiceId.value;
      updatedData.serviceList![serviceIndex] = service;
    }
    MessagePlugin.success('已修改服务');
  } else {
    MessagePlugin.success('已添加服务');

    // 添加新服务时生成唯一ID
    service.serviceId = generateUniqueId();
    updatedData.serviceList!.push(service);
  }

  updateInternalData(updatedData);
  showAddServiceDialog.value = false;
};

const handleModifyService = (categoryIndex: number, serviceIndex: number) => {
  const service = normalizedServiceData.value[categoryIndex].services[serviceIndex];
  console.log('修改handleModifyService', categoryIndex, serviceIndex, service);

  if (!service.originalService) {
    MessagePlugin.error(CONST.ERROR.GET_SERVICE);
    return;
  }

  // 设置当前服务信息
  currentCategoryIndex.value = categoryIndex;
  currentCategoryName.value = normalizedServiceData.value[categoryIndex].name;
  currentServiceId.value = service.id; // 使用生成的唯一ID
  isModifyMode.value = true;
  currentEditService.value = service.originalService;

  // 打开修改弹窗
  showAddServiceDialog.value = true;
};

const handleDeleteService = (categoryIndex: number, serviceIndex: number) => {
  // 设置当前索引
  currentCategoryIndex.value = categoryIndex;
  currentServiceIndex.value = serviceIndex;

  // 显示确认弹窗
  showDeleteServicePopconfirm.value = true;
};

// 确认删除服务
const confirmDeleteService = () => {
  const categoryServices =
    normalizedServiceData.value[currentCategoryIndex.value].services;
  const serviceId = categoryServices[currentServiceIndex.value].id;
  const currentCategoryName =
    normalizedServiceData.value[currentCategoryIndex.value].name;

  // 创建新的数据对象
  const updatedData = DataManager.createOrUpdateData(internalData.value);

  // 检查该服务是否为分类中的唯一服务
  const isSingleServiceInCategory =
    categoryServices.length === 1 && currentCategoryName !== '无分类';

  if (isSingleServiceInCategory) {
    // 如果是分类中的唯一服务，找到该服务的索引并替换为空分类标记服务，保持顺序
    const serviceIndex = updatedData.serviceList!.findIndex(
      (service) => service.serviceId === serviceId
    );

    if (serviceIndex !== -1) {
      // 直接替换为空分类标记服务，保持在原位置
      updatedData.serviceList![serviceIndex] =
        createEmptyCategoryService(currentCategoryName);
    }
  } else {
    // 不是分类中的唯一服务，直接删除服务
    updatedData.serviceList = updatedData.serviceList!.filter(
      (service) => service.serviceId !== serviceId
    );
  }

  // 更新到父组件
  updateInternalData(updatedData);
  MessagePlugin.success(CONST.SUCCESS.SERVICE_DELETED);

  // 关闭确认弹窗
  showDeleteServicePopconfirm.value = false;
};

// 分类上限提示状态
const showCategoryLimitPopconfirm = ref(false);

const onVisibleChange = (
  visible: boolean,
  context?: PopconfirmVisibleChangeContext | undefined
) => {
  if (context && context.trigger === 'confirm') {
    showCategoryLimitPopconfirm.value = visible;
  }
};

const showDeleteCategoryPopconfirm = ref(false);
const onDeleteCategoryVisibleChange = (
  visible: boolean,
  context?: PopconfirmVisibleChangeContext | undefined
) => {
  if (context && context.trigger === 'confirm') {
    confirmDeleteCategory();
    setDataChanged();
  } else if (context && context.trigger === 'cancel') {
    showDeleteCategoryPopconfirm.value = false;
  }
};

// 删除服务确认状态
const showDeleteServicePopconfirm = ref(false);
const currentServiceIndex = ref(0);

// 添加服务限制提示状态
const showAddServiceLimitPopconfirm = ref(false);

const onDeleteServiceVisibleChange = (
  visible: boolean,
  context?: PopconfirmVisibleChangeContext | undefined
) => {
  if (context) {
    if (context.trigger === 'confirm') {
      // 直接执行删除操作，不再检查服务总数
      confirmDeleteService();
      setDataChanged();
    } else if (context.trigger === 'cancel') {
      showDeleteServicePopconfirm.value = false;
    }
  }
};

/**
 * 处理添加服务按钮点击
 * @param categoryIndex 分类索引
 */
const handleAddServiceClick = (categoryIndex: number): void => {
  currentCategoryIndex.value = categoryIndex;
  const normalizedData = normalizedServiceData.value;

  // 检查是否可以添加服务
  if (!canAddService(normalizedData, categoryIndex)) {
    showAddServiceLimitPopconfirm.value = true;
    return;
  }

  // 可以添加服务，打开添加服务弹窗
  handleAddService(categoryIndex);
};

// 添加服务弹窗
const onAddServiceVisibleChange = (
  visible: boolean,
  context?: PopconfirmVisibleChangeContext | undefined
) => {
  if (context && context.trigger === 'confirm') {
    showAddServiceLimitPopconfirm.value = false;
  }
};

// 处理分类确认事件
const handleCategoryConfirm = (categoryName: string) => {
  console.log('handleCategoryConfirm 被调用:', isModifyCategoryMode.value, categoryName);
  setDataChanged();
  if (isModifyCategoryMode.value) {
    confirmModifyCategory(categoryName);
  } else {
    confirmAddCategory(categoryName);
  }
};

const columns = computed(() => {
  if (isShowButton.value) {
    return [
      {
        colKey: 'sort',
        width: 80,
        title: '顺序',
        className: 'column-order',
      },
      {
        colKey: 'name',
        title: '服务名称',
        width: 200,
        className: 'column-name',
      },
      {
        colKey: 'linkType',
        title: '服务链接类型',
        width: 160,
        className: 'column-link-type',
      },
      {
        colKey: 'link',
        title: '服务链接',
        className: 'column-link',
        ellipsis: true,
      },
      {
        colKey: 'operation',
        title: '操作',
        width: 120,
        className: 'column-actions',
        align: 'right' as const,
      },
    ];
  }
  return [
    {
      colKey: 'name',
      title: '服务名称',
      width: 240,
      className: 'column-name',
    },
    {
      colKey: 'linkType',
      title: '服务链接类型',
      width: 200,
      className: 'column-link-type',
    },
    {
      colKey: 'link',
      title: '服务链接',
      className: 'column-link',
      ellipsis: true,
    },
  ];
});

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const handleDragSort = (params: { newData: any[] }, categoryIndex: number) => {
  normalizedServiceData.value[categoryIndex].services = params.newData;

  // 获取所有分类的服务，确保保留前端生成的唯一ID
  const allServices = normalizedServiceData.value.map((item: NormalizedCategory) => {
    // 处理空services数组的情况
    if (item.services.length === 0 && item.name !== '无分类') {
      return [
        {
          serviceId: `empty_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          serviceName: '',
          serviceClassifyName: item.name,
          jumpInfo: {
            jumpType: 4,
            web: {
              path: '',
            },
            miniProgram: {
              appid: '',
              path: '',
            },
          },
        },
      ];
    }

    return item.services.map((service: NormalizedServiceItem) => {
      // 确保使用原始服务对象中的serviceId
      if (service.originalService) {
        // 如果originalService存在，使用其中的数据但保留当前的serviceId
        return {
          ...service.originalService,
          serviceId: service.id, // 使用normalizedServiceItem中的id作为serviceId
        };
      }
      return null;
    });
  });

  // 过滤掉null值并更新数据
  updateInternalData({
    serviceList: allServices.flat().filter(Boolean) as ServiceItem[],
  });
};

// 为所有分类容器添加拖拽事件
const setupCategoryDropEvents = () => {
  // 移除旧的事件监听器（如果存在）
  document.removeEventListener('dragover', categoryDragOverHandler);
  document.removeEventListener('dragleave', categoryDragLeaveHandler);
  document.removeEventListener('drop', categoryDropHandler);

  // 只有在可编辑状态下才添加事件监听器
  if (isEdit.value) {
    // 添加新的委托事件监听器
    document.addEventListener('dragover', categoryDragOverHandler);
    document.addEventListener('dragleave', categoryDragLeaveHandler);
    document.addEventListener('drop', categoryDropHandler);
  }
};

// 通过事件委托处理拖拽
function categoryDragOverHandler(e: Event) {
  e.preventDefault();
  if (dragState.value.dragType !== 'service') return;

  // 找到目标分类元素
  const categoryEl = findParentWithClass(e.target as HTMLElement, 'service-category');
  if (!categoryEl || categoryEl.classList.contains(CONST.CLASS.DRAGGING_SOURCE)) return;

  // 高亮显示可放置的目标分类
  DragStateManager.setDropTargetStyle(categoryEl, true);
}

function categoryDragLeaveHandler(e: Event) {
  // 找到目标分类元素
  const categoryEl = findParentWithClass(e.target as HTMLElement, 'service-category');
  if (!categoryEl) return;

  // 判断是否真的离开了分类元素
  const relatedTarget = (e as DragEvent).relatedTarget as HTMLElement;
  if (categoryEl.contains(relatedTarget)) return;

  DragStateManager.setDropTargetStyle(categoryEl, false);
}

function categoryDropHandler(e: Event) {
  e.preventDefault();
  e.stopPropagation();

  if (dragState.value.dragType !== 'service' || !dragState.value.serviceInfo) return;

  // 找到目标分类元素
  const categoryEl = findParentWithClass(e.target as HTMLElement, 'service-category');
  if (!categoryEl) return;

  DragStateManager.setDropTargetStyle(categoryEl, false);

  const { serviceId } = dragState.value.serviceInfo;

  // 获取分类信息
  const categoryName = categoryEl.dataset.categoryName || CONST.DRAG.DEFAULT_CATEGORY;
  const categoryIndex = parseInt(categoryEl.dataset.categoryIndex || '0', 10);

  // 移动服务到新分类
  moveServiceToCategory(serviceId, categoryName, categoryIndex);

  // 恢复底部按钮显示
  isDragging.value = false;
}

// 设置category-spacer的放置事件
const setupCategorySpacerEvents = () => {
  // 移除旧的事件监听器（如果存在）
  document.removeEventListener('dragover', spacerDragOverHandler);
  document.removeEventListener('dragleave', spacerDragLeaveHandler);
  document.removeEventListener('drop', spacerDropHandler);

  // 只有在可编辑状态下才添加事件监听器
  if (isEdit.value) {
    // 添加新的委托事件监听器
    document.addEventListener('dragover', spacerDragOverHandler);
    document.addEventListener('dragleave', spacerDragLeaveHandler);
    document.addEventListener('drop', spacerDropHandler);
  }
};

// 通过事件委托处理分隔区域的拖拽
function spacerDragOverHandler(e: Event) {
  e.preventDefault();

  // 如果没有拖拽信息，直接返回
  if (dragState.value.dragType === null) return;

  // 找到目标spacer元素
  const spacerEl = findParentWithClass(e.target as HTMLElement, 'category-spacer');
  if (!spacerEl) return;

  // 高亮显示目标spacer
  spacerEl.classList.add(CONST.CLASS.SPACER_ACTIVE);

  // 记录当前活跃的spacer索引
  const spacerIndex = parseInt(spacerEl.dataset.spacerIndex || '0', 10);
  dragState.value.activeSpacer = spacerIndex;
}

function spacerDragLeaveHandler(e: Event) {
  // 找到目标spacer元素
  const spacerEl = findParentWithClass(e.target as HTMLElement, 'category-spacer');
  if (!spacerEl) return;

  // 判断是否真的离开了spacer元素
  const relatedTarget = (e as DragEvent).relatedTarget as HTMLElement;
  if (spacerEl.contains(relatedTarget)) return;

  spacerEl.classList.remove(CONST.CLASS.SPACER_ACTIVE);

  // 重置活跃的spacer索引
  const spacerIndex = parseInt(spacerEl.dataset.spacerIndex || '0', 10);
  if (dragState.value.activeSpacer === spacerIndex) {
    dragState.value.activeSpacer = null;
  }
}

function spacerDropHandler(e: Event) {
  e.preventDefault();
  e.stopPropagation();

  // 找到目标spacer元素
  const spacerEl = findParentWithClass(e.target as HTMLElement, 'category-spacer');
  if (!spacerEl) return;

  spacerEl.classList.remove(CONST.CLASS.SPACER_ACTIVE);

  // 获取spacer索引
  const spacerIndex = parseInt(spacerEl.dataset.spacerIndex || '0', 10);
  console.log(`放置在spacer: ${spacerIndex}`);

  // 确定是哪种类型的拖拽
  const isServiceDrag = dragState.value.dragType === 'service';
  const isCategoryDrag = dragState.value.dragType === 'category';

  // 优先处理分类拖拽
  if (isCategoryDrag && dragState.value.categoryInfo) {
    const { categoryName, fromIndex } = dragState.value.categoryInfo;

    if (categoryName === CONST.DRAG.DEFAULT_CATEGORY || !categoryName) {
      return; // 不允许拖拽"无分类"
    }

    // 获取可排序的分类(不包括"无分类")
    const sortableCategories = normalizedServiceData.value
      .filter((cat) => cat.name !== CONST.DRAG.DEFAULT_CATEGORY)
      .map((cat) => cat.name);

    // 计算真实的目标位置
    // spacer索引与分类实际位置的对应关系：
    // spacer 0: 在第一个分类之前
    // spacer 1: 在第一个分类之后/第二个分类之前
    // spacer n: 在第n个分类之后/第n+1个分类之前
    let targetPosition;

    if (spacerIndex === 0) {
      // 放在第一个位置
      targetPosition = 0;
    } else if (spacerIndex <= sortableCategories.length) {
      // 正常情况下，spacer索引就是目标位置
      targetPosition = spacerIndex - 1;

      // 如果是向下拖拽
      if (fromIndex !== null && fromIndex < targetPosition) {
        targetPosition = spacerIndex;
      }
    } else {
      // 超出范围，放在最后一个位置
      targetPosition = sortableCategories.length - 1;
    }

    // 移动分类
    moveCategoryToPosition(categoryName, targetPosition);

    // 隐藏所有spacer
    document.querySelectorAll('.category-spacer').forEach((spacer) => {
      spacer.classList.remove(CONST.CLASS.SPACER_VISIBLE, CONST.CLASS.SPACER_ACTIVE);
    });

    // 清理样式
    document.querySelectorAll('.service-category').forEach((el) => {
      el.classList.remove(
        CONST.CLASS.CATEGORY_DRAGGING,
        CONST.CLASS.DRAGGING_SOURCE,
        CONST.CLASS.DROP_TARGET
      );
    });

    // 重置状态
    dragState.value.categoryInfo = null;
    dragState.value.dragType = null;
    dragState.value.activeSpacer = null;

    // 恢复底部按钮显示
    isDragging.value = false;

    nextTick(() => {
      initCategoryOrderMap();
    });

    return;
  }

  // 处理服务拖拽
  if (isServiceDrag && dragState.value.serviceInfo) {
    const { serviceId } = dragState.value.serviceInfo;

    // 根据spacer索引确定目标分类
    let targetCategoryName = CONST.DRAG.DEFAULT_CATEGORY;
    let targetCategoryIndex = 0;

    // 找到目标分类 - 使用规则：spacer索引对应上一个分类
    // spacer 0 = 第一个分类前
    // spacer 1 = 第一个分类后/第二个分类前
    // spacer n = 第n个分类后

    if (spacerIndex === 0) {
      // 第一个spacer总是"无分类"之前
      targetCategoryName = CONST.DRAG.DEFAULT_CATEGORY;
      targetCategoryIndex = 0;
    } else {
      // 找到对应的分类 - 每个spacer对应前一个分类
      const categoryIndex = spacerIndex - 1;

      // 确保索引在范围内
      if (categoryIndex >= 0 && categoryIndex < normalizedServiceData.value.length) {
        const category = normalizedServiceData.value[categoryIndex];
        targetCategoryName = category.name;
        targetCategoryIndex = categoryIndex;
      }
    }

    console.log(`目标分类确定为: ${targetCategoryName}, 索引: ${targetCategoryIndex}`);

    // 移动服务
    if (serviceId) {
      moveServiceToCategory(serviceId, targetCategoryName, targetCategoryIndex);
    }

    // 清除所有样式
    document.querySelectorAll('.service-category').forEach((el) => {
      el.classList.remove(CONST.CLASS.DRAGGING_SOURCE, CONST.CLASS.DROP_TARGET);
    });

    document.querySelectorAll('.category-spacer').forEach((spacer) => {
      spacer.classList.remove(CONST.CLASS.SPACER_VISIBLE, CONST.CLASS.SPACER_ACTIVE);
    });

    // 重置拖拽状态
    dragState.value.serviceInfo = null;
    dragState.value.dragType = null;

    // 恢复底部按钮显示
    isDragging.value = false;
    return;
  }
}

// 重新绑定表格行拖拽事件
const rebindTableDragEvents = () => {
  // 遍历所有分类，重新绑定表格行拖拽
  document.querySelectorAll('.service-category').forEach((categoryEl) => {
    const catEl = categoryEl as HTMLElement;

    // 从数据模型中找到对应分类
    const categoryName = catEl.dataset.categoryName || '';
    const categoryIndex = normalizedServiceData.value.findIndex(
      (cat: NormalizedCategory) => cat.name === categoryName
    );

    if (categoryIndex === -1) return; // 找不到对应的分类

    // 找到表格元素
    const tableEl = catEl.querySelector('.t-table');
    if (!tableEl) return;

    // 为表格中的每个可拖拽行重新设置数据属性
    tableEl.querySelectorAll('.t-table__row').forEach((row, rowIdx) => {
      const rowEl = row as HTMLElement;
      rowEl.setAttribute('data-category-name', categoryName);
      rowEl.setAttribute('data-category-index', String(categoryIndex));
      rowEl.setAttribute('data-row-index', String(rowIdx));
    });
  });
};
watch(
  () => processId.value,
  (newVal, oldVal) => {
    console.log('newVal', newVal, 'oldVal', oldVal);
  }
);
watch(
  [() => normalizedServiceData.value],
  () => {
    nextTick(() => {
      setTimeout(setupAllDragEvents, CONST.DRAG.EVENT_TIMEOUT);
    });
  },
  { deep: true }
);

const continueEdit = async () => {
  console.log('继续编辑', processData.value);
  // const serviceList = processData.value.content?.serviceList || [];

  // internalData.value = {
  //   serviceList,
  // };
  // updateInternalData({ serviceList });
  isEdit.value = true;
  isShowButton.value = true;
  isShowEntryButton.value = true;
  disabledBtn.value = false;
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });
  try {
    const response = await CardServiceApi.fetchLatestServicePublishProcess();
    const { processId: id, state } = response;
    updateProcessData(response.process, state);
    processId.value = id;
    isEdit.value = true;
    initCategoryOrderMap();
    // 更新数据显示
    if (response.process?.content?.serviceList) {
      updateDataAndState({
        serviceList: response.process.content.serviceList,
      });
    }

    updateButtonsVisibility({
      confirm:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      save:
        lastState.value !== ProcessState.AUDITING &&
        lastState.value !== ProcessState.AUDIT_PASS &&
        lastState.value !== ProcessState.APPROVED &&
        lastState.value !== ProcessState.PUBLISHING,
      cancel:
        lastState.value === ProcessState.DRAFT ||
        lastState.value === ProcessState.AUDITING ||
        lastState.value === ProcessState.AUDIT_PASS ||
        lastState.value === ProcessState.REJECTED ||
        lastState.value === ProcessState.APPROVED,
      return: true,
    });
  } catch (error) {
    MessagePlugin.error('获取数据失败');
  } finally {
    loadingInstance.hide();
  }
};
const showPublishDialog = ref(false);
const editPublish = () => {
  showPublishDialog.value = true;
  // router.push({
  //   path: `/home/<USER>/card-config-preview/${processId.value}`,
  // });
};

const viewDetails = async () => {
  // const serviceList = processData.value.content?.serviceList || [];
  // internalData.value = {
  //   serviceList,
  // };
  // 236展示撤销
  updateButtonsVisibility({
    confirm: false,
    save: false,
    cancel:
      lastState.value === ProcessState.DRAFT ||
      lastState.value === ProcessState.AUDITING ||
      lastState.value === ProcessState.AUDIT_PASS ||
      lastState.value === ProcessState.REJECTED ||
      lastState.value === ProcessState.APPROVED,
    return: true,
  });
  // updateInternalData({ serviceList });
  const loadingInstance = LoadingPlugin({
    fullscreen: true,
    text: '加载中...',
    attach: 'body',
  });
  try {
    const response = await CardServiceApi.fetchLatestServicePublishProcess();
    const { processId: id, state } = response;
    updateProcessData(response.process, state);
    processId.value = id;
    isEdit.value = true;
    // 更新数据显示
    if (response.process?.content?.serviceList) {
      updateDataAndState({
        serviceList: response.process.content.serviceList,
      });
    }
    initCategoryOrderMap();

    isEdit.value = false;
    // 服务编辑状态时，不应有"修改"按钮，应该是所有编辑态的按钮都在但为失效状态
    isShowEntryButton.value = true;
    isShowButton.value = true;
  } catch (error) {
    MessagePlugin.error('获取数据失败');
  } finally {
    loadingInstance.hide();
  }
};

const closeStatusAlert = () => {
  showStatusAlert.value = false;
};

// 处理撤销成功后的逻辑
const handleCancelSuccess = async () => {
  // 隐藏状态提示
  showStatusAlert.value = false;
  isShowBar.value = false;

  // 设置撤销按钮为禁用状态
  cancelDisabled.value = true;

  // 新客撤销成功
  if (isfirst.value) {
    internalData.value = {
      serviceList: [],
    };
    updateInternalData({ serviceList: [] });

    // 设置为可编辑状态
    isEdit.value = true;

    // 显示所有按钮
    updateButtonsVisibility({
      confirm: true,
      save: true,
      cancel: true,
      return: true,
    });

    // 重置数据变更标志
    isDataChanged.value = false;
    closeStatusAlert();
    processId.value = await createService();
    isShowEntryButton.value = true;
    disabledBtn.value = false;
    isShowButton.value = true;
    isEdit.value = true;
  } else {
    router.push({
      path: `/home/<USER>/card-detail`,
    });
  }
};
</script>

<style scoped>
@import './style/index.css';
.card-service-dialog {
  width: 960px !important;
  height: 560px !important;
}
</style>

<style>
.t-table__ele--draggable-chosen {
  background-color: #ebfaf2 !important;
  border-color: #ebfaf2 !important;
  border-radius: 8px !important;
}

.t-table__ele--draggable-ghost {
  background-color: #ebfaf2 !important;
  color: #07c160 !important;
  border-color: #ebfaf2 !important;
}

tr[draggable='true'] {
  border-radius: 8px !important;
  color: #07c160 !important;
  background-color: #ebfaf2 !important;
}
</style>
