import {
  type ServiceData,
  type ServiceItem,
  type NormalizedCategory,
  type NormalizedServiceData,
  type ValidationResult,
  type DeleteServiceButtonConfig,
  type MoveServiceResult,
} from './utils/type';
import { MessagePlugin } from 'tdesign-vue-next';
import { CONST } from './utils/constants';

/**
 * 数据管理器类
 * 负责处理服务数据的处理、转换和验证
 */
export class DataManager {
  /**
   * 确保服务列表中的每个服务都有唯一ID
   * @param services 服务列表
   * @returns 带有唯一ID的服务列表
   */
  static ensureUniqueIds(services: ServiceItem[]): ServiceItem[] {
    return services.map((service) => {
      // 如果没有serviceId或者serviceId为空字符串，生成新的ID
      if (!service.serviceId || service.serviceId === '') {
        service.serviceId = DataManager.generateUniqueId();
      }
      return service;
    });
  }

  /**
   * 生成唯一ID
   * @returns 唯一ID字符串
   */
  static generateUniqueId(): string {
    return `service_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 检查服务ID是否是有效的服务端ID（非前端生成的临时ID）
   * @param id 服务ID
   * @returns 是否是有效的服务端ID
   */
  static isValidServerServiceId(id: string): boolean {
    return !!(id && id !== '' && !id.startsWith('service_'));
  }

  /**
   * 准备数据用于保存
   * @param serviceData 服务数据
   * @returns 准备好的用于保存的数据
   */
  static prepareDataForSave(serviceData: ServiceData): { serviceList: ServiceItem[] } {
    return {
      serviceList:
        serviceData.serviceList
          ?.filter((service) => {
            // 过滤掉空服务名的服务
            if (service.serviceName === '') return false;

            // 过滤掉serviceId以empty_开头的服务
            if (service.serviceId?.startsWith('empty_')) return false;

            return true;
          })
          .map((service) => {
            // 检查是否是有效的服务端ID
            const isValidId = DataManager.isValidServerServiceId(service.serviceId || '');

            const processedService = { ...service };

            if (processedService.serviceClassifyName === '无分类') {
              processedService.serviceClassifyName = '';
            }

            if (processedService.jumpInfo) {
              const { jumpType } = processedService.jumpInfo;
              if (jumpType === 1) {
                processedService.jumpInfo = {
                  jumpType: 1,
                  miniProgram: {
                    appid: processedService.jumpInfo.miniProgram?.appid || '',
                    path: processedService.jumpInfo.miniProgram?.path || '',
                  },
                };
              } else {
                processedService.jumpInfo = {
                  jumpType: 4,
                  web: {
                    path: processedService.jumpInfo.web?.path || '',
                  },
                };
              }
            }

            if (isValidId) {
              // 如果是有效的服务端ID保留
              return processedService;
            }
            // 如果是前端生成的ID，不发送到服务器
            const { serviceId, ...serviceWithoutId } = processedService;
            return { ...serviceWithoutId };
          }) || [],
    };
  }

  /**
   * 创建或更新数据对象
   * @param currentData 当前数据
   * @returns 更新后的数据对象
   */
  static createOrUpdateData(currentData?: ServiceData): ServiceData {
    // 创建新的数据对象
    let updatedData: ServiceData;

    if (!currentData?.serviceList) {
      // 如果没有数据，创建新数据
      updatedData = {
        serviceList: [],
      };
    } else {
      // 复制现有数据
      updatedData = JSON.parse(JSON.stringify(currentData));
    }

    // 确保serviceList存在
    if (!updatedData.serviceList) {
      updatedData.serviceList = [];
    }

    return updatedData;
  }

  /**
   * 初始化分类顺序映射
   * @param serviceData 服务数据
   * @returns 分类顺序映射
   */
  static initCategoryOrderMap(serviceData: ServiceData): Map<string, number> {
    const map = new Map<string, number>();

    if (serviceData?.serviceList) {
      let order = 0;
      // 用Set记录已添加的分类，避免重复
      const addedCategories = new Set<string>();

      serviceData.serviceList.forEach((service: ServiceItem) => {
        const categoryName = service.serviceClassifyName || '无分类';

        // 跳过"无分类"，它不参与排序
        if (categoryName === '无分类') {
          return;
        }

        if (!addedCategories.has(categoryName)) {
          map.set(categoryName, order);
          order += 1;
          addedCategories.add(categoryName);
        }
      });
    }

    return map;
  }

  /**
   * 将服务数据转换为规范化的分类数据
   * @param serviceData 服务数据
   * @param categoryOrderMap 分类顺序映射
   * @returns 规范化的分类数据
   */
  static normalizeServiceData(
    serviceData: ServiceData,
    categoryOrderMap: Map<string, number>
  ): NormalizedCategory[] {
    // 从serviceList中提取不同的分类
    const classifyMap = new Map<string, NormalizedCategory>();

    if (serviceData?.serviceList && serviceData.serviceList.length > 0) {
      const uniqueCategories = new Set<string>();
      // 确保所有分类都被添加到orderMap中
      serviceData.serviceList.forEach((service: ServiceItem) => {
        const classifyName = service.serviceClassifyName || '无分类';

        // 记录唯一的分类名称
        uniqueCategories.add(classifyName);

        // 如果这是一个新分类，添加到orderMap
        if (!categoryOrderMap.has(classifyName) && classifyName !== '无分类') {
          categoryOrderMap.set(classifyName, categoryOrderMap.size);
        }

        if (!classifyMap.has(classifyName)) {
          classifyMap.set(classifyName, {
            name: classifyName,
            services: [],
          });
        }

        // 跳过空的服务名称，这些可能是分类标记
        if (!service.serviceName) {
          return;
        }

        // 确保服务有唯一ID
        if (!service.serviceId || service.serviceId === '') {
          service.serviceId = DataManager.generateUniqueId();
        }

        const category = classifyMap.get(classifyName);
        if (category) {
          category.services.push({
            id: service.serviceId,
            name: service.serviceName,
            linkType: service.jumpInfo.jumpType === 1 ? '小程序' : '网页',
            link:
              service.jumpInfo.jumpType === 1
                ? service.jumpInfo.miniProgram?.path || ''
                : service.jumpInfo.web?.path || '',
            originalService: service,
          });
        }
      });
    }

    // 获取"无分类"和其他分类
    let defaultCategory = classifyMap.get('无分类');
    classifyMap.delete('无分类');

    // 按照categoryOrderMap中的顺序排序其他分类
    const sortedCategories = Array.from(classifyMap.values()).sort((a, b) => {
      const orderA = categoryOrderMap.get(a.name) ?? Number.MAX_SAFE_INTEGER;
      const orderB = categoryOrderMap.get(b.name) ?? Number.MAX_SAFE_INTEGER;
      return orderA - orderB;
    });

    // 确保"无分类"始终存在并显示
    if (!defaultCategory) {
      defaultCategory = {
        name: '无分类',
        services: [],
      };
    }
    // 无论是否有服务，都将"无分类"添加到末尾
    sortedCategories.push(defaultCategory);

    return sortedCategories;
  }

  /**
   * 检查服务数量是否已达上限
   * @param categories 当前分类列表
   * @param categoryIndex 分类索引
   * @returns 是否可以添加新服务
   */
  static checkServiceCount(
    categories: NormalizedServiceData,
    categoryIndex: number
  ): boolean {
    // 检查该分类下的服务数量是否已达上限
    if (
      categories[categoryIndex]?.services.length >= CONST.LIMIT.MAX_SERVICES_PER_CATEGORY
    ) {
      MessagePlugin.warning(CONST.MSG.CATEGORY_MAX_LIMIT);
      return false;
    }

    // 检查总服务数量是否已达上限
    let totalServices = 0;
    categories.forEach((category: NormalizedCategory) => {
      totalServices += category.services?.length || 0;
    });

    if (totalServices >= CONST.LIMIT.MAX_TOTAL_SERVICES) {
      MessagePlugin.warning(CONST.MSG.SERVICE_MAX_LIMIT);
      return false;
    }

    return true;
  }
}

/**
 * 检查文本是否包含表情符号
 * @param text 需要检查的文本
 * @returns 是否包含表情符号
 */
export const containsEmoji = (text: string): boolean => {
  if (!text) return false;
  // 使用正则表达式检查emoji
  const emojiRegex =
    /[\u{1F300}-\u{1F5FF}\u{1F600}-\u{1F64F}\u{1F680}-\u{1F6FF}\u{1F700}-\u{1F77F}\u{1F780}-\u{1F7FF}\u{1F800}-\u{1F8FF}\u{1F900}-\u{1F9FF}\u{1FA00}-\u{1FA6F}\u{1FA70}-\u{1FAFF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/u;
  return emojiRegex.test(text);
};

/**
 * 校验分类名称
 * @param name 分类名称
 * @returns 校验结果对象，包含是否有效和错误信息
 */
export const validateCategoryName = (name: string): ValidationResult => {
  if (name.replace(/\s/g, '').length > 6) {
    return {
      valid: false,
      message: CONST.MSG.CATEGORY_NAME_TOO_LONG,
    };
  }

  // 校验是否包含表情符号
  if (containsEmoji(name)) {
    return {
      valid: false,
      message: CONST.MSG.CATEGORY_NAME_EMOJI,
    };
  }

  // 通过所有校验
  return {
    valid: true,
    message: '',
  };
};

/**
 * 校验服务名称
 * @param name 服务名称
 * @returns 校验结果对象，包含是否有效和错误信息
 */
export const validateServiceName = (name: string): ValidationResult => {
  // 校验是否为空
  if (!name || name.trim() === '') {
    return {
      valid: false,
      message: '服务名称必填',
    };
  }

  if (name.replace(/\s/g, '').length > 8) {
    return {
      valid: false,
      message: CONST.MSG.SERVICE_NAME_TOO_LONG,
    };
  }

  // 校验是否包含表情符号
  if (containsEmoji(name)) {
    return {
      valid: false,
      message: CONST.MSG.SERVICE_NAME_EMOJI,
    };
  }

  // 通过所有校验
  return {
    valid: true,
    message: '',
  };
};

/**
 * 检查是否已存在同名分类
 * @param categories 当前分类列表
 * @param name 分类名称
 * @returns 是否存在同名分类
 */
export const checkExistingCategory = (
  categories: NormalizedServiceData,
  name: string
): boolean => {
  const existingCategory = categories.find((c) => c.name === name);

  if (existingCategory) {
    MessagePlugin.warning(CONST.MSG.CATEGORY_EXISTS);
    return true;
  }

  return false;
};

/**
 * 检查分类数量是否已达上限
 * @param serviceData 服务数据
 * @returns 是否可以添加新分类，如果达到上限返回false
 */
export const checkCategoryCount = (serviceData?: ServiceData): boolean => {
  if (!serviceData?.serviceList) return true;

  const classifyNames = new Set<string>();
  serviceData.serviceList.forEach((service: ServiceItem) => {
    if (service.serviceClassifyName && service.serviceClassifyName !== '无分类') {
      classifyNames.add(service.serviceClassifyName);
    }
  });
  if (classifyNames.size >= CONST.LIMIT.MAX_CATEGORIES) {
    return false;
  }

  return true;
};

/**
 * 创建新的空分类标记服务
 * @param categoryName 分类名称
 * @returns 新的服务对象
 */
export const createEmptyCategoryService = (categoryName: string): ServiceItem => ({
  serviceId: `empty_${Date.now().toString()}`,
  serviceName: '',
  serviceClassifyName: categoryName,
  jumpInfo: {
    jumpType: 0,
    miniProgram: {
      appid: '',
      path: '',
    },
    web: {
      path: '',
    },
  },
});

/**
 * 检查是否可以添加服务
 * @param categories 分类列表
 * @param categoryIndex 分类索引
 * @returns 是否可以添加服务
 */
export const canAddService = (
  categories: NormalizedServiceData,
  categoryIndex: number
): boolean => {
  if (!categories?.[categoryIndex]?.services) {
    return false;
  }

  const categoryName = categories[categoryIndex].name;
  const categoryServices = categories[categoryIndex].services;

  // 检查总服务数量是否已达上限
  let totalServices = 0;
  categories.forEach((category: NormalizedCategory) => {
    totalServices += category.services?.length || 0;
  });

  if (totalServices >= CONST.LIMIT.MAX_TOTAL_SERVICES) {
    return false;
  }

  // 如果是无分类，无需检查单个分类下的服务数量限制
  if (categoryName === '无分类') {
    return true;
  }

  // 检查该分类下的服务数量是否已达上限
  if (categoryServices.length >= CONST.LIMIT.MAX_SERVICES_PER_CATEGORY) {
    return false;
  }

  return true;
};

/**
 * 获取添加服务popconfirm内容
 * @param categories 分类列表
 * @param categoryIndex 分类索引
 * @returns 提示内容
 */
export const getAddServiceContent = (
  categories: NormalizedServiceData,
  categoryIndex: number
): string => {
  if (!categories?.[categoryIndex]?.services) {
    return '';
  }

  const categoryName = categories[categoryIndex].name;
  const categoryServices = categories[categoryIndex].services;

  // 先检查总数
  let totalServices = 0;
  categories.forEach((category: NormalizedCategory) => {
    totalServices += category.services?.length || 0;
  });

  if (totalServices >= CONST.LIMIT.MAX_TOTAL_SERVICES) {
    return CONST.MSG.TOTAL_SERVICE_LIMIT;
  }

  // 如果是无分类，不需要检查单个分类下的服务数量限制
  if (categoryName === '无分类') {
    return '';
  }

  // 检查该分类下的服务数量是否已达上限
  if (categoryServices.length >= CONST.LIMIT.MAX_SERVICES_PER_CATEGORY) {
    return CONST.MSG.CATEGORY_SERVICE_LIMIT;
  }

  return '';
};

/**
 * 获取删除服务的popconfirm内容
 * @param categories 分类列表
 * @param categoryIndex 分类索引
 * @returns 提示内容
 */
export const getDeleteServiceContent = (
  _categories: NormalizedServiceData,
  _categoryIndex: number
): string =>
  // 无论服务总数多少，都直接返回删除服务的常规提示信息
  CONST.MSG.DELETE_SERVICE;

/**
 * 获取删除服务的popconfirm按钮配置
 * @param categories 分类列表
 * @param categoryIndex 分类索引
 * @returns 按钮配置
 */
export const getDeleteServiceBtnConfig = (
  _categories: NormalizedServiceData,
  _categoryIndex: number
): DeleteServiceButtonConfig =>
  // 无论服务总数多少，都返回确认删除按钮
  ({
    content: CONST.BTN.CONFIRM_DELETE,
    theme: 'danger' as const,
  });

/**
 * 获取删除服务的cancel按钮配置
 * @param categories 分类列表
 * @param categoryIndex 分类索引
 * @returns 按钮配置或null
 */
export const getDeleteServiceCancelBtn = (
  _categories: NormalizedServiceData,
  _categoryIndex: number
): { content: string; theme: 'default' } | null =>
  // 无论服务总数多少，都显示取消按钮
  ({
    content: CONST.BTN.CANCEL,
    theme: 'default' as const,
  });

/**
 * 查找具有指定类名的父元素
 * @param element 起始元素
 * @param className 要查找的类名
 * @returns 找到的父元素或null
 */
export const findParentWithClass = (
  element: HTMLElement | null,
  className: string
): HTMLElement | null => {
  let current = element;
  while (current) {
    if (current.classList?.contains(className)) {
      return current;
    }
    current = current.parentElement;
  }
  return null;
};

/**
 * 清理所有拖拽相关的样式
 */
export const clearDragStyles = (): void => {
  // 清除所有分类的拖拽样式
  document.querySelectorAll('.service-category').forEach((el) => {
    el.classList.remove(
      CONST.CLASS.DRAGGING_SOURCE,
      CONST.CLASS.DROP_TARGET,
      CONST.CLASS.CATEGORY_DRAGGING
    );
  });

  // 清除所有spacer的拖拽样式
  document.querySelectorAll('.category-spacer').forEach((spacer) => {
    spacer.classList.remove(CONST.CLASS.SPACER_ACTIVE, CONST.CLASS.SPACER_VISIBLE);
  });
};

/**
 * 为分类头部设置拖拽属性
 */
export const setupCategoryDragAttributes = (): void => {
  document.querySelectorAll('.category-header').forEach((element) => {
    const header = element as HTMLElement;
    const categoryEl = findParentWithClass(header, 'service-category');

    if (!categoryEl) return;

    const categoryName = categoryEl.dataset.categoryName || '';

    // 如果是"无分类"，则不允许拖拽
    if (categoryName === CONST.DRAG.DEFAULT_CATEGORY) {
      header.setAttribute('draggable', 'false');
      header.removeAttribute(CONST.DRAG.DRAG_TYPE_ATTR);
      return;
    }

    // 其他分类
    header.setAttribute('draggable', 'true');
    header.setAttribute(CONST.DRAG.DRAG_TYPE_ATTR, CONST.DRAG.CATEGORY_TYPE);
  });
};

/**
 * 格式化字符串，替换{n}占位符
 * @param template 模板字符串
 * @param args 替换参数
 * @returns 格式化后的字符串
 */
export const formatString = (template: string, ...args: unknown[]): string =>
  template.replace(/{(\d+)}/g, (match, number) => {
    const replacement = args[number];
    return typeof replacement !== 'undefined' ? String(replacement) : match;
  });

/**
 * 校验小程序路径
 * @param path 小程序路径
 * @returns 校验结果对象，包含是否有效和错误信息
 */
export const validateMiniProgramPath = (path: string): ValidationResult => {
  // 如果路径为空，视为有效（可选字段）
  if (!path || path.trim() === '') {
    return {
      valid: true,
      message: '',
    };
  }

  // 校验路径是否以/开头
  if (path.startsWith('/')) {
    return {
      valid: false,
      message: CONST.MSG.MINIPROGRAM_PATH_START_SLASH,
    };
  }

  // 通过所有校验
  return {
    valid: true,
    message: '',
  };
};

/**
 * 校验网页链接
 * @param url 网页链接
 * @returns 校验结果对象，包含是否有效和错误信息
 */
export const validateWebUrl = (url: string): ValidationResult => {
  // 如果URL为空，视为有效（可选字段）
  if (!url || url.trim() === '') {
    return {
      valid: true,
      message: '',
    };
  }

  const urlRegex =
    /^https?:\/\/((?:www\.)?((?!-)[A-Za-z0-9\u4e00-\u9fa5\uac00-\ud7af\u1100-\u11ff\u3130-\u318f\u30a0-\u30ff\u31f0-\u31ff\u4e00-\u9faf-]{1,63}(?<!-)\.)+[A-Za-z\u4e00-\u9fa5\uac00-\ud7af\u1100-\u11ff\u3130-\u318f\u30a0-\u30ff\u31f0-\u31ff\u4e00-\u9faf]{2,})((\/[-a-zA-Z0-9@:%_+.~#?&!*();=$,\[\]\u4e00-\u9fa5\u3130-\u318f\u30a0-\u30ff\u31f0-\u31ff\uac00-\ud7af\u1100-\u11ff\u4e00-\u9faf]*)*\/?)$/;

  if (!urlRegex.test(url)) {
    return {
      valid: false,
      message: CONST.MSG.INVALID_WEB_URL,
    };
  }

  // 通过所有校验
  return {
    valid: true,
    message: '',
  };
};

/**
 * 移动服务到指定分类
 * @param serviceId 服务ID
 * @param targetCategoryName 目标分类名
 * @param serviceData 原始服务数据
 * @returns {updatedData: ServiceData, success: boolean, message: string} 更新后的数据、操作结果和消息
 */
export const moveServiceBetweenCategories = (
  serviceId: string | null,
  targetCategoryName: string,
  serviceData?: ServiceData
): MoveServiceResult => {
  // 返回值初始化
  const result: MoveServiceResult = {
    updatedData: DataManager.createOrUpdateData(serviceData),
    success: false,
    message: '',
  };

  if (!serviceId) {
    result.message = CONST.MSG.DRAG_FAILED_SERVICE_ID;
    return result;
  }

  const allServices = result.updatedData.serviceList || [];

  // 查找服务 - 使用字符串比较确保类型匹配
  const serviceIndex = allServices.findIndex(
    (s) => String(s.serviceId) === String(serviceId)
  );

  if (serviceIndex === -1) {
    result.message = CONST.MSG.DRAG_FAILED_SERVICE_NOT_FOUND;
    return result;
  }

  // 获取服务
  const service = allServices[serviceIndex];
  // 获取服务所在的原分类
  const originalCategoryName = service.serviceClassifyName || '无分类';

  // 如果是要移动到同一个分类，直接返回成功
  if (originalCategoryName === targetCategoryName) {
    result.success = true;
    return result;
  }

  // 检查目标分类的服务数量是否达到上限
  const targetCategoryServices = allServices.filter(
    (s) => s.serviceClassifyName === targetCategoryName
  );

  if (targetCategoryServices.length >= CONST.LIMIT.MAX_SERVICES_PER_CATEGORY) {
    result.message = CONST.MSG.DRAG_CATEGORY_MAX;
    return result;
  }

  // 检查是否是原分类中的最后一个服务
  const originalCategoryServices = allServices.filter(
    (s) => s.serviceClassifyName === originalCategoryName && s.serviceId !== serviceId
  );
  const isLastServiceInCategory =
    originalCategoryServices.length === 0 && originalCategoryName !== '无分类';

  // 创建服务的副本，避免直接操作原对象
  const serviceCopy = JSON.parse(JSON.stringify(service));

  // 修改副本的分类属性
  serviceCopy.serviceClassifyName = targetCategoryName;

  if (isLastServiceInCategory) {
    // 如果是原分类中的最后一个服务，创建一个空的分类标记服务
    const emptyCategoryService = createEmptyCategoryService(originalCategoryName);

    // 在原位置替换为空分类标记服务，保持原分类的位置
    allServices[serviceIndex] = emptyCategoryService;

    // 将修改后的服务添加到列表中（添加到末尾，后续会根据分类顺序重新排序）
    allServices.push(serviceCopy);
  } else {
    // 不是原分类中的最后一个服务，直接替换原服务
    allServices[serviceIndex] = serviceCopy;
  }

  // 设置成功状态
  result.success = true;

  return result;
};

/**
 * 根据分类顺序重新排序服务列表
 * @param serviceData 原始服务数据
 * @param categoryOrderMap 分类顺序映射
 * @returns 排序后的服务数据
 */
export const sortServiceListByCategoryOrder = (
  serviceData: ServiceData,
  categoryOrderMap: Map<string, number>
): ServiceData => {
  if (!serviceData?.serviceList || serviceData.serviceList.length === 0) {
    return serviceData;
  }

  // 创建新的数据对象
  const updatedData = DataManager.createOrUpdateData(serviceData);

  // 按分类名称分组服务
  const servicesByCategory = new Map<string, ServiceItem[]>();

  // 先收集所有分类的服务
  updatedData.serviceList!.forEach((service) => {
    const categoryName = service.serviceClassifyName || '无分类';
    if (!servicesByCategory.has(categoryName)) {
      servicesByCategory.set(categoryName, []);
    }
    servicesByCategory.get(categoryName)!.push(service);
  });

  // 获取所有分类名称并按顺序排序
  const categories = Array.from(servicesByCategory.keys());

  // 按照categoryOrderMap排序分类（除了"无分类"）
  const sortedCategories = categories
    .filter((name) => name !== '无分类')
    .sort((a, b) => {
      const orderA = categoryOrderMap.get(a) ?? Number.MAX_SAFE_INTEGER;
      const orderB = categoryOrderMap.get(b) ?? Number.MAX_SAFE_INTEGER;
      return orderA - orderB;
    });

  // 如果存在"无分类"，将其添加到末尾
  if (categories.includes('无分类')) {
    sortedCategories.push('无分类');
  }

  // 按排序后的分类顺序重新组织服务列表
  const sortedServiceList: ServiceItem[] = [];
  sortedCategories.forEach((categoryName) => {
    const services = servicesByCategory.get(categoryName) || [];
    sortedServiceList.push(...services);
  });

  // 更新服务列表
  updatedData.serviceList = sortedServiceList;

  return updatedData;
};

/**
 * 拖拽状态管理器
 * 用于统一管理分类拖拽和服务拖拽的状态
 */
export class DragStateManager {
  /**
   * 重置所有拖拽状态
   * 包括DOM样式和状态变量
   */
  static resetAllDragStates(): void {
    // 清理所有DOM样式
    clearDragStyles();

    // 重置分类和服务的spacer激活状态
    this.setActiveCategorySpacerIndex(null);
  }

  /**
   * 设置活跃的分类spacer索引
   * @param index spacer索引或null表示无活跃spacer
   */
  static setActiveCategorySpacerIndex(index: number | null): void {
    // 先清除所有spacer的激活状态
    document.querySelectorAll('.category-spacer').forEach((spacer) => {
      spacer.classList.remove(CONST.CLASS.SPACER_ACTIVE);
    });

    // 如果有活跃索引，激活对应的spacer
    if (index !== null) {
      const spacer = document.querySelector(
        `.category-spacer[data-spacer-index="${index}"]`
      );
      if (spacer) {
        spacer.classList.add(CONST.CLASS.SPACER_ACTIVE);
      }
    }
  }

  /**
   * 设置分类spacer的可见性
   * @param excludedCategoryIndex 要排除的分类索引
   */
  static setupCategorySpacersVisibility(excludedCategoryIndex: number): void {
    // 先隐藏所有spacer
    document.querySelectorAll('.category-spacer').forEach((spacer) => {
      spacer.classList.remove(CONST.CLASS.SPACER_VISIBLE, CONST.CLASS.SPACER_ACTIVE);
    });

    // 显示除当前拖拽分类相邻的spacer以外的所有spacer
    const currentPrevSpacerIndex = excludedCategoryIndex; // 当前分类前面的spacer
    const currentNextSpacerIndex = excludedCategoryIndex + 1; // 当前分类后面的spacer

    document.querySelectorAll('.category-spacer').forEach((spacer) => {
      const spacerEl = spacer as HTMLElement;
      const spacerIndex = parseInt(spacerEl.dataset.spacerIndex || '0', 10);

      // 跳过当前拖拽分类前后的spacer
      if (
        spacerIndex === currentPrevSpacerIndex ||
        spacerIndex === currentNextSpacerIndex
      ) {
        return;
      }

      // 显示其他所有spacer
      spacerEl.classList.add(CONST.CLASS.SPACER_VISIBLE);
    });
  }

  /**
   * 设置拖拽元素样式
   * @param element 要设置样式的元素
   * @param isDragging 是否正在拖拽
   */
  static setDraggingStyle(element: HTMLElement, isDragging: boolean): void {
    if (isDragging) {
      element.classList.add(CONST.CLASS.CATEGORY_DRAGGING);
    } else {
      element.classList.remove(CONST.CLASS.CATEGORY_DRAGGING);
    }
  }

  /**
   * 设置源拖拽元素样式
   * @param element 要设置样式的元素
   * @param isDraggingSource 是否为拖拽源
   */
  static setDraggingSourceStyle(element: HTMLElement, isDraggingSource: boolean): void {
    if (isDraggingSource) {
      element.classList.add(CONST.CLASS.DRAGGING_SOURCE);
    } else {
      element.classList.remove(CONST.CLASS.DRAGGING_SOURCE);
    }
  }

  /**
   * 设置目标放置元素样式
   * @param element 要设置样式的元素
   * @param isDropTarget 是否为放置目标
   */
  static setDropTargetStyle(element: HTMLElement, isDropTarget: boolean): void {
    if (isDropTarget) {
      element.classList.add(CONST.CLASS.DROP_TARGET);
    } else {
      element.classList.remove(CONST.CLASS.DROP_TARGET);
    }
  }
}

export const formatTimestamp = (
  timestamp: string | null,
  isAuditTime = false
): string => {
  if (!timestamp) return '';

  try {
    // 处理字符串格式的时间戳，如果长度为10（秒级时间戳），则乘以1000转为毫秒
    const timestampNum = parseInt(timestamp);
    // 判断是否为秒级时间戳（10位数），如果是则转换为毫秒级
    const milliseconds =
      timestampNum.toString().length === 10 ? timestampNum * 1000 : timestampNum;

    const date = new Date(milliseconds);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');

    // 审核时间精确到秒，发布时间精确到分钟
    if (isAuditTime) {
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
    return `${year}-${month}-${day} ${hour}:${minute}`;
  } catch (error) {
    console.error('时间戳格式转换错误:', error);
    return '';
  }
};
