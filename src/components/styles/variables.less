// 文字颜色
@card-link-text-secondary: rgba(0, 0, 0, 0.6);
@card-link-text-disabled: rgba(0, 0, 0, 0.26);

// 背景颜色
@card-link-bg-color-container: #ffffff;
@card-link-bg-color-component: #fafafa;

// 边框颜色
@card-link-border-level-2-color: rgba(0, 0, 0, 0.06);

// ===== 尺寸变量 =====
// 间距
@card-link-spacer-xs: 4px;
@card-link-spacer: 16px;

// 字体大小
@card-link-font-size-base: 14px;
@card-link-font-size-l: 18px;

// 行高
@card-link-line-height-base: 22px;

// ===== 组件特定变量 =====
// 操作栏
@card-link-action-bar-border-color: @card-link-border-level-2-color;

// ===== 阴影变量 =====
@card-link-shadow-1:
  0 1px 10px rgba(0, 0, 0, 0.05),
  0 4px 5px rgba(0, 0, 0, 0.08),
  0 2px 4px -1px rgba(0, 0, 0, 0.12);

// ===== 过渡动画 =====
@card-link-transition-duration-base: 0.2s;
@card-link-transition-timing-function: cubic-bezier(0.38, 0, 0.24, 1);

// ===== 响应式断点 =====
@card-link-screen-sm: 768px;
@card-link-screen-md: 1024px;

// ===== Z-index 层级 =====
@card-link-zindex-affix: 10;

// ===== 混合宏 =====
// Flex 垂直居中
.card-link-flex-vertical-center() {
  display: flex;
  align-items: center;
}

// Flex 两端对齐
.card-link-flex-between() {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
