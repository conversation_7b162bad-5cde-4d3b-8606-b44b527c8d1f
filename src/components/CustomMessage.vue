<template>
  <div></div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

interface Props {
  theme?: 'success' | 'warning' | 'error' | 'info';
  content?: string;
  duration?: number;
  closeBtn?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  theme: 'info',
  content: '',
  duration: 3000,
  closeBtn: false
});

onMounted(() => {
  const messageConfig = {
    content: props.content,
    duration: props.duration,
    closeBtn: props.closeBtn
  };

  switch (props.theme) {
    case 'success':
      MessagePlugin.success(messageConfig);
      break;
    case 'warning':
      MessagePlugin.warning(messageConfig);
      break;
    case 'error':
      MessagePlugin.error(messageConfig);
      break;
    case 'info':
    default:
      MessagePlugin.info(messageConfig);
      break;
  }
});
</script> 