import { createPinia } from 'pinia';
import { componentRegistry } from '@tencent/dsl-page'
import 'tdesign-vue-next/es/style/index.css';
import '@/assets/styles/index.css';
import * as TDesign from 'tdesign-vue-next';
import * as TDesignIcons from 'tdesign-icons-vue-next';
import { createApp } from 'vue';
import App from './app.vue';
import router from './router';
import '@tencent/xpage-mch-components/style';
import CustomMessage from './components/CustomMessage.vue';

// 注册基础组件
const tDesignMap = [
  'input',
  'select',
  'upload',
  'radio',
  'link',
  'checkbox',
  'button',
  'space',
  'dialog',
  'upload',
  'drawer',
  'tabs',
  'image',
  'alert',
  'textarea',
  'table',
  'tag',
  'popconfirm',
]
  .map((tag) => ({ [`v-${tag}`]: TDesign[tag[0].toUpperCase() + tag.slice(1)] }))
  .concat([
    {
      'v-checkbox-group': TDesign.CheckboxGroup, // Explicitly handle CheckboxGroup with correct capitalization
    },
  ]);
const mapComponentToVprefix = tDesignMap.reduce((o, c) => {
  const [tag, com] = Object.entries(c)[0];
  o[tag] = com;
  return o;
}, {});
const tDesignIconsMap = ['plus', 'search', 'refresh', 'arrow-left'].map((tag) => ({
  [`v-icon-${tag}`]:
    TDesignIcons[
      `${tag[0].toUpperCase() + tag.slice(1).replace(/-(\w)/g, (_a, w) => w.toUpperCase())}Icon` as keyof typeof TDesignIcons
    ],
}));
const mapIconToVprefix = tDesignIconsMap.reduce((o, c) => {
  const [tag, com] = Object.entries(c)[0];
  o[tag] = com;
  return o;
}, {});

componentRegistry.registerBatch({
  'v-page-loading': TDesign.Loading,
  'v-error': TDesign.Message,
  'v-img': TDesign.Image,
  'v-radio-group': TDesign.RadioGroup,
  'v-date-picker': TDesign.DatePicker,
  'v-checkbox-group': TDesign.CheckboxGroup,
  'v-imageview': TDesign.ImageViewer,
  'v-message': CustomMessage,
  ...mapComponentToVprefix,
  ...mapIconToVprefix,
});



const app = createApp(App);

app.use(TDesign);
/* __COMPONENT_PLACEHOLDER */
app.use(router);
const pinia = createPinia();
app.use(pinia)
app.provide('pinia', pinia);

app.mount('#app-root');
