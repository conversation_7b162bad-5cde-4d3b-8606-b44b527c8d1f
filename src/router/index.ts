import { createRouter, createWeb<PERSON><PERSON><PERSON>, use<PERSON>out<PERSON>, useRouter } from 'vue-router';
// 移除未使用的 Home 导入
// import UserInsight from '@/pages/user-insight.vue';
// import UserPersona from '@/pages/user-persona.vue';
// import ShakeActivityOverview from '@/pages/shake-activity-overview.vue';
// import ShakeActivityDetail from '@/pages/shake-activity-detail.vue';
// import ShakeActivityPersona from '@/pages/shake-activity-persona.vue';
// import BusinessOverview from '@/pages/business-overview.vue';
import { Component, h, watch } from 'vue';
import { DynamicPage, deepMerge } from '@tencent/dsl-page';
import HomeModel from '@/models/page-models/home-model';
import { request } from '@/utils/request';
import { ProcessState } from '@/pages/card-mna/common/model/tutorial';
import { useBreadcrumb } from '@/router/useBreadcrumb';

const homeModel = HomeModel.getInstance();

// 定义类型接口
interface SchemaData {
  [key: string]: unknown;
}

interface DynamicPageSchema {
  main?: unknown;
  layout?: unknown;
  meta?: unknown;
  cssClasses?: unknown;
  interactions?: unknown;
  [key: string]: unknown;
}

interface PageSchema {
  schema: SchemaData | null;
  index: unknown | null;
}

interface JsonModule {
  path: string;
  module: SchemaData;
}

interface ComponentRegistry {
  registerBatch: (components: Record<string, unknown>) => void;
}

interface ModuleExports {
  registerComponent?: (registry: ComponentRegistry) => void;
  registerRouter?: (router: ReturnType<typeof createRouter>) => void;
}

interface CommonSchemaModule {
  default: {
    schema: SchemaData;
  };
}

// 获取所有页面JSON文件和index.ts文件
const jsonModules = import.meta.glob('@/pages/**/*.json', { eager: true });
const pageModules = import.meta.glob('@/pages/**/index.ts', { eager: true });
let ctxRegisterRouter: ((router: ReturnType<typeof createRouter>) => void) | null = null;

// 构建页面映射
const pageMap = new Map<string, PageSchema>();

// 按目录分组JSON文件
const dirJsonMap = new Map<string, JsonModule[]>();
for (const [path, module] of Object.entries(jsonModules)) {
  const match = path.match(/\/pages\/(.+?)\/([^/]+)\.json$/);
  if (match) {
    const [, dirPath] = match;
    if (!dirJsonMap.has(dirPath)) {
      dirJsonMap.set(dirPath, []);
    }
    dirJsonMap.get(dirPath)!.push({
      path,
      module: (module as { default: SchemaData }).default,
    });
  }
}

// 处理每个目录的JSON文件合并
for (const [dirPath, jsonFiles] of dirJsonMap.entries()) {
  // 查找schema.json作为基础
  const schemaFile = jsonFiles.find((f) => f.path.endsWith('schema.json'));
  if (!schemaFile) continue;

  // 合并同目录下其他JSON文件
  let mergedSchema = { ...schemaFile.module };
  for (const file of jsonFiles) {
    if (file.path !== schemaFile.path) {
      mergedSchema = deepMerge(mergedSchema, file.module);
    }
  }

  // 保存合并后的schema
  const pageId = dirPath.replace(/\/schema$/, '');
  if (!pageMap.has(pageId)) {
    pageMap.set(pageId, { schema: null, index: null });
  }
  const pageData = pageMap.get(pageId);
  if (pageData) {
    pageData.schema = (mergedSchema as { schema: SchemaData }).schema;
  }
}

// 处理index.ts文件
for (const [path, module] of Object.entries(pageModules)) {
  const match = path.match(/\/pages\/(.+)\/index\.ts$/);
  if (match) {
    const [, pageId] = match;
    if (!pageMap.has(pageId)) {
      pageMap.set(pageId, { schema: null, index: null });
    }

    const { registerComponent, registerRouter } = module as ModuleExports;

    if (typeof registerComponent === 'function') {
      // 注意：这里需要确保 componentRegistry 在适当的地方被导入
      // registerComponent(componentRegistry.registerBatch.bind(componentRegistry));
    }
    if (typeof registerRouter === 'function') {
      ctxRegisterRouter = registerRouter;
    }
  }
}

const pageCommonModules = import.meta.glob('@/pages/**/common/schema.json', {
  eager: true,
});

const createPageLayout = (contentRenderer: () => ReturnType<typeof h>) => ({
  setup() {
    const { dynamicBreadcrumb } = useBreadcrumb();

    return () =>
      h('div', [
        h('div', { class: 'flex-1 w-[1224px] flex flex-col gap-[8px]' }, [
          dynamicBreadcrumb.value,
          h(
            'div',
            {
              class: 'flex flex-col gap-[8px]',
              style: {
                width: '100%',
                minHeight: 'calc(100vh - 520px)',
              },
            },
            contentRenderer()
          ),
        ]),
      ]);
  },
});

const renderDslPage = () => ({
  setup() {
    const route = useRoute();
    const router = useRouter();
    if (typeof ctxRegisterRouter === 'function') {
      ctxRegisterRouter(router);
    }
    watch(
      () => router.currentRoute.value,
      () => {
        console.log(homeModel);
        const { sideBarNavItemsRef } = homeModel;
        // 可以在这里使用 sideBarNavItemsRef
        console.log('Navigation items updated', sideBarNavItemsRef.value);
      },
      { immediate: true }
    );

    const layout = createPageLayout(() => {
      const dir = route.path.replace(/\/home\//, '');
      const [dirName, pageName] = dir.split('/');

      // 安全地获取 schema
      const commonSchema = pageCommonModules[
        `/src/pages/${dirName}/common/schema.json`
      ] as CommonSchemaModule;
      const pageData = pageMap.get(`${dirName}/${pageName}`);

      if (!commonSchema || !pageData?.schema) {
        console.warn(`Schema not found for page: ${dirName}/${pageName}`);
        return h('div', { class: 'error' }, 'Page schema not found');
      }

      const pageSchema = deepMerge(
        { ...commonSchema.default.schema },
        { ...pageData.schema }
      );

      return h(DynamicPage, {
        key: Math.random(),
        schema: pageSchema as DynamicPageSchema,
      });
    });

    return () => h(layout);
  },
});

const renderNativePage = (Component: Component) => createPageLayout(() => h(Component));

// 路由检查函数 - 判断用户应该在哪个页面
const checkRouter = async (): Promise<{
  code: number;
  message: string;
  path: string;
}> => {
  console.log('----home init----');
  const result = await request.get('/get-latest-tutorial-publish-process');

  if (result?.code === 0) {
    // 有新手引导发布单
    if (result.data?.process.state === ProcessState.PUBLISHED) {
      return {
        code: 3000,
        message: '已完成新手引导',
        path: '/home/<USER>/dashboard',
      };
    }

    // 有新手引导发布单，非终态,继续新手
    return {
      code: 2000,
      message: '已有新手单',
      path: '/home/<USER>/card-detail',
    };
  }

  const [cardInfo, cardService] = await Promise.all([
    request.get('/get-card-info'),
    request.get('/get-service-config'),
  ]);

  // 无新手引导发布单
  if (cardInfo.code === 0 && cardService.code === 0) {
    // 基础信息和服务都有数据
    return {
      code: 3000,
      message: '已完成新手引导',
      path: '/home/<USER>/dashboard',
    };
  }

  // 其中一个没有都当新手
  return {
    code: 1000,
    message: '新手',
    path: '/home/<USER>/card-home',
  };
};

const router = createRouter({
  history: createWebHistory('/xdc/mchcardinfomgrweb/'),
  scrollBehavior() {
    // always scroll to top
    return { top: 0 };
  },
  routes: [
    {
      path: '/',
      redirect: '/home',
    },
    {
      path: '/home/',
      redirect: '/home/<USER>/card-home',
      component: () => Promise.resolve(renderDslPage()),
      children: [
        {
          path: 'card-mna/card-home',
          component: () => Promise.resolve(renderDslPage()),
          name: 'CardHome',
        },
        {
          path: 'card-mna/service-config/:type',
          component: () => Promise.resolve(renderDslPage()),
          name: 'ServiceConfig',
          meta: {
            hideInMenu: true,
          },
        },
        {
          path: 'card-mna/service-state/:type/:processId',
          component: () => Promise.resolve(renderDslPage()),
          name: 'ServiceState',
        },
        {
          path: 'card-mna/card-detail',
          component: () => Promise.resolve(renderDslPage()),
          name: 'CardDetail',
          meta: {
            hideInMenu: true,
          },
        },
        {
          path: 'card-mna/card-config/:configId?',
          component: () => Promise.resolve(renderDslPage()),
          name: 'CardConfig',
        },
        {
          path: 'card-mna/card-view/:configId?',
          component: () => Promise.resolve(renderDslPage()),
          name: 'CardView',
        },
        {
          path: 'card-mna/brand-vip',
          component: () => Promise.resolve(renderDslPage()),
          name: 'BrandAndVip',
        },
        {
          path: 'card-mna/dashboard',
          component: () => Promise.resolve(renderDslPage()),
          name: 'Dashboard',
        },
        {
          path: 'card-mna/card-publish/:processId',
          component: () => Promise.resolve(renderDslPage()),
          name: 'CardPublish',
        },
        {
          path: 'card-mna/state-preview/:id',
          component: () => import('@/components/state-preview/state-preview.vue'),
          name: 'StatePreview',
        },
        {
          path: 'card-mna/common-publish/:processId',
          component: () => Promise.resolve(renderDslPage()),
          name: 'cardConfigPreview',
        },
      ],
    },
    {
      // 交易连接名片首页
      path: '/card-link/home',
      component: () =>
        import('@/pages/card-link/home/<USER>').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkHome',
    },
    {
      // 交易连接名片添加引导页
      path: '/card-link/scene-add-guide',
      component: () =>
        import('@/pages/card-link/scene-add-guide/scene-add-guide.vue').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkSceneAddGuide',
    },
    {
      // 交易连接 - 支付分管理页
      path: '/card-link/pay-score-mna',
      component: () =>
        import('@/pages/card-link/pay-score-mna/pay-score-mna.vue').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkPayScoreManager',
    },
    {
      // 交易连接 - 支付分添加
      path: '/card-link/pay-score-add',
      component: () =>
        import('@/pages/card-link/pay-score-add/pay-score-add.vue').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkPayScoreAdd',
    },
    {
      path: '/card-link/payment-code',
      component: () =>
        import('@/pages/card-link/payment-code.vue').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkPaymentCode',
    },
    {
      path: '/card-link/payment-code/add',
      component: () =>
        import('@/pages/card-link/payment-code-add.vue').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkPaymentCodeAdd',
    },
    {
      path: '/card-link/payment-code/add-result',
      component: () =>
        import('@/pages/card-link/payment-code-add-result.vue').then((m) =>
          renderNativePage(m.default)
        ),
      name: 'CardLinkPaymentCodeAddResult',
    },
    {
      path: '/card-link/test',
      component: () =>
        import('@/pages/card-link/mp-pay.vue').then((m) => renderNativePage(m.default)),
      name: 'CardLinkTest',
    },
    // Card-Home Vue组件路由 - 独立路由，不使用DSL布局
    {
      path: '/home/<USER>/card-home-vue',
      component: () => import('@/pages/card-mna/card-home-vue/CardHome.vue'),
      name: 'CardHomeVue',
      meta: {
        layout: false, // 明确指定不使用布局
      },
    },
    // Card-Detail Vue组件路由 - 独立路由，不使用DSL布局
    {
      path: '/home/<USER>/card-detail-vue',
      component: () => import('@/pages/card-mna/card-detail-vue/CardDetail.vue'),
      name: 'CardDetailVue',
      meta: {
        layout: false, // 明确指定不使用布局
      },
    },
    {
      path: '/home/<USER>/card-detail-vue/test',
      component: () => import('@/pages/card-mna/card-detail-vue/test-component.vue'),
      name: 'CardDetailTest',
      meta: {
        layout: false, // 明确指定不使用布局
      },
    },
  ],
});

// 添加全局前置守卫来处理首次进入的重定向逻辑
router.beforeEach(async (to, from, next) => {
  // 定义需要进行路由检查的页面
  const routesToCheck = [
    // '/home/<USER>/card-home',
    // '/home/<USER>/card-home-vue',
    '/home/<USER>/card-detail',
    '/home/<USER>/dashboard',
  ];
  if (routesToCheck.includes(to.path)) {
    try {
      const routeCheck = await checkRouter();
      console.log('Route check result:', routeCheck);

      // 根据检查结果进行重定向
      // 避免无限重定向：只有当目标路径与当前路径不同，且不是来自重定向时才进行重定向
      if (routeCheck.path !== to.path && from.path !== routeCheck.path) {
        console.log(`Redirecting from ${to.path} to ${routeCheck.path}`);
        next(routeCheck.path);
        return;
      }
    } catch (error) {
      console.error('Route check failed:', error);
      // 如果检查失败，且不是已经在新手页面，则跳转到新手页面
      if (to.path !== '/home/<USER>/card-home') {
        next('/home/<USER>/card-home');
        return;
      }
    }
  }
  next();
});

export default router;
