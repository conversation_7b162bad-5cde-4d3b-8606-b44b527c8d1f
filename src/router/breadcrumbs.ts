import { RouteLocationNormalizedLoaded } from 'vue-router';

export interface BreadcrumbItem {
  name: string;
  path?: string;
}

export type BreadcrumbConfig = BreadcrumbItem[] | ((route: RouteLocationNormalizedLoaded) => BreadcrumbItem[]);

export const breadcrumbs: Record<string, BreadcrumbConfig> = {
  CardHome: [{ name: '名片管理' }],
  CardHomeVue: [{ name: '名片管理' }],

  Dashboard: [{ name: '名片管理' }],

  CardDetail: [{ name: '名片管理' }],

  CardConfig: [
    { name: '名片管理', path: '/home/<USER>/card-detail' },
    { name: '配置基础信息', path: '' },
  ],

  CardView: [
    { name: '名片管理', path: '/home/<USER>/dashboard' },
    { name: '基础信息', path: '' },
  ],

  CardPublish: [
    { name: '名片管理', path: '/home/<USER>/dashboard' },
    { name: '发布名片', path: '' },
  ],

  cardConfigPreview: [
    { name: '名片管理', path: '/home/<USER>/dashboard' },
    { name: '发布名片', path: '' },
  ],

  // 服务配置 - 根据type参数动态生成
  ServiceConfig: (route: RouteLocationNormalizedLoaded) => {
    const type = route.params.type as string;
    if (type === 'config') {
      return [
        { name: '名片管理', path: '/home/<USER>/dashboard' },
        { name: '配置名片服务', path: '' },
      ];
    }
    if (type === 'preview') {
      return [
        { name: '名片管理', path: '/home/<USER>/dashboard' },
        { name: '名片服务', path: '' },
      ];
    }
    return [
      { name: '名片管理', path: '/home/<USER>/dashboard' },
      { name: '名片服务', path: '' },
    ];
  },

  // 品牌与VIP
  BrandAndVip: [
    { name: '名片管理', path: '/home/<USER>/dashboard' },
    { name: '品牌与会员', path: '' },
  ],

  // 服务状态 - 根据type参数动态生成
  ServiceState: (route: RouteLocationNormalizedLoaded) => {
    const type = route.params.type as string;
    if (type === 'config') {
      return [
        { name: '名片管理', path: '/home/<USER>/dashboard' },
        { name: '配置基础信息', path: '' },
      ];
    }
    if (type === 'service') {
      return [
        { name: '名片管理', path: '/home/<USER>/dashboard' },
        { name: '配置名片服务', path: '' },
      ];
    }
    if (type === 'publish') {
      return [
        { name: '名片管理', path: '/home/<USER>/dashboard' },
        { name: '发布名片', path: '' },
      ];
    }
    return [
      { name: '名片管理', path: '/home/<USER>/dashboard' },
      { name: '状态详情', path: '' },
    ];
  },
  CardLinkHome: [{ name: '交易连接名片', path: '/card-link/home' }],
  CardLinkPayScoreManager: [
    { name: '交易连接名片', path: '/card-link/home' },
    { name: '支付分场景', path: '/card-link/pay-score-mna' },
  ],
  CardLinkSceneAddGuide: [
    { name: '交易连接名片', path: '/card-link/home' },
    { name: '添加交易场景', path: '/card-link/scene-add-guide' },
  ],
  CardLinkPayScoreAdd: [
    { name: '交易连接名片', path: '/card-link/home' },
    { name: '添加交易场景', path: '/card-link/pay-score-add' },
  ],
  CardLinkPaymentCode: [
    { name: '交易连接名片', path: '/card-link/home' },
    { name: '付款码支付', path: '' },
  ],

  CardLinkPaymentCodeAdd: [
    { name: '交易连接名片', path: '/card-link/home' },
    { name: '添加交易场景', path: '' },
  ],

  CardLinkPaymentCodeAddResult: [
    { name: '交易连接名片', path: '/card-link/home' },
    { name: '添加交易场景', path: '' },
  ],
};

/**
 * 获取面包屑配置
 * @param name 路由名称
 * @param route 路由对象
 * @returns 面包屑配置
 */
export function getBreadcrumb(name: string, route: RouteLocationNormalizedLoaded): BreadcrumbItem[] {
  const config = breadcrumbs[name];
  if (!config) return [];

  if (typeof config === 'function') {
    return config(route);
  }

  return config;
}
