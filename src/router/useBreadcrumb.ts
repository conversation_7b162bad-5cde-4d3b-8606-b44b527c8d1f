import { h, ref, VNode, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import HomeModel from '@/models/page-models/home-model';
import { getBreadcrumb } from '@/router/breadcrumbs';

const homeModel = HomeModel.getInstance();

// 定义导航项类型
interface NavItem {
  href?: string;
  name: string;
  children?: NavItem[];
}

export function useBreadcrumb() {
  const route = useRoute();
  const router = useRouter();
  const dynamicBreadcrumb = ref<VNode>();

  // Recursive function to find breadcrumb path
  const findBreadcrumbPath = (
    items: NavItem[],
    path: string,
    parents: NavItem[] = []
  ): NavItem[] => {
    for (const item of items) {
      if (item.href?.replace('#', '') === path) {
        return [...parents, item];
      }

      if (item.href && path.indexOf(item.href.replace('#', '')) > -1) {
        return [...parents, item];
      }

      if (item.children) {
        const found = findBreadcrumbPath(item.children, path, [...parents, item]);
        if (found.length) return found;
      }
    }
    return [];
  };

  const findNameByPath = (path: string) => {
    const { sideBarNavItemsRef } = homeModel;
    const breadcrumbPath = findBreadcrumbPath(sideBarNavItemsRef.value, path);
    return breadcrumbPath.length > 0
      ? breadcrumbPath[breadcrumbPath.length - 1].name
      : '';
  };

  const updateBreadcrumb = () => {
    const { sideBarNavItemsRef } = homeModel;
    const currentPath = route.path;

    // 使用统一面包屑管理，根据路由名称获取面包屑配置
    if (route.name) {
      const breadcrumbItems = getBreadcrumb(route.name as string, route);

      if (breadcrumbItems.length > 0) {
        // 处理面包屑配置
        dynamicBreadcrumb.value = h(
          'div',
          {
            class: '!mb-4 breadcrumb',
            style: 'padding: 16px 0; font-size: 18px;',
          },
          breadcrumbItems
            .map((item, index) => {
              const isLast = index === breadcrumbItems.length - 1;
              return [
                h(
                  'span',
                  {
                    class: `breadcrumb-item ${isLast ? 'active' : ''}`,
                    style: !isLast
                      ? 'cursor: pointer; color: #999; font-size: 24px;'
                      : 'color: #333; font-weight: bold; font-size: 24px;',
                    onClick:
                      !isLast && item.path
                        ? () => router.push(item.path || '')
                        : undefined,
                  },
                  item.name
                ),
                !isLast &&
                  h(
                    'span',
                    {
                      class: 'breadcrumb-separator',
                      style: 'color: #999; margin: 0 8px; font-size: 24px;',
                    },
                    '/'
                  ),
              ];
            })
            .flat()
            .filter(Boolean)
        );
        return;
      }
    }

    // 如果没有配置breadcrumb，使用原来的逻辑查找面包屑路径
    const breadcrumbPath = findBreadcrumbPath(sideBarNavItemsRef.value, currentPath);

    if (breadcrumbPath.length > 1) {
      // Skip the first item (level 1)
      const filteredPath = breadcrumbPath.slice(1);
      dynamicBreadcrumb.value = h(
        'div',
        { class: '!mb-4 breadcrumb' },
        filteredPath
          .map((item, index) => {
            const isLast = index === filteredPath.length - 1;
            return [
              h(
                'span',
                {
                  class: `breadcrumb-item ${isLast ? 'active' : ''}`,
                },
                item.name
              ),
              !isLast && h('span', { class: 'breadcrumb-separator' }, '/'),
            ];
          })
          .flat()
          .filter(Boolean)
      );
    } else if (breadcrumbPath.length === 1) {
      // Only show active item if there's only one level
      dynamicBreadcrumb.value = h('div', { class: '!mb-4 breadcrumb' }, [
        h('span', { class: 'breadcrumb-item' }, breadcrumbPath[0].name),
        h('span', { class: 'breadcrumb-separator' }, '/'),
        h('span', { class: 'breadcrumb-item active' }, findNameByPath(currentPath)),
      ]);
    }
  };

  // Watch route changes
  watch(() => route.path, updateBreadcrumb, { immediate: true });

  return {
    dynamicBreadcrumb,
    updateBreadcrumb,
    findBreadcrumbPath,
    findNameByPath,
  };
}
