/**
 * Vitest 测试环境设置
 * 配置全局测试环境和Mock
 */

import { vi } from 'vitest';
import { config } from '@vue/test-utils';

// 全局Mock配置
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Vue Test Utils 全局配置
config.global.stubs = {
  // TDesign 组件 Mock
  TCard: true,
  TButton: true,
  TLoading: true,
  TAlert: true,
  TSpace: true,
  TRow: true,
  TCol: true,
  TDivider: true,
  TIcon: true,
  TTooltip: true,
  TPopup: true,
  TDialog: true,
  TMessage: true,
  TNotification: true,

  // 路由组件 Mock
  RouterLink: true,
  RouterView: true,

  // 过渡组件 Mock
  Transition: true,
  TransitionGroup: true,
};

// 全局属性Mock
config.global.mocks = {
  $t: (key: string) => key, // i18n Mock
  $route: {
    path: '/',
    query: {},
    params: {},
    name: 'home',
  },
  $router: {
    push: vi.fn(),
    replace: vi.fn(),
    go: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
  },
};

// 控制台警告过滤
const originalWarn = console.warn;
console.warn = (...args) => {
  // 过滤Vue测试中的常见警告
  if (
    args[0]?.includes?.('Vue received a Component which was made a reactive object') ||
    args[0]?.includes?.('[Vue warn]') ||
    args[0]?.includes?.('Failed to resolve component')
  ) {
    return;
  }
  originalWarn(...args);
};

// 控制台错误过滤
const originalError = console.error;
console.error = (...args) => {
  // 过滤测试中的预期错误
  if (args[0]?.includes?.('Vue warn') || args[0]?.includes?.('Failed to resolve component')) {
    return;
  }
  originalError(...args);
};
