/**
 * @file 日期相关工具方法
 */

import { dayjs } from '@tencent/xpage-web-sdk';
import { ActiveCompassRulePeriod } from '../types/models';

/**
 * 最长可选31天
 */
export const MAX_DATE_RANGE_DAYS = 31;

/**
 * 默认用户洞察数据时间范围
 */
export const DEFAULT_USER_INSIGHT_TRENT_DATE_RANGE = 31;

/**
 * 日期格式
 */
export enum DateFormat {
  /** 用于页面显示 */
  page = 'YYYY/MM/DD',
  /** 用于提交后端 */
  api = 'YYYY-MM-DD',
  /** ao原始返回的数据格式 */
  ao = 'YYYYMMDD',
};

/**
 * 对日期进行格式化
 * 支持的格式：
 * 1. 2021-01-01
 * 2. 2021/01/01
 * 3. 20210101
 * 其他合法日期格式
 *
 * @param date 日期
 * @param format 格式
 */
export const dateFormat = (date: string | number, format: DateFormat = DateFormat.api): string => {
  if (typeof date === 'string') {
    // 2021-01-01
    if (/^\d{4}-\d{2}-\d{2}$/.test(date) ) {
      return dayjs(date).format(format);
    }
    // 2021/01/01
    if (/^\d{4}\/\d{2}\/\d{2}$/.test(date) ) {
      return dayjs(date).format(format);
    }
    // 20210101
    if (/^\d{8}$/.test(date)) {
      return dayjs(date, 'YYYYMMDD').format(format);
    }
  }
  if (typeof date === 'number') {
    // 20210101
    if (/^\d{8}$/.test(date.toString())) {
      return dayjs(date, 'YYYYMMDD').format(format);
    }
  }

  if (dayjs(date).isValid()) {
    return dayjs(date).format(format);
  }
  throw new Error(`日期格式错误：${date}`);
};

/**
 * 报表出库时间
 */
export const ReportTime = {
  hour: 12,
  minute: 0,
};

/**
 * 获取默认的最早出报表的日期
 * 一个比较久远的日期
 * 默认格式YYYY-MM-DD
 */
export const getDefaultEarliestDate = (format = 'YYYY-MM-DD') => dayjs('2024-09-01', 'YYYY-MM-DD').format(format);

/**
 * 默认一个比较晚的日期
 */
export const getDefaultLatestDate = (format = 'YYYY-MM-DD') => dayjs('2099-12-31', 'YYYY-MM-DD').format(format);
/**
 * 获取默认的最后出报表的日期
 * 默认格式YYYY-MM-DD
 */
export const getDefaultEndDate = (format = 'YYYY-MM-DD'): string => {
  const currentTime = dayjs();
  const reportTime = dayjs().set('hour', ReportTime.hour).set('minute', ReportTime.minute);

  // 没到昨天的报表时间，取前天的报表
  if (currentTime.isBefore(reportTime)) {
    return reportTime.subtract(2, 'day').format(format);
  }

  // 到了昨天的报表时间，取昨天的报表
  return reportTime.subtract(1, 'day').format(format);
};

/**
 * 获取默认的报表结束日期的下一天
 */
export const getDefaultEndDateNext = (format = 'YYYY-MM-DD'): string =>
  dayjs(getDefaultEndDate()).add(1, 'day').format(format);

/**
 * 获取默认的报表开始日期，为一周前
 */
export const getDefaultStartDate = (format = 'YYYY-MM-DD'): string =>
  // 默认一周前，即往前数6天
  dayjs(getDefaultEndDate(), 'YYYY-MM-DD').subtract(6, 'day').format(format);

/**
 * 查询时间不在有效时间段段话，返回实际时间，已经提示
 * @param activeDate 实际开始时间\结束时间
 * @param getDateBegin 此次查询开始时间 YYYY-MM-DD
 * @param getDateEnd 此次查询结束时间 YYYY-MM-DD
 */
export const getRealActiveDateBeginAndTips = (
  activeDate: ActiveCompassRulePeriod,
  getDateBegin: string,
): {
  /** 开始时间不一致时，字段非空 */
  tips: string;
  /** 实际的查询开始时间 */
  dateBegin: string;
} => {
  const activeDateBegin = dayjs(`${activeDate.active_begin_time}`, 'YYYYMMDD');
  const dateBegin = dayjs(getDateBegin, 'YYYY-MM-DD');

  // 实际开始时间
  const resDateBegin = dateBegin.isBefore(activeDateBegin) ? activeDateBegin : dateBegin;

  // 实际开始时间，与此次查询时间一致，直接返回
  if (resDateBegin.isSame(dateBegin, 'day')) {
    return {
      dateBegin: resDateBegin.format('YYYY-MM-DD'),
      tips: '',
    };
  }

  // 获取文案提示
  // 举例：假如数据生效起始日期为8.8日；
  // 如果查8.7日开始的数据，提示：8.7日暂无数据XXX
  // 如果查8.6日开始的数据，提示：8.6-8.7日暂无数据XXX
  const activePreviousDateBegin = activeDateBegin.subtract(1, 'day');
  let tips = `${activePreviousDateBegin.format('YYYY/MM/DD')}暂无数据，将展示除此外的数据。`;
  if (dateBegin.isBefore(activePreviousDateBegin, 'date')) {
    tips = `${dayjs(dateBegin).format('YYYY/MM/DD')}-${tips}`;
  }

  return {
    dateBegin: resDateBegin.format('YYYY-MM-DD'),
    tips,
  };
};
