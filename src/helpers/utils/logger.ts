class Logger {
  private name = '';
  constructor(name: string) {
    this.name = name;
  }

  info(...args: unknown[]) {
    console.info(`[${this.name}]`, ...args);
  }

  log(...args: unknown[]) {
    console.log(`[${this.name}]`, ...args);
  }

  warn(...args: unknown[]) {
    console.warn(`[${this.name}]`, ...args);
  }

  error(...args: unknown[]) {
    console.error(`[${this.name}]`, ...args);
  }
}

export default Logger;
