/**
 * 优化类型
 */
export enum OptimizationType {
  /** 未知类型 */
  OPTIMIZATION_UNKNOW = 0,
  /** 紧急优化 */
  OPTIMIZATION_FAST = 1,
  /** 提升优化 */
  OPTIMIZATION_PROMOTION = 2,
}

/**
 * 优化类型Map
 */
export const OptimizationTypeMap = {
  // 未知类型，当作提升优化来处理
  [OptimizationType.OPTIMIZATION_UNKNOW]: { typeName: '提升优化', typeTheme: 'primary' },
  [OptimizationType.OPTIMIZATION_FAST]: { typeName: '紧急优化', typeTheme: 'danger' },
  [OptimizationType.OPTIMIZATION_PROMOTION]: {
    typeName: '提升优化',
    typeTheme: 'primary',
  },
};

/**
 * 经营概况类型
 */
export enum BusinessOverviewType {
  /** 未知类型 */
  BUSINESS_OVERVIEW_UNKNOW = 0,
  /** 支付金额 */
  BUSINESS_OVERVIEW_PAY_AMOUNT = 1,
  /** 支付客单价 */
  BUSINESS_OVERVIEW_SINGLE_AMOUNT = 2,
  /** 支付订单数 */
  BUSINESS_OVERVIEW_PAY_ORDER_NUM = 3,
  /** 支付用户数 */
  BUSINESS_OVERVIEW_PAY_USER_NUM = 4,
  /** 营销成交金额 */
  BUSINESS_OVERVIEW_CONSUME_AMOUNT = 5,
  /** 营销成交订单数 */
  BUSINESS_OVERVIEW_CONSUME_ORDER_NUM = 6,
  /** 营销领取用户数 */
  BUSINESS_OVERVIEW_RECEIVE_USER_NUM = 7,
  /** 营销核销用户数 */
  BUSINESS_OVERVIEW_CONSUME_USER_NUM = 8,
}

/**
 * 经营概况类型Map
 */
export const BusinessOverviewTypeMap = {
  [BusinessOverviewType.BUSINESS_OVERVIEW_UNKNOW]: {
    title: '未知类型',
    tips: '',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_PAY_AMOUNT]: {
    title: '支付金额',
    tips: '选定周期内，品牌下所有商户号的支付订单的实收金额总和',
    typeOfAmount: true,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_SINGLE_AMOUNT]: {
    title: '支付客单价',
    tips: '选定周期内的支付金额总和/支付用户数总和',
    typeOfAmount: true,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_PAY_ORDER_NUM]: {
    title: '支付订单数',
    tips: '选定周期内，品牌下所有商户号的支付订单数量总和；合单支付场景下算子单总和。',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_PAY_USER_NUM]: {
    title: '支付用户数',
    tips: '选定周期内的支付根据用户数总和，根据微信号进行去重计数（当用户选择自定义时间范围时，人数为直接相加）',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_CONSUME_AMOUNT]: {
    title: '营销成交金额',
    tips: '选定周期内，核销了「本品牌下的商户号创建或归属的代金券或商家券」的订单，对应的订单实收金额总和。备注：核销了商家券的订单需要通过「商家券-关联订单信息」接口回传订单ID。',
    typeOfAmount: true,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_CONSUME_ORDER_NUM]: {
    title: '营销成交订单数',
    tips: '选定周期内，核销了「本品牌下的商户号创建或归属的代金券或商家券」的订单，对应的订单数总和。备注：核销了商家券的订单需要通过「商家券-关联订单信息」接口回传订单ID。',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_RECEIVE_USER_NUM]: {
    title: '营销领取用户数',
    tips: '选定周期内，领取了「本品牌下的商户号创建或归属的代金券或商家券」的用户数总和，根据微信号进行去重计数（当用户选择自定义时间范围时，人数为直接相加）',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [BusinessOverviewType.BUSINESS_OVERVIEW_CONSUME_USER_NUM]: {
    title: '营销核销用户数',
    tips: '选定周期内，核销了「本品牌下的商户号创建或归属的代金券或商家券」的用户数总和，根据微信号进行去重计数（当用户选择自定义时间范围时，人数为直接相加）',
    typeOfAmount: false,
    typeOfPercent: false,
  },
};

/**
 * 经营概况类型
 */
export enum BusinessType {
  /** 未知类型 */
  BUSINESS_UNKNOW = 0,
  /** 交易概况 */
  BUSINESS_TYPE_TRADE = 1,
  /** 营销概况 */
  BUSINESS_TYPE_MARKET = 2,
}

/**
 * 活动类型
 */
export enum ActivityType {
  /** 未知类型 */
  ACTIVITY_UNKNOW = 0,
  /** 摇一摇有优惠 */
  ACTIVITY_SHAKE = 1,
  /** 支付有礼 */
  ACTIVITY_PAY_GIFT = 2,
}

/**
 * 用户画像类型
 */
export enum UserPersonaType {
  /** 未知 */
  USER_PERSONA_UNKNOW = 0,
  /** 性别 */
  USER_PERSONA_SEX = 1,
  /** 年龄 */
  USER_PERSONA_AGE = 2,
  /** 省份 */
  USER_PERSONA_REGION_PROVINCE = 3,
  /** 城市 */
  USER_PERSONA_REGION_CITY = 4,
  /** 日均消费 */
  USER_PERSONA_DAY_AVERAGE = 5,
  /** 消费偏好 */
  USER_PERSONA_CONSUME_PREFER = 6,
  /** 消费时间段偏好 */
  USER_PERSONA_CONSUME_TIME_PREFER = 7,
  /** 社交偏好 */
  USER_PERSONA_SOCIAL_PREFER = 8,
}

/**
 * 用户画像类型Map
 */
export const UserPersonaTypeMap = {
  [UserPersonaType.USER_PERSONA_UNKNOW]: { label: '未知' },
  [UserPersonaType.USER_PERSONA_AGE]: { label: '年龄' },
  [UserPersonaType.USER_PERSONA_SEX]: { label: '性别' },
  [UserPersonaType.USER_PERSONA_REGION_PROVINCE]: { label: '省份' },
  [UserPersonaType.USER_PERSONA_REGION_CITY]: { label: '城市' },
  [UserPersonaType.USER_PERSONA_DAY_AVERAGE]: { label: '日均消费' },
  [UserPersonaType.USER_PERSONA_CONSUME_PREFER]: { label: '消费偏好' },
  [UserPersonaType.USER_PERSONA_CONSUME_TIME_PREFER]: { label: '消费时间段偏好' },
  [UserPersonaType.USER_PERSONA_SOCIAL_PREFER]: { label: '社交偏好' },
};

/**
 * 活动概况类型分组
 */
export enum ActivityOverviewTypeGroup {
  UNKNOWN = 0,
  /** 销售数据 */
  GROUP_SALE = 1,
  /** 转化漏斗 */
  GROUP_CONVERT = 2,
}

/**
 * 活动概况类型分组Map
 */
export const ActivityOverviewTypeGroupMap = {
  [ActivityOverviewTypeGroup.UNKNOWN]: { title: '未知' },
  [ActivityOverviewTypeGroup.GROUP_SALE]: { title: '销售数据' },
  [ActivityOverviewTypeGroup.GROUP_CONVERT]: { title: '转化漏斗' },
};

/**
 * 活动概况类型
 */
export enum ActivityOverviewType {
  /** 未知类型 */
  ACTIVITY_OVERVIEW_UNKNOW = 0,
  /** 核销金额 */
  ACTIVITY_OVERVIEW_CONSUME_AMOUNT = 1,
  /** 券曝光次数 */
  ACTIVITY_OVERVIEW_EXPOSURE_CNT = 2,
  /** 券点击次数 */
  ACTIVITY_OVERVIEW_CLICK_CNT = 3,
  /** 券领取次数 */
  ACTIVITY_OVERVIEW_RECEIVE_CNT = 4,
  /** 券核销次数 */
  ACTIVITY_OVERVIEW_CONSUME_CNT = 5,
  /** 券曝光人数 */
  ACTIVITY_OVERVIEW_EXPOSURE_USERS = 6,
  /** 券点击人数 */
  ACTIVITY_OVERVIEW_CLICK_USERS = 7,
  /** 券领取人数 */
  ACTIVITY_OVERVIEW_RECEIVE_USERS = 8,
  /** 券核销人数 */
  ACTIVITY_OVERVIEW_CONSUME_USERS = 9,
  /** 曝光-点击率 */
  ACTIVITY_OVERVIEW_EXPOSURE_CLICK_PERCENT = 10,
  /** 点击-领取率 */
  ACTIVITY_OVERVIEW_CLICK_RECEIVE_PERCENT = 11,
  /** 领取-核销率 */
  ACTIVITY_OVERVIEW_RECEIVE_CONSUME_PERCENT = 12,
  /** 整体转化率 */
  ACTIVITY_OVERVIEW_TOTAL_PERCENT = 13,
}

/**
 * 活动概况类型Map
 */
export const ActivityOverviewTypeMap = {
  [ActivityOverviewType.ACTIVITY_OVERVIEW_UNKNOW]: {
    title: '未知类型',
    tips: '',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_CONSUME_AMOUNT]: {
    title: '核销金额',
    tips: '指标定义：选定周期内产生核销的券的面额。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: true,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_EXPOSURE_CNT]: {
    title: '券曝光次数',
    tips: '指标定义：选定周期内产生曝光的PV。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_CLICK_CNT]: {
    title: '券点击次数',
    tips: '指标定义：选定周期内产生点击的PV。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_RECEIVE_CNT]: {
    title: '券领取次数',
    tips: '指标定义：选定周期内产生领取的PV。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_CONSUME_CNT]: {
    title: '券核销次数',
    tips: '指标定义：选定周期内产生核销的PV。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_EXPOSURE_USERS]: {
    title: '券曝光人数',
    tips: '指标定义：选定周期内产生曝光的人数。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_CLICK_USERS]: {
    title: '券点击人数',
    tips: '指标定义：选定周期内产生点击的人数。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_RECEIVE_USERS]: {
    title: '券领取人数',
    tips: '指标定义：选定周期内产生领取的人数。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_CONSUME_USERS]: {
    title: '券核销人数',
    tips: '指标定义：选定周期内产生核销的人数。活动范围：品牌下的所有商户号创建+归属的活动',
    typeOfAmount: false,
    typeOfPercent: false,
    group: ActivityOverviewTypeGroup.GROUP_SALE,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_EXPOSURE_CLICK_PERCENT]: {
    title: '曝光-点击率',
    tips: '点击PV/曝光PV',
    typeOfAmount: false,
    typeOfPercent: true,
    group: ActivityOverviewTypeGroup.GROUP_CONVERT,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_CLICK_RECEIVE_PERCENT]: {
    title: '点击-领取率',
    tips: '领取PV/点击PV',
    typeOfAmount: false,
    typeOfPercent: true,
    group: ActivityOverviewTypeGroup.GROUP_CONVERT,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_RECEIVE_CONSUME_PERCENT]: {
    title: '领取-核销率',
    tips: '核销PV/领取PV',
    typeOfAmount: false,
    typeOfPercent: true,
    group: ActivityOverviewTypeGroup.GROUP_CONVERT,
  },
  [ActivityOverviewType.ACTIVITY_OVERVIEW_TOTAL_PERCENT]: {
    title: '整体转化率',
    tips: '核销PV/曝光PV',
    typeOfAmount: false,
    typeOfPercent: true,
    group: ActivityOverviewTypeGroup.GROUP_CONVERT,
  },
};

/**
 * 用户分层类型分组
 */
export enum UserPartitionTypeGroup {
  UNKNOWN = 0,
  /** 全部 */
  GROUP_ALL = 1,
  /** 按消费新老 */
  GROUP_REBUY = 2,
  /** 按消费频次 */
  GROUP_FREQ = 3,
  /** 按消费趋势 */
  GROUP_TREND = 4,
}

export const UserPartitionTypeGroupMap = {
  [UserPartitionTypeGroup.UNKNOWN]: { title: '未知' },
  [UserPartitionTypeGroup.GROUP_ALL]: { title: '全部' },
  [UserPartitionTypeGroup.GROUP_REBUY]: { title: '按消费新老' },
  [UserPartitionTypeGroup.GROUP_FREQ]: { title: '按消费频次' },
  [UserPartitionTypeGroup.GROUP_TREND]: { title: '按消费趋势' },
};

/**
 * 用户分层类型
 */
export enum UserPartitionType {
  /** 未知分层类型 */
  USER_PARTITION_UNKNOW = 0,
  /** 全部用户 */
  USER_PARTITION_TOTAL = 1,
  /** 首购用户 */
  USER_PATITION_FIRST = 2,
  /** 复购用户 */
  USER_PATITION_REBUY = 3,
  /** 高频交易用户 */
  USER_PATITION_HIGH_FREQ = 4,
  /** 低频交易用户 */
  USER_PATITION_LOW_FREQ = 5,
  /** 沉默用户 */
  USER_PATITION_SILENCE = 6,
  /** 流失用户 */
  USER_PATITION_LOSE = 7,
  /** 交易频次稳定用户 */
  USER_PATITION_STABILITY_FREQ = 8,
  /** 交易频次提升用户 */
  USER_PATITION_ADD_FREQ = 9,
  /** 交易频次下降用户 */
  USER_PATITION_REDUCE_FREQ = 10,
  /** 回流用户 */
  USER_PATITION_BACK = 11,
}

export const UserPartitionTypeMap: {
  [key: number]: {
    title: string;
    group: UserPartitionTypeGroup;
    tips: string;
    typeOfAmount: boolean;
    typeOfPercent: boolean;
  };
} = {
  [UserPartitionType.USER_PARTITION_UNKNOW]: {
    title: '未知',
    group: UserPartitionTypeGroup.UNKNOWN,
    tips: '',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PARTITION_TOTAL]: {
    title: '全部用户',
    group: UserPartitionTypeGroup.GROUP_ALL,
    tips: '',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_FIRST]: {
    title: '首购用户',
    group: UserPartitionTypeGroup.GROUP_REBUY,
    tips: '所设置的消费周期内，发生了近3年首次在本品牌商户号下产生交易的用户。',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_REBUY]: {
    title: '复购用户',
    group: UserPartitionTypeGroup.GROUP_REBUY,
    tips: '所设置的消费周期内，非近3年首次在本品牌商户号下产生交易的用户。',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_HIGH_FREQ]: {
    title: '高频交易用户',
    group: UserPartitionTypeGroup.GROUP_FREQ,
    tips: '所设置的消费周期内，在本品牌商户号下产生交易次数≥x的用户，x由商户自定义',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_LOW_FREQ]: {
    title: '低频交易用户',
    group: UserPartitionTypeGroup.GROUP_FREQ,
    tips: '所设置的消费周期内，在本品牌商户号下产生交易次数≥x的用户、且剔除高频交易用户，x由商户自定义',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_SILENCE]: {
    title: '沉默用户',
    group: UserPartitionTypeGroup.GROUP_FREQ,
    tips: '上个消费周期在本品牌商户号下产生交易，本周期未产生交易的用户',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_LOSE]: {
    title: '流失用户',
    group: UserPartitionTypeGroup.GROUP_FREQ,
    tips: '近n+1个消费周期在本品牌商户号下产生交易，近n个消费周期未产生交易的用户，且剔除沉默用户；n由商户自定义',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_STABILITY_FREQ]: {
    title: '交易频次稳定用户',
    group: UserPartitionTypeGroup.GROUP_TREND,
    tips: '近2个消费周期内交易频次稳定的用户数',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_ADD_FREQ]: {
    title: '交易频次提升用户',
    group: UserPartitionTypeGroup.GROUP_TREND,
    tips: '近2个消费周期内交易频次上升的用户数',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_REDUCE_FREQ]: {
    title: '交易频次下降用户',
    group: UserPartitionTypeGroup.GROUP_TREND,
    tips: '近2个消费周期内交易频次下降的用户数',
    typeOfAmount: false,
    typeOfPercent: false,
  },
  [UserPartitionType.USER_PATITION_BACK]: {
    title: '回流用户',
    group: UserPartitionTypeGroup.GROUP_TREND,
    tips: '上个消费周期在本品牌商户号下无交易、本周期有交易的老用户',
    typeOfAmount: false,
    typeOfPercent: false,
  },
};

export const RegionProvince = {
  110000: '北京市',
  120000: '天津市',
  130000: '河北省',
  140000: '山西省',
  150000: '内蒙古自治区',
  210000: '辽宁省',
  220000: '吉林省',
  230000: '黑龙江省',
  310000: '上海市',
  320000: '江苏省',
  330000: '浙江省',
  340000: '安徽省',
  350000: '福建省',
  360000: '江西省',
  370000: '山东省',
  410000: '河南省',
  420000: '湖北省',
  430000: '湖南省',
  440000: '广东省',
  450000: '广西壮族自治区',
  460000: '海南省',
  500000: '重庆市',
  510000: '四川省',
  520000: '贵州省',
  530000: '云南省',
  540000: '西藏自治区',
  610000: '陕西省',
  620000: '甘肃省',
  630000: '青海省',
  640000: '宁夏回族自治区',
  650000: '新疆维吾尔自治区',
  710000: '台湾省',
  810000: '香港特别行政区',
  820000: '澳门特别行政区',
};

/**
 * 活动状态枚举
 */
export enum ActivityStatus {
  ACTIVITY_STATUS_UNKNOWN = 0, // 未知状态
  ACTIVITY_STATUS_CREATING = 1, // 创建中
  ACTIVITY_STATUS_CREATED = 2, // 创建成功
  ACTIVITY_STATUS_TERMINATING = 3, // 终止中
  ACTIVITY_STATUS_TERMINATED = 4, // 已终止
  ACTIVITY_STATUS_EXPIRED = 5, // 已过期
}

export enum ActivityStatusExtend {
  ACTIVITY_STATUS_ALL = -1,
}

export const ActivityStatusMap = {
  [ActivityStatus.ACTIVITY_STATUS_UNKNOWN]: { label: '未知' },
  [ActivityStatus.ACTIVITY_STATUS_CREATING]: { label: '创建中' },
  [ActivityStatus.ACTIVITY_STATUS_CREATED]: { label: '创建成功' },
  [ActivityStatus.ACTIVITY_STATUS_TERMINATING]: { label: '终止中' },
  [ActivityStatus.ACTIVITY_STATUS_TERMINATED]: { label: '已终止' },
  [ActivityStatus.ACTIVITY_STATUS_EXPIRED]: { label: '已过期' },
};

export const ActivityStatusOptions = () => {
  const list = Object.entries(ActivityStatusMap)
    .filter(([key, _value]) => Number(key) !== ActivityStatus.ACTIVITY_STATUS_UNKNOWN)
    .map(([key, value]) => ({
      label: value.label,
      value: Number(key),
    }));
  list.unshift({ value: ActivityStatusExtend.ACTIVITY_STATUS_ALL, label: '全部' });

  return list;
};
