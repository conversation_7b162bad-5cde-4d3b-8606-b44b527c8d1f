/**
 * @file 洞察相关辅助工具方法
 */

import { dayjs, formatAmountFromFen } from '@tencent/xpage-web-sdk';
import { STATISTIC_DATA_COLOR, type StatisticDataCardInfo, type StatisticDataTrendInfo } from './types/insight';
import { ActivityOverview, BusinessOverview, UserPartition, UserPersonaDetailContent } from './types/models';
import {
  ActivityOverviewType,
  ActivityOverviewTypeMap,
  BusinessOverviewType,
  BusinessOverviewTypeMap,
  UserPartitionType,
  UserPartitionTypeMap,
} from './constants/model';
import { percentInteger2String } from './utils/format';

export enum TradeCycleDays {
  ONE = 1,
  TWO = 2,
  THREE = 3,
  FOUR = 4,
  FIVE = 5,
  SIX = 6,
  SEVEN = 7,
  FOURTEEN = 14,
  TWENTY_ONE = 21,
  THIRTY = 30,
  SIXTY = 60,
  NINETY = 90,
  ONE_HUNDRED_EIGHTY = 180,
  THREE_HUNDRED_SIXTY_FIVE = 365,
}

/**
 * 周期天数
 */
export const TRADE_CYCLE_DAYS_OPTIONS = [
  { value: 1, label: '1天' },
  { value: 2, label: '2天' },
  { value: 3, label: '3天' },
  { value: 4, label: '4天' },
  { value: 5, label: '5天' },
  { value: 6, label: '6天' },
  { value: 7, label: '7天' },
  { value: 14, label: '14天' },
  { value: 21, label: '21天' },
  { value: 30, label: '30天' },
  { value: 60, label: '60天' },
  { value: 90, label: '90天' },
  { value: 180, label: '180天' },
  { value: 365, label: '365天' },
];

/**
 * 洞察趋势图最多选中的指标数量
 */
const TREND_SELECTED_MAX = {
  value: 2,
  tips: '最多只能选择两个指标',
};

/**
 * 把统计值格式化
 * 金额情况：8888 => 88.88
 * 百分比情况：8888 => 88.88
 * 其他情况：直接返回原值
 *
 * @param value 原始的值
 * @param amount 是非为金额
 * @param isPercent 是非为百分比
 * @returns 格式化后的数值
 */
export const getStatisticValueDisplay = (value: number, amount: boolean, isPercent: boolean): string | number => {
  if (amount) {
    return formatAmountFromFen(value);
  }
  if (isPercent) {
    return percentInteger2String(value);
  }
  return value;
};

/**
 * 格式化数字
 */
export function formatNumber(value: string | number): string {
  const num = typeof value === 'string' ? Number(value) : value;
  if (Number.isNaN(num)) {
    return `${value}`;
  }
  return num.toLocaleString();
}

/*
 * 根据原始数据获取【统计数据】
 * @param businessOverview 原始数据
 * @returns 统计数据
 */
export const getBusinessOverviewOfStatistics = (businessOverview: BusinessOverview): StatisticDataCardInfo[] => {
  const statistics: StatisticDataCardInfo[] = [];

  (businessOverview.business_overview_statistics_list || [])
    // 过滤掉未知类型的数据
    .filter((item) => item.business_overview_type !== BusinessOverviewType.BUSINESS_OVERVIEW_UNKNOW)
    .sort((a, b) => a.business_overview_type - b.business_overview_type)
    .forEach((item) => {
      const typeInfo = BusinessOverviewTypeMap[item.business_overview_type as BusinessOverviewType];
      if (!typeInfo) {
        return;
      }

      statistics.push({
        overviewType: item.business_overview_type,
        title: typeInfo.title,
        tip: typeInfo.tips,
        value: typeInfo.typeOfPercent
          ? percentInteger2String(item.overview_statistics_num)
          : item.overview_statistics_num,
        changeRate: item.overview_compare_last_cycle,
        checked: false,
        amount: typeInfo.typeOfAmount,
        checkedColor: '',
        checkedTime: 0,
      });
    });

  const currentTime = new Date().getTime();
  STATISTIC_DATA_COLOR.forEach((color, index) => {
    if (statistics.length > index) {
      statistics[index].checked = true;
      statistics[index].checkedColor = color;
      statistics[index].checkedTime = currentTime + index; // 第二个选中的卡片，需要比第一个选中的卡片时间戳大
    }
  });

  return statistics;
};

/**
 * 根据统计数据获取【趋势图表数据】
 * @param statistics 统计数据
 * @param businessOverview 原始总数据
 * @returns
 */
export const getBusinessOverviewOfTrend = (
  statistics: StatisticDataCardInfo[],
  businessOverview: BusinessOverview,
): StatisticDataTrendInfo[] => {
  const res = statistics
    .filter((x) => x.checked)
    .map((x) => {
      const trendInfo = businessOverview.business_overview_trend_list.find(
        (y) => y.business_overview_type === x.overviewType,
      );
      const typeInfo = BusinessOverviewTypeMap[x.overviewType as BusinessOverviewType];
      return {
        name: x.title,
        color: x.checkedColor!,
        value: (trendInfo?.business_overview_detail_list || []).map(
          (y) =>
            [
              dayjs(`${y.time_date}`, 'YYYYMMDD').format('YYYY/MM/DD'),
              typeInfo.typeOfAmount ? formatAmountFromFen(y.user_num) : y.user_num,
            ] as [string, number | string],
        ),
      };
    });

  return res;
};

/**
 * 切换要显示的趋势图表数据
 * @param statistics 所有图表数据
 * @param selected 是否选中
 * @param item 被切换的数据
 * @returns
 */
export const switchOverviewCheckedState = (
  statistics: StatisticDataCardInfo[],
  selected: boolean,
  item: StatisticDataCardInfo,
): {
  success: boolean;
  message: string;
  /** 切换后的图表数据 */
  statistics: StatisticDataCardInfo[];
} => {
  // 如果要取消勾选，不允许，直接返回
  if (!selected) {
    return {
      success: true,
      message: '',
      statistics,
    };
  }

  // 如果要显示的卡片已经超过了最大可显示的数量，把最早显示的卡片取消选中
  let resStatistics: StatisticDataCardInfo[] = [];

  const selectedList = statistics.filter((x) => x.checked).sort((a, b) => a.checkedTime! - b.checkedTime!);
  if (selectedList.length >= TREND_SELECTED_MAX.value) {
    resStatistics = statistics.map((x) => {
      if (selectedList[0].overviewType === x.overviewType) {
        return {
          ...x,
          checked: false,
          checkedColor: '',
          checkedTime: 0,
        };
      }
      return x;
    });
  }

  // 找一个还没用过的颜色
  const notUsedColors = STATISTIC_DATA_COLOR.filter((color) =>
    resStatistics.every((x) => !x.checked || (x.checked && x.checkedColor !== color)),
  );
  const notUsedColor = notUsedColors.length ? notUsedColors[0] : STATISTIC_DATA_COLOR[0];

  // 设置选中的卡片
  resStatistics = resStatistics.map((x) => {
    if (x.overviewType === item.overviewType) {
      // eslint-disable-next-line no-param-reassign
      x.checkedColor = selected ? notUsedColor : '';
      return {
        ...x,
        checked: true,
        checkedColor: notUsedColor,
        checkedTime: new Date().getTime(),
      };
    }

    return x;
  });

  return {
    success: true,
    message: '',
    statistics: resStatistics,
  };
};

/*
 * 根据原始数据获取【统计数据】
 * @param activityOverview 原始数据
 * @returns 统计数据
 */
export const getActivityOverviewOfStatistics = (activityOverview: ActivityOverview): StatisticDataCardInfo[] => {
  const statistics: StatisticDataCardInfo[] = [];
  (activityOverview.activity_overview_statistics_list || [])
    // 过滤掉未知类型的数据
    .filter((item) => item.activity_overview_type !== ActivityOverviewType.ACTIVITY_OVERVIEW_UNKNOW)
    .forEach((item) => {
      const typeInfo = ActivityOverviewTypeMap[item.activity_overview_type as ActivityOverviewType];
      if (!typeInfo) {
        return;
      }

      statistics.push({
        overviewType: item.activity_overview_type,
        title: typeInfo.title,
        tip: typeInfo.tips,
        value: typeInfo.typeOfPercent
          ? percentInteger2String(item.overview_statistics_num)
          : item.overview_statistics_num,
        changeRate: item.overview_compare_last_cycle,
        checked: false,
        amount: typeInfo.typeOfAmount,
        isPercent: typeInfo.typeOfPercent,
        checkedColor: '',
        checkedTime: 0,
      });
    });

  const currentTime = new Date().getTime();
  STATISTIC_DATA_COLOR.forEach((color, index) => {
    if (statistics.length > index) {
      statistics[index].checked = true;
      statistics[index].checkedColor = color;
      statistics[index].checkedTime = currentTime + index; // 第二个选中的卡片，需要比第一个选中的卡片时间戳大
    }
  });

  return statistics;
};

/**
 * 根据统计数据获取【趋势图表数据】
 * @param statistics 统计数据
 * @param activityOverview 原始总数据
 * @returns
 */
export const getActivityOverviewOfTrend = (
  statistics: StatisticDataCardInfo[],
  activityOverview: ActivityOverview,
): StatisticDataTrendInfo[] => {
  const res = (statistics || [])
    .filter((x) => x.checked)
    .map((x) => {
      const trendInfo = activityOverview.activity_overview_trend_list.find(
        (y) => y.activity_overview_type === x.overviewType,
      );
      const typeInfo = ActivityOverviewTypeMap[x.overviewType as ActivityOverviewType];

      return {
        name: x.title,
        color: x.checkedColor!,
        value: (trendInfo?.activity_overview_detail_list || []).map(
          (y) =>
            [
              dayjs(`${y.time_date}`, 'YYYYMMDD').format('YYYY/MM/DD'),
              getStatisticValueDisplay(y.user_num, typeInfo.typeOfAmount, typeInfo.typeOfPercent),
            ] as [string, number | string],
        ),
      };
    });

  return res;
};

/*
 * 根据原始数据获取【统计数据】
 * @param userInsightOverview 原始数据
 * @returns 统计数据
 */
export const getUserInsightOfStatistics = (userInsightOverview: UserPartition): StatisticDataCardInfo[] => {
  const statistics: StatisticDataCardInfo[] = [];

  const currentTime = new Date().getTime();
  const getColor = (type: UserPartitionType) => {
    if (type === UserPartitionType.USER_PATITION_HIGH_FREQ) {
      return {
        color: STATISTIC_DATA_COLOR[0],
        checked: true,
        time: currentTime,
      };
    }
    if (type === UserPartitionType.USER_PATITION_LOW_FREQ) {
      return {
        color: STATISTIC_DATA_COLOR[1],
        checked: true,
        time: currentTime + 1, // 作为第二个选中的卡片，需要比第一个选中的卡片时间戳大
      };
    }
    return {
      color: '',
      checked: false,
      time: 0,
    };
  };
  (userInsightOverview.user_partition_statistics_list || [])
    // 过滤掉未知类型的数据
    .filter((item) => item.user_partition_type !== UserPartitionType.USER_PARTITION_UNKNOW)
    .forEach((item) => {
      const typeInfo = UserPartitionTypeMap[item.user_partition_type as UserPartitionType];
      if (!typeInfo) {
        return;
      }

      statistics.push({
        overviewType: item.user_partition_type,
        title: typeInfo.title,
        tip: typeInfo.tips,
        value: typeInfo.typeOfPercent ? percentInteger2String(item.user_statistics_num) : item.user_statistics_num,
        changeRate: item.user_compare_last_cycle,
        checked: getColor(item.user_partition_type).checked,
        amount: false,
        checkedColor: getColor(item.user_partition_type).color,
        checkedTime: getColor(item.user_partition_type).time,
      });
    });

  return statistics;
};

/**
 * 根据统计数据获取【趋势图表数据】
 * @param statistics 统计数据
 * @param userPartition 原始总数据
 * @returns
 */
export const getUserInsightOfTrend = (
  statistics: StatisticDataCardInfo[],
  userPartition: UserPartition,
): StatisticDataTrendInfo[] => {
  const res = statistics
    .filter((x) => x.checked)
    .map((x) => {
      const trendInfo = userPartition.user_trend_list.find((y) => y.user_partition_type === x.overviewType);
      return {
        name: x.title,
        color: x.checkedColor!,
        value: (trendInfo?.user_partition_detail_list || []).map(
          (y) => [dayjs(`${y.time_date}`, 'YYYYMMDD').format('YYYY/MM/DD'), y.user_num] as [string, number | string],
        ),
      };
    });

  return res;
};

/**
 * 根据百分比，推算大概的数据
 * @param list 所有列表
 * @param percent 需要计算的百分比
 */
export const computCountByPercentOfPersona = (list: UserPersonaDetailContent[], percent: string): number => {
  // 找有效记录列表：number不为0
  const validList = (list || []).filter((x) => x.number);
  if (!validList.length) {
    return 0;
  }

  const validPercent = Number(validList[0].percent.replace(/%/, ''));
  const validNumber = validList[0].number;

  const targetPercent = Number(percent.replace(/%/, ''));

  const targetNumber = Math.round((validNumber / validPercent) * targetPercent);
  return targetNumber;
};
