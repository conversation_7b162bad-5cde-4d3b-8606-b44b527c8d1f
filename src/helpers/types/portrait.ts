export type PieChartData = Array<{ name: string; value: number; customPercent: number }>;

/**
 * 柱状图数据
 * @property {string} 0 - 柱状图名称
 * @property {number} 1 - 柱状图数值
 * @property {string} 2 - 柱状图颜色
 */
export type BarChartItem = {
  name: string;
  value: number;
  color: string;
  customPercent?: number;
};

export type TableData = {
  value: number;
  content: string;
  number: number;
  percent: string;
  rank: number | string;
  customPercent?: number;
};

export type UserPortraitInfo = {
  title: string; // 画像标题
  link: {
    // 详情链接
    content?: string; // 链接文案
    href: string; // 链接地址
    target?: string; // 跳转目标
  };
  data: Array<{
    title: string; // 画像维度标题
    value: string; // 画像维度值信息
    desc: string; // 画像维度描述
  }>;
};
