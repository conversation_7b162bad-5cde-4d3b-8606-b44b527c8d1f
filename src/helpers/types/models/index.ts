export type TaskInfoReq = {
  /**
   * 时间
   */
  callbackTimestamp?: number;

  taskPayload?: {};

  /**
   * 请求方法(POST, PUT,GET,DELETE)
   */
  method?: string;

  /**
   * 回调URL
   */
  callbackUrl?: string;
};

export type TaskInfoResp = {
  code?: number;

  msg?: string;
};

export type Payload = {
  /**
   * 值（对应redis的值）
   */
  value?: string;
};

export type ListResultRes = {
  code?: number;

  msg?: string;

  payloads?: Payload[];
};

export type AffectRes = {
  payload?: number;

  code?: number;

  msg?: string;
};

export type ResultRes = {
  code?: number;

  msg?: string;

  payload?: {};
};

/**
 * batchRead 的请求参数
 */
export type IbatchReadRequestParams = {
  path: {
    domain: string;

    key: string;
  };
};

/**
 * batchWrite 的请求参数
 */
export type IbatchWriteRequestParams = {
  query: {
    query: string;
  };

  path: {
    domain: string;

    key: string;
  };
};

/**
 * deleteKey 的请求参数
 */
export type IdeleteKeyRequestParams = {
  path: {
    /**
     * 分类域
     */
    domain: string;

    /**
     * 键
     */
    key: string;
  };
};

/**
 * getKey 的请求参数
 */
export type IgetKeyRequestParams = {
  path: {
    /**
     * 分类域
     */
    domain: string;

    /**
     * 键
     */
    key: string;
  };
};

/**
 * writeKey 的请求参数
 */
export type IwriteKeyRequestParams = {
  query: {
    /**
     * 单位 秒
     */
    expire: number;
  };

  path: {
    /**
     * 分类域
     */
    domain: string;

    /**
     * 键
     */
    key: string;
  };
};
/**
 * batchRead 的请求参数
 */
export type TBatchReadRequestParams = {
  path: {
    domain: string;

    key: string;
  };
};

/**
 * batchWrite 的请求参数
 */
export type TBatchWriteRequestParams = {
  query: {
    query: string;
  };

  path: {
    domain: string;

    key: string;
  };
};

/**
 * 请求体
 */
export type TBatchWriteRequestBody = {
  code: string;
};

/**
 * 响应体
 */
export type TBatchWriteResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  msg?: string;

  /**
   * 返回数据对象
   */
  data?: {};
};

/**
 * deleteKey 的请求参数
 */
export type TDeleteKeyRequestParams = {
  path: {
    /**
     * 分类域
     */
    domain: string;

    /**
     * 键
     */
    key: string;
  };
};

/**
 * getKey 的请求参数
 */
export type TGetKeyRequestParams = {
  path: {
    /**
     * 分类域
     */
    domain: string;

    /**
     * 键
     */
    key: string;
  };
};

/**
 * writeKey 的请求参数
 */
export type TWriteKeyRequestParams = {
  query: {
    /**
     * 单位 秒， 需大于2s， 小于半年防止db写暴
     */
    expire: number;
  };

  path: {
    /**
     * 分类域
     */
    domain: string;

    /**
     * 键
     */
    key: string;
  };
};

/**
 * 经营概况统计
 */
export type BusinessOverviewStatistics = {
  /**
   * 经营概况类型 BusinessOverviewType（BUSINESS_OVERVIEW_UNKNOW = 0;  // 未知类型   BUSINESS_OVERVIEW_PAY_AMOUNT = 1;  // 支付金额   BUSINESS_OVERVIEW_SINGLE_AMOUNT = 2;  // 支付客单价   BUSINESS_OVERVIEW_PAY_ORDER_NUM = 3;  // 支付订单数   BUSINESS_OVERVIEW_PAY_USER_NUM = 4;  // 支付用户数   BUSINESS_OVERVIEW_CONSUME_AMOUNT = 5;  // 营销成交金额   BUSINESS_OVERVIEW_CONSUME_ORDER_NUM = 6;  // 营销成交订单数   BUSINESS_OVERVIEW_RECEIVE_USER_NUM = 7;  // 营销领取用户数   BUSINESS_OVERVIEW_CONSUME_USER_NUM = 8;  // 营销核销用户数）
   */
  business_overview_type: number;

  /**
   * 本周期统计值
   */
  overview_statistics_num: number;

  /**
   * 较上周期
   */
  overview_compare_last_cycle: string;
};

/**
 * 经营数据详情
 */
export type BusinessOverviewDetail = {
  /**
   * 时间日期 yyyymmdd
   */
  time_date: number;

  /**
   * 数量
   */
  user_num: number;
};

/**
 * 经营概况数据趋势
 */
export type BusinessOverviewTrend = {
  /**
   * 经营概况类型 BusinessOverviewType（BUSINESS_OVERVIEW_UNKNOW = 0;  // 未知类型   BUSINESS_OVERVIEW_PAY_AMOUNT = 1;  // 支付金额   BUSINESS_OVERVIEW_SINGLE_AMOUNT = 2;  // 支付客单价   BUSINESS_OVERVIEW_PAY_ORDER_NUM = 3;  // 支付订单数   BUSINESS_OVERVIEW_PAY_USER_NUM = 4;  // 支付用户数   BUSINESS_OVERVIEW_CONSUME_AMOUNT = 5;  // 营销成交金额   BUSINESS_OVERVIEW_CONSUME_ORDER_NUM = 6;  // 营销成交订单数   BUSINESS_OVERVIEW_RECEIVE_USER_NUM = 7;  // 营销领取用户数   BUSINESS_OVERVIEW_CONSUME_USER_NUM = 8;  // 营销核销用户数）
   */
  business_overview_type: number;

  /**
   * 经营概况详情列表
   */
  business_overview_detail_list: BusinessOverviewDetail[];
};


export type BusinessOverview = {
  /**
   * 经营概况统计列表
   */
  business_overview_statistics_list: BusinessOverviewStatistics[];

  /**
   * 经营概况趋势列表
   */
  business_overview_trend_list: BusinessOverviewTrend[];
};

/**
 * 优化
 */
export type Optimization = {
  /**
   * 按钮跳转链接
   */
  button_url: string;

  /**
   * url为空是的提示内容
   */
  empty_url_content: string;

  /**
   * 优化类型 OptimizationType（OPTIMIZATION_UNKNOW = 0;  // 未知类型   OPTIMIZATION_FAST = 1;  // 紧急优化   OPTIMIZATION_PROMOTION = 2;  // 提升优化）
   */
  optimization_type: number;

  /**
   * 内容
   */
  content: string;

  /**
   * 解释说明
   */
  explain: string;

  /**
   * 按钮内容
   */
  button_content: string;
};


export type BusinessSuggest = {
  /**
   * 优化列表
   */
  optimization_list: Optimization[];
};


export type CompassRule = {
  /**
   * 交易周期
   */
  trade_cycle: number;

  /**
   * 高频交易用户阈值
   */
  high_freq_trade_user_limit: number;

  /**
   * 低频交易用户阈值
   */
  low_freq_trade_user_limit: number;

  /**
   * 流失用户统计周期
   */
  lose_user_cycle: number;
};

/**
 * 用户分层统计
 */
export type UserPartitionStatistics = {
  /**
   * 用户分层类型 UserPartitionType（USER_PARTITION_UNKNOW = 0;  // 未知分层类型   USER_PARTITION_TOTAL = 1;  // 全部用户   USER_PATITION_FIRST = 2;  // 首购用户   USER_PATITION_REBUY = 3;  // 复购用户   USER_PATITION_HIGH_FREQ = 4;  // 高频交易用户   USER_PATITION_LOW_FREQ = 5;  // 低频交易用户   USER_PATITION_SILENCE = 6;  // 沉默用户   USER_PATITION_LOSE = 7;  // 流失用户   USER_PATITION_STABILITY_FREQ = 8;  // 交易频次稳定用户   USER_PATITION_ADD_FREQ = 9;  // 交易频次提升用户   USER_PATITION_REDUCE_FREQ = 10;  // 交易频次下降用户）
   */
  user_partition_type: number;

  /**
   * 本周期统计值
   */
  user_statistics_num: number;

  /**
   * 较上周期
   */
  user_compare_last_cycle: string;
};

/**
 * 用户分层详情
 */
export type UserPartitionDetail = {
  /**
   * 时间日期 yyyymmdd
   */
  time_date: number;

  /**
   * 数量
   */
  user_num: number;
};

/**
 * 用户数据趋势
 */
export type UserTrend = {
  /**
   * 用户分层类型 UserPartitionType（USER_PARTITION_UNKNOW = 0;  // 未知分层类型   USER_PARTITION_TOTAL = 1;  // 全部用户   USER_PATITION_FIRST = 2;  // 首购用户   USER_PATITION_REBUY = 3;  // 复购用户   USER_PATITION_HIGH_FREQ = 4;  // 高频交易用户   USER_PATITION_LOW_FREQ = 5;  // 低频交易用户   USER_PATITION_SILENCE = 6;  // 沉默用户   USER_PATITION_LOSE = 7;  // 流失用户   USER_PATITION_STABILITY_FREQ = 8;  // 交易频次稳定用户   USER_PATITION_ADD_FREQ = 9;  // 交易频次提升用户   USER_PATITION_REDUCE_FREQ = 10;  // 交易频次下降用户）
   */
  user_partition_type: number;

  /**
   * 用户分层详情列表
   */
  user_partition_detail_list: UserPartitionDetail[];
};

/**
 * 用户分层
 */
export type UserPartition = {
  /**
   * 用户分层统计列表
   */
  user_partition_statistics_list: UserPartitionStatistics[];

  /**
   * 用户分层统计列表
   */
  user_trend_list: UserTrend[];
};

/**
 * 转化来源
 */
export type UserFlowConversion = {
  /**
   * 用户分层类型 UserPartitionType（USER_PARTITION_UNKNOW = 0;  // 未知分层类型   USER_PARTITION_TOTAL = 1;  // 全部用户   USER_PATITION_FIRST = 2;  // 首购用户   USER_PATITION_REBUY = 3;  // 复购用户   USER_PATITION_HIGH_FREQ = 4;  // 高频交易用户   USER_PATITION_LOW_FREQ = 5;  // 低频交易用户   USER_PATITION_SILENCE = 6;  // 沉默用户   USER_PATITION_LOSE = 7;  // 流失用户   USER_PATITION_STABILITY_FREQ = 8;  // 交易频次稳定用户   USER_PATITION_ADD_FREQ = 9;  // 交易频次提升用户   USER_PATITION_REDUCE_FREQ = 10;  // 交易频次下降用户）
   */
  user_partition_type: number;

  /**
   * 数量
   */
  user_num: number;

  /**
   * 转化率
   */
  conversion_percent: string;

  /**
   * 较上周期
   */
  user_compare_last_cycle: string;
};

/**
 * 用户流向信息
 */
export type UserFlowInfo = {
  /**
   * 用户分层类型 UserPartitionType（USER_PARTITION_UNKNOW = 0;  // 未知分层类型   USER_PARTITION_TOTAL = 1;  // 全部用户   USER_PATITION_FIRST = 2;  // 首购用户   USER_PATITION_REBUY = 3;  // 复购用户   USER_PATITION_HIGH_FREQ = 4;  // 高频交易用户   USER_PATITION_LOW_FREQ = 5;  // 低频交易用户   USER_PATITION_SILENCE = 6;  // 沉默用户   USER_PATITION_LOSE = 7;  // 流失用户   USER_PATITION_STABILITY_FREQ = 8;  // 交易频次稳定用户   USER_PATITION_ADD_FREQ = 9;  // 交易频次提升用户   USER_PATITION_REDUCE_FREQ = 10;  // 交易频次下降用户）
   */
  user_partition_type: number;

  /**
   * 数量
   */
  user_num: number;

  /**
   * 转化来源
   */
  user_flow_conversion: UserFlowConversion[];
};

/**
 * 用户流向
 */
export type UserFlow = {
  /**
   * 用户流向信息
   */
  user_flow_info: UserFlowInfo[];
};

/**
 * 用户画像概况类别
 */
export type UserPersonaSurveyCategory = {
  /**
   * 占比
   */
  percent: string;

  /**
   * 用户画像类型 UserPersonaType（USER_PERSONA_UNKNOW = 0;  // 未知类型 USER_PERSONA_AGE = 1;  // 年龄 USER_PERSONA_SEX = 2;  // 性别 USER_PERSONA_REGION_PROVINCE = 3;  // 地域编码-省 USER_PERSONA_REGION_CITY = 4;  // 地域编码-市 USER_PERSONA_DAY_AVERAGE = 5;  // 日均消费 USER_PERSONA_CONSUME_PREFER = 6;  // 消费偏好 USER_PERSONA_CONSUME_TIME_PREFER = 7;  // 消费时间段偏好 USER_PERSONA_SOCIAL_PREFER = 8;  // 社交偏好）
   */
  user_persona_type: number;

  /**
   * 前端展示的值（根据item_type字段映射到到值）
   */
  label: string;

  /**
   * 对应内容的枚举(如男性为1、女性为2)
   */
  item_type: number;

  /**
   * 数量
   */
  number: number;
};


export type UserPersonaSurvey = {
  /**
   * 用户画像概况分层列表
   */
  user_persona_survey_category_list: UserPersonaSurveyCategory[];
};

/**
 * 用户画像详情内容
 */
export type UserPersonaDetailContent = {
  /**
   * 占比(如23.4%)
   */
  percent: string;

  /**
   * 排名
   */
  rank: number;

  /**
   * 对应内容的枚举(如男性为1、女性为2)
   */
  item_type: number;

  /**
   * 数量
   */
  number: number;

  /**
   * 占比的数字形式(如23.4%, 这里为2340)
   */
  percent_integer: number;

  /**
   * 前端展示的值（根据item_type字段映射到到值）
   */
  label: string;
};

/**
 * 用户画像详情类别
 */
export type UserPersonaDetailCategory = {
  /**
   * 用户画像类型 UserPersonaType（USER_PERSONA_UNKNOW = 0;  // 未知类型 USER_PERSONA_AGE = 1;  // 年龄 USER_PERSONA_SEX = 2;  // 性别 USER_PERSONA_REGION_PROVINCE = 3;  // 地域编码-省 USER_PERSONA_REGION_CITY = 4;  // 地域编码-市 USER_PERSONA_DAY_AVERAGE = 5;  // 日均消费 USER_PERSONA_CONSUME_PREFER = 6;  // 消费偏好 USER_PERSONA_CONSUME_TIME_PREFER = 7;  // 消费时间段偏好 USER_PERSONA_SOCIAL_PREFER = 8;  // 社交偏好）
   */
  user_persona_type: number;

  /**
   * 用户画像详情内容列表
   */
  user_persona_detail_content_list: UserPersonaDetailContent[];
};

/**
 * 用户画像详情
 */
export type UserPersonaDetail = {
  /**
   * 用户画像详情类别列表
   */
  user_persona_detail_category_list: UserPersonaDetailCategory[];
};

/**
 * V1GetCompassBusinessOverview 的请求参数
 */
export type TV1GetCompassBusinessOverviewRequestParams = {
  query: {
    /**
     * 查询的开始日期-必填, 如2024-08-10
     */
    get_date_begin: string;

    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassBusinessOverviewResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    trade_overview: BusinessOverview;

    market_overview: BusinessOverview;

    business_suggest: BusinessSuggest;

    active_compass_rule: ActiveCompassRule;

    active_compass_rule_period: ActiveCompassRulePeriod;

    real_date: RealDate;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassRuleResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  data?: {
    compass_rule: CompassRule;
  };
};

/**
 * 请求体
 */
export type TV1PutCompassRuleRequestBody = {
  compass_rule: CompassRule;
};

/**
 * 响应体
 */
export type TV1PutCompassRuleResponseParams = {
  data?: {
    compass_rule: CompassRule;
  };

  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;
};

/**
 * V1GetCompassUserInsight 的请求参数
 */
export type TV1GetCompassUserInsightRequestParams = {
  query: {
    /**
     * 查询的日期-必填, 如2024-08-17
     */
    get_date: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassUserInsightResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  data?: {
    user_partition: UserPartition;

    user_flow: UserFlow;

    user_persona_survey: UserPersonaSurvey;

    active_compass_rule: ActiveCompassRule;

    active_compass_rule_period: ActiveCompassRulePeriod;

    /**
     * 实际查询使用的日期YYYY-MM-DD
     */
    real_get_date: string;
  };
};

/**
 * V1GetCompassUserPersonaDetail 的请求参数
 */
export type TV1GetCompassUserPersonaDetailRequestParams = {
  query: {
    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;

    /**
     * 生效的规则版本号-必填, 如1，从ActiveCompassRule中获取
     */
    active_rule_version: string;

    /**
     * 生效的品牌聚合版本号-必填, 如2，从ActiveCompassRule中获取
     */
    active_brand_version: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassUserPersonaDetailResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    user_persona_detail: UserPersonaDetail;
  };
};

/**
 * V1GetCompassUserTrend 的请求参数
 */
export type TV1GetCompassUserTrendRequestParams = {
  query: {
    /**
     * 查询的开始日期-必填, 如2024-08-10
     */
    get_date_begin: string;

    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;

    /**
     * 生效的规则版本号-必填, 如1，从ActiveCompassRule中获取
     */
    active_rule_version: string;

    /**
     * 生效的品牌聚合版本号-必填, 如2，从ActiveCompassRule中获取
     */
    active_brand_version: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassUserTrendResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    /**
     * 用户趋势列表
     */
    user_trend_list: UserTrend[];
  };
};


export type ActivityOverviewStatistics = {
  /**
   * 活动概况类型 ActivityOverviewType（ACTIVITY_OVERVIEW_UNKNOW = 0;  // 未知类型   ACTIVITY_OVERVIEW_CONSUME_AMOUNT = 1;  // 核销金额   ACTIVITY_OVERVIEW_EXPOSURE_CNT = 2;  // 券曝光次数   ACTIVITY_OVERVIEW_CLICK_CNT = 3;  // 券点击次数   ACTIVITY_OVERVIEW_RECEIVE_CNT = 4;  // 券领取次数   ACTIVITY_OVERVIEW_CONSUME_CNT = 5;  // 券核销次数   ACTIVITY_OVERVIEW_EXPOSURE_USERS = 6;  // 券曝光人数   ACTIVITY_OVERVIEW_CLICK_USERS = 7;  // 券点击人数   ACTIVITY_OVERVIEW_RECEIVE_USERS = 8;  // 券领取人数   ACTIVITY_OVERVIEW_CONSUME_USERS = 9;  // 券核销人数   ACTIVITY_OVERVIEW_EXPOSURE_CLICK_PERCENT = 10;  // 曝光-点击率   ACTIVITY_OVERVIEW_CLICK_RECEIVE_PERCENT = 11;  // 点击-领取率   ACTIVITY_OVERVIEW_RECEIVE_CONSUME_PERCENT = 12;  // 领取-核销率   ACTIVITY_OVERVIEW_TOTAL_PERCENT = 13;  // 整体转化率）
   */
  activity_overview_type: number;

  /**
   * 本周期统计值
   */
  overview_statistics_num: number;

  /**
   * 较上周期
   */
  overview_compare_last_cycle: string;
};


export type ActivityOverviewDetail = {
  /**
   * 时间日期 yyyymmdd
   */
  time_date: number;

  /**
   * 数量
   */
  user_num: number;
};


export type ActivityOverviewTrend = {
  /**
   * 活动概况类型 ActivityOverviewType（ACTIVITY_OVERVIEW_UNKNOW = 0;  // 未知类型   ACTIVITY_OVERVIEW_CONSUME_AMOUNT = 1;  // 核销金额   ACTIVITY_OVERVIEW_EXPOSURE_CNT = 2;  // 券曝光次数   ACTIVITY_OVERVIEW_CLICK_CNT = 3;  // 券点击次数   ACTIVITY_OVERVIEW_RECEIVE_CNT = 4;  // 券领取次数   ACTIVITY_OVERVIEW_CONSUME_CNT = 5;  // 券核销次数   ACTIVITY_OVERVIEW_EXPOSURE_USERS = 6;  // 券曝光人数   ACTIVITY_OVERVIEW_CLICK_USERS = 7;  // 券点击人数   ACTIVITY_OVERVIEW_RECEIVE_USERS = 8;  // 券领取人数   ACTIVITY_OVERVIEW_CONSUME_USERS = 9;  // 券核销人数   ACTIVITY_OVERVIEW_EXPOSURE_CLICK_PERCENT = 10;  // 曝光-点击率   ACTIVITY_OVERVIEW_CLICK_RECEIVE_PERCENT = 11;  // 点击-领取率   ACTIVITY_OVERVIEW_RECEIVE_CONSUME_PERCENT = 12;  // 领取-核销率   ACTIVITY_OVERVIEW_TOTAL_PERCENT = 13;  // 整体转化率）
   */
  activity_overview_type: number;

  /**
   * 活动概况详情列表
   */
  activity_overview_detail_list: ActivityOverviewDetail[];
};


export type ActivityOverview = {
  /**
   * 活动概况统计列表
   */
  activity_overview_statistics_list: ActivityOverviewStatistics[];

  /**
   * 活动概况趋势列表
   */
  activity_overview_trend_list: ActivityOverviewTrend[];
};

/**
 * 罗盘生效规则
 */
export type ActiveCompassRule = {
  /**
   * 交易周期
   */
  trade_cycle: number;

  /**
   * 高频交易用户阈值
   */
  high_freq_trade_user_limit: number;

  /**
   * 低频交易用户阈值
   */
  low_freq_trade_user_limit: number;

  /**
   * 流失用户统计周期
   */
  lose_user_cycle: number;

  /**
   * 规则版本号
   */
  rule_version: number;

  /**
   * 品牌版本号
   */
  brand_version: number;

  /**
   * 推荐的周期
   */
  recommend_cycle: number;
};

/**
 * V1GetCompassActivityDetailOverview 的请求参数
 */
export type TV1GetCompassActivityDetailOverviewRequestParams = {
  query: {
    /**
     * 活动id
     */
    activity_id: string;

    /**
     * 查询的开始日期-必填, 如2024-08-10
     */
    get_date_begin: string;

    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;

    /**
     * ACTIVITY_UNKNOW = 0;  // 未知类型   ACTIVITY_SHAKE = 1;  // 摇一摇有优惠   ACTIVITY_PAY_GIFT = 2;  // 支付有礼
     */
    activity_type: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassActivityDetailOverviewResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    activity_detail_overview: ActivityOverview;

    active_compass_rule_period: ActiveCompassRulePeriod;

    real_date: RealDate;
  };
};

/**
 * V1GetCompassActivityOverview 的请求参数
 */
export type TV1GetCompassActivityOverviewRequestParams = {
  query: {
    /**
     * 查询的开始日期-必填, 如2024-08-10
     */
    get_date_begin: string;

    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;

    /**
     * ACTIVITY_UNKNOW = 0;  // 未知类型   ACTIVITY_SHAKE = 1;  // 摇一摇有优惠   ACTIVITY_PAY_GIFT = 2;  // 支付有礼
     */
    activity_type: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassActivityOverviewResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    activity_persona: UserPersonaSurvey;

    active_compass_rule_period: ActiveCompassRulePeriod;

    active_compass_rule: ActiveCompassRule;

    activity_overview: ActivityOverview;

    real_date: RealDate;
  };
};

/**
 * V1GetCompassActivityPersonaDetail 的请求参数
 */
export type TV1GetCompassActivityPersonaDetailRequestParams = {
  query: {
    /**
     * 查询的开始日期-必填, 如2024-08-10
     */
    get_date_begin: string;

    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;

    /**
     * 活动类型ACTIVITY_UNKNOW = 0;  // 未知类型   ACTIVITY_SHAKE = 1;  // 摇一摇有优惠
     */
    activity_type: string;

    /**
     * 生效的品牌聚合版本号-必填, 如2，从ActiveCompassRule中获取
     */
    active_brand_version: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassActivityPersonaDetailResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    activity_persona_detail: UserPersonaDetail;
  };
};

/**
 * V1GetCompassBusinessOverviewByBusinessType 的请求参数
 */
export type TV1GetCompassBusinessOverviewByBusinessTypeRequestParams = {
  query: {
    /**
     * 查询的开始日期-必填, 如2024-08-10
     */
    get_date_begin: string;

    /**
     * 查询的结束日期-必填, 如2024-08-17
     */
    get_date_end: string;

    /**
     * 生效的品牌聚合版本号-必填, 如2，从ActiveCompassRule中获取
     */
    active_brand_version: string;

    /**
     * 当前版本的生效起始时间-必填, 如2024-08-17，从ActiveCompassRulePeriod中获取
     */
    active_begin_time: string;
  };

  path: {
    /**
     * 经营概况类型BUSINESS_UNKNOW = 0;  // 未知类型   BUSINESS_TYPE_TRADE = 1;  // 交易概况   BUSINESS_TYPE_MARKET = 2;  // 营销概况
     */
    business_type: string;
  };
};

/**
 * 响应体
 */
export type TV1GetCompassBusinessOverviewByBusinessTypeResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    business_data: BusinessOverview;
  };
};

/**
 * 罗盘有效数据的时间段
 */
export type ActiveCompassRulePeriod = {
  /**
   * 生效开始时间
   */
  active_begin_time: number;

  /**
   * 生效结束时间
   */
  active_end_time: number;
};


export type ActivityDeliveryRule = {
  /**
   * 投放开始时间（时间戳）
   */
  delivery_start_time: number;

  /**
   * 投放结束时间（时间戳）
   */
  delivery_end_time: number;

  activity_label: TargetLabel[][];
};

/**
 * 活动信息
 */
export type BrandManagerActivity = {
  /**
   * 活动ID
   */
  act_id: number;

  /**
   * 活动名称，不超过9汉字字符
   */
  act_name: string;

  /**
   * 活动描述，自定义文案，for C 端详情页优惠介绍展示
   */
  act_description: string;

  /**
   * 活动状态ActivityStatus（ACTIVITY_STATUS_UNKNOWN = 0;// 未知状态    ACTIVITY_STATUS_CREATING = 1;// 创建中    ACTIVITY_STATUS_CREATED = 2;// 创建成功    ACTIVITY_STATUS_TERMINATING = 3;// 终止中    ACTIVITY_STATUS_TERMINATED = 4;// 已终止    ACTIVITY_STATUS_EXPIRED = 5;// 已过期）
   */
  act_status: number;

  act_delivery_rule: ActivityDeliveryRule;

  /**
   * 创建时间（时间戳）
   */
  create_time: number;

  /**
   * 修改时间（时间戳）
   */
  modify_time: number;

  /**
   * 关联的券信息列表
   */
  stock_list: Stock[];
};

/**
 * 活动筛选条件, 选填（为空则只查: 自己创建、归属的, 品牌主则会额外查品牌下的活动）
 */
export type ActivityFilter = {
  /**
   * 活动ID
   */
  act_id?: number;

  /**
   * 活动类型ActivityType（ACTIVITY_TYPE_UNKNOWN = 0;                         // 未知状态 ACTIVITY_TYPE_STORE_WIDE = 1;// 商家全场活动 ACTIVITY_TYPE_SINGLE_PRODUCT = 2;// 商家单品活动 ACTIVITY_TYPE_PLATFORM_DISCOUNT = 3;// 平台立减活动 ACTIVITY_TYPE_BANK_DISCOUNT = 4;// 银行立减活动）
   */
  act_type?: number;

  /**
   * 投放开始时间
   */
  delivery_start_time?: number;

  /**
   * 投放结束时间
   */
  delivery_end_time?: number;

  /**
   * 活动名称
   */
  act_name?: string;

  /**
   * 归属方商户
   */
  belong_account?: number;

  /**
   * 活动状态ActivityStatus（ACTIVITY_STATUS_UNKNOWN = 0;// 未知状态    ACTIVITY_STATUS_CREATING = 1;// 创建中    ACTIVITY_STATUS_CREATED = 2;// 创建成功    ACTIVITY_STATUS_TERMINATING = 3;// 终止中    ACTIVITY_STATUS_TERMINATED = 4;// 已终止    ACTIVITY_STATUS_EXPIRED = 5;// 已过期）
   */
  ActivityStatus?: number;
};

/**
 * 分页信息
 */
export type PageInfo = {
  /**
   * 偏移量
   */
  offset: number;

  /**
   * 限制（最大值：50）
   */
  limit: number;
};

/**
 * V1GetActivity 的请求参数
 */
export type TV1GetActivityRequestParams = {
  query: {
    /**
     * 偏移量
     */
    offset?: string;

    /**
     * 限制，(最大值：50)
     */
    limit?: string;

    /**
     * 活动名称
     */
    act_name?: string;

    /**
     * 活动状态
     */
    act_status?: string;
  };
};

/**
 * 响应体
 */
export type TV1GetActivityResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    total_count: number;

    /**
     * 活动信息列表
     */
    activity_info_list: BrandManagerActivity[];
  };
};

/**
 * V1GetActivityById 的请求参数
 */
export type TV1GetActivityByIdRequestParams = {
  path: {
    activity_id: string;
  };
};

/**
 * 响应体
 */
export type TV1GetActivityByIdResponseParams = {
  /**
   * 返回错误码
   */
  code?: number;

  /**
   * 返回错误信息
   */
  message?: string;

  /**
   * 返回数据对象
   */
  data?: {
    activity_info: BrandManagerActivity;
  };
};


export type TargetLabel = {
  /**
   * 标签类型eLabelType（LABEL_TYPE_UNKNOWN = 0; LABEL_TYPE_AGE = 1; // 年龄 LABEL_TYPE_GENDER = 2; // 性别 LABEL_TYPE_LIVING_CITY = 3; // 长住城市 LABEL_TYPE_JOB = 4; // 职业(数据未ready) LABEL_TYPE_CONSUME_PREFERENCE = 5; // 消费偏好 LABEL_TYPE_TRADE_TREND = 6; // 消费趋势 LABEL_TYPE_TRADE_FREQ = 7; // 消费频次 LABEL_TYPE_TRADE_NEW = 8; // 消费新老 LABEL_TYPE_CONSUME_PER_DAY = 9; // 日均消费水平 LABEL_TYPE_AMOUNT_PER_TRADE = 10; // 笔均消费水平 LABEL_TYPE_CONSUME_PERIOD_PREFERENCE = 11; // 消费时间偏好 LABEL_TYPE_GROUP_TRADE = 12; // 拼单爱好人群 LABEL_TYPE_CITY_LEVEL = 13; // 城市等级）
   */
  label_type: number;

  /**
   * 标签值
   */
  label_value: string;

  /**
   * 标签显示值
   */
  label_display: string;
};

/**
 * 券信息
 */
export type Stock = {
  /**
   * 券批次ID
   */
  stock_id: string;

  /**
   * 批次名称
   */
  stock_name: string;
};

/**
 * 实际查询使用的日期
 */
export type RealDate = {
  /**
   * 结束日期YYYY-MM-DD
   */
  end_date: string;

  /**
   * 开始日期YYYY-MM-DD
   */
  begin_date: string;
};

