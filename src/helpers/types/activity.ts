/**
 * @file 营销活动相关类型定义
 */

/**
 * 活动状态类型
 */
export enum ActivityState {
  All = 0,
  Start = 1,
  NotStart = 2,
  End = 3,
}

export type PeerRankInfo = {
  name: string; // 商品名称
  image: string; // 商品图片
  label: string; // 商品标签信息
  description: string; // 描述信息
  growth?: string; // 变化率
};

export type ActivityInfo = {
  activityId: string; // 活动 id
  activityName: string; // 活动名称
  activityState: ActivityState; // 活动状态
  activityTime: string; // 活动时间
  createTime: string; // 创建时间
  couponInfo: string; // 商家券信息
  targetCrowdInfo?: string; // 投放目标人群信息
};
