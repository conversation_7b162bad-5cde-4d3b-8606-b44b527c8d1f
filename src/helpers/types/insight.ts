/**
 * 数据统计卡片 - 类型
 */
export type StatisticDataCardInfo = {
  overviewType: string | number; // 卡片 key
  title: string; // 卡片标题
  tip: string; // 卡片问号展现的 tip 信息
  value: string | number; // 卡片展现的指标值，如果是金额单位为 分
  amount?: boolean; // 指标值是否金额
  isPercent?: boolean; // 指标值是否百分比
  changeRate: string; // 展现的指标变化率
  peerChangeRate?: string; // 同行的指标变化率
  checked?: boolean; // 卡片是否选中
  checkedColor?: string; // 卡片选中颜色
  checkedTime?: number; // 卡片选中时间戳 用户选择卡片时对最早入队的卡片进行替换
  activityNum?: number; // 投放活动数量
};

/**
 * 数据统计趋势图 - 类型
 */
export type StatisticDataTrendInfo = {
  name: string;
  color: string;
  value: [string | number, string | number][];
};

/**
 * 数据统计卡片 - 选中后显示的颜色
 */
export const STATISTIC_DATA_COLOR = ['#07C160', '#3399FF'];
/**
 * 饼图的颜色
 */
export const PIE_CHART_COLOR = ['#07C160', '#1485EE'];
/**
 * 饼图的默认颜色
 */
export const PIE_CHART_COLOR_DEFAULT = '#E6E6E6';

export type ActivityTradeDataInfo = {
  title: string;
  details: StatisticDataCardInfo[];
};

export type ProductBasicInfo = {
  name: string; // 商品名称
  logo: string; // 商品 logo
  typeName: string; // 商品类型名
  id: string; // 商品 id
  oriAmount: string; // 商品原价，单位分
  discountAmount: string; // 商品优惠价格，单位分
};

export type ProductTableRowData = ProductBasicInfo & {
  type: number; // 商品类型
  productState: 1; // 商品状态
  displayNum: string; // 展示次数
  clickNum: string; // 点击次数
  payNum: string; // 成交次数
  consumeNum: string; // 核销次数
};

export type ConsumeStoreRankInfo = {
  name: string; // 门店名称
  description: string; // 门店描述
  sales: number; // 核销数量
  growth: string; // 核销变化率
};

export type ConsumeProductRankInfo = {
  name: string; // 商品名称
  image: string; // 商品图片
  label: string; // 商品类型标签名
  price: string; // 商品价格，单位分
  sales: number; // 核销数量
  growth: string; // 核销变化率
};

export type ActivityProductPriceInfo = {
  label: string; // 消费群体标签
  price: string; // 消费价格，单位分
};

export type ProductActivityInfo = {
  title: string; // 活动标题
  startDate: string; // 活动开始日期
  endDate: string; // 活动结束日期
  priceGroups: Array<ActivityProductPriceInfo>;
};

export type SankeyDataCardInfo = {
  label: string;
  value: number | string;
  num: number | string;
  converRate?: number | string;
  compare?: number | string;
};
