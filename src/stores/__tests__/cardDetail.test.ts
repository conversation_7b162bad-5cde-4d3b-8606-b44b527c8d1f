import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';
import { useCardDetailStore } from '../cardDetail';
import {
  mockNewUserState,
  mockPartialConfiguredState,
  mockAuditingState,
  mockRejectedState,
  mockReadyToPublishState,
  mockCouponAccessState,
  mockApiErrorState,
  mockApprovedState,
  getExpectedResults,
} from '@/pages/card-mna/card-detail-vue/__tests__/mockStates';

// Mock API调用
vi.mock('@/services/cardDetail', () => ({
  cardDetailApi: {
    getLatestCardInfoPublishProcess: vi.fn(),
    getLatestServicePublishProcess: vi.fn(),
    getBrandMemberAccess: vi.fn(),
    getShakeCouponAccess: vi.fn(),
    getPreviewData: vi.fn(),
    getConfigStatus: vi.fn(),
  },
}));

describe('useCardDetailStore - 状态计算测试', () => {
  let store: ReturnType<typeof useCardDetailStore>;
  const expectedResults = getExpectedResults();

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useCardDetailStore();
  });

  describe('新手用户状态测试', () => {
    beforeEach(() => {
      // 设置mock数据
      store.latestCardInfoPublishProcess = mockNewUserState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess = mockNewUserState.latestServicePublishProcess;
      store.brandMemberAccess = mockNewUserState.brandMemberAccess;
      store.shakeCouponAccess = mockNewUserState.shakeCouponAccess;

      // 触发状态更新
      store.updateConfigItemsStatus();
    });

    it('基础信息配置项应该显示未配置状态', () => {
      const basicInfo = store.configItems[0];
      const expected = expectedResults.newUser.basicInfo;

      expect(basicInfo.statusText).toBe(expected.statusText);
      expect(basicInfo.buttonText).toBe(expected.buttonText);
      expect(basicInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(basicInfo.completed).toBe(expected.completed);
    });

    it('名片服务配置项应该显示未配置状态', () => {
      const serviceInfo = store.configItems[1];
      const expected = expectedResults.newUser.serviceInfo;

      expect(serviceInfo.statusText).toBe(expected.statusText);
      expect(serviceInfo.buttonText).toBe(expected.buttonText);
      expect(serviceInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(serviceInfo.completed).toBe(expected.completed);
    });

    it('品牌会员配置项应该显示未接入状态', () => {
      const brandInfo = store.configItems[2];
      const expected = expectedResults.newUser.brandInfo;

      expect(brandInfo.statusText).toBe(expected.statusText);
      expect(brandInfo.buttonText).toBe(expected.buttonText);
      expect(brandInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(brandInfo.completed).toBe(expected.completed);
    });

    it('发布按钮应该被禁用', () => {
      expect(store.canPublish).toBe(expectedResults.newUser.canPublish);
    });
  });

  describe('部分配置完成状态测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess =
        mockPartialConfiguredState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess =
        mockPartialConfiguredState.latestServicePublishProcess;
      store.brandMemberAccess = mockPartialConfiguredState.brandMemberAccess;
      store.shakeCouponAccess = mockPartialConfiguredState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('基础信息应该显示已配置状态', () => {
      const basicInfo = store.configItems[0];
      const expected = expectedResults.partialConfigured.basicInfo;

      expect(basicInfo.statusText).toBe(expected.statusText);
      expect(basicInfo.buttonText).toBe(expected.buttonText);
      expect(basicInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(basicInfo.completed).toBe(expected.completed);
    });

    it('名片服务应该显示配置中状态', () => {
      const serviceInfo = store.configItems[1];
      const expected = expectedResults.partialConfigured.serviceInfo;

      expect(serviceInfo.statusText).toBe(expected.statusText);
      expect(serviceInfo.buttonText).toBe(expected.buttonText);
      expect(serviceInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(serviceInfo.completed).toBe(expected.completed);
    });

    it('品牌会员应该显示未接入状态', () => {
      const brandInfo = store.configItems[2];
      const expected = expectedResults.partialConfigured.brandInfo;

      expect(brandInfo.statusText).toBe(expected.statusText);
      expect(brandInfo.buttonText).toBe(expected.buttonText);
      expect(brandInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(brandInfo.completed).toBe(expected.completed);
    });

    it('发布按钮应该被禁用', () => {
      expect(store.canPublish).toBe(expectedResults.partialConfigured.canPublish);
    });
  });

  describe('审核中状态测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess = mockAuditingState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess = mockAuditingState.latestServicePublishProcess;
      store.brandMemberAccess = mockAuditingState.brandMemberAccess;
      store.shakeCouponAccess = mockAuditingState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('基础信息应该显示审核中状态', () => {
      const basicInfo = store.configItems[0];
      const expected = expectedResults.auditing.basicInfo;

      expect(basicInfo.statusText).toBe(expected.statusText);
      expect(basicInfo.buttonText).toBe(expected.buttonText);
      expect(basicInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(basicInfo.completed).toBe(expected.completed);
    });

    it('发布按钮应该被禁用(审核中)', () => {
      expect(store.canPublish).toBe(expectedResults.auditing.canPublish);
    });
  });

  describe('审核驳回状态测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess = mockRejectedState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess = mockRejectedState.latestServicePublishProcess;
      store.brandMemberAccess = mockRejectedState.brandMemberAccess;
      store.shakeCouponAccess = mockRejectedState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('基础信息应该显示审核驳回状态', () => {
      const basicInfo = store.configItems[0];
      const expected = expectedResults.rejected.basicInfo;

      expect(basicInfo.statusText).toBe(expected.statusText);
      expect(basicInfo.buttonText).toBe(expected.buttonText);
      expect(basicInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(basicInfo.completed).toBe(expected.completed);
    });

    it('发布按钮应该被禁用(有驳回项)', () => {
      expect(store.canPublish).toBe(expectedResults.rejected.canPublish);
    });
  });

  describe('准备发布状态测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess =
        mockReadyToPublishState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess =
        mockReadyToPublishState.latestServicePublishProcess;
      store.brandMemberAccess = mockReadyToPublishState.brandMemberAccess;
      store.shakeCouponAccess = mockReadyToPublishState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('所有配置项应该显示已配置状态', () => {
      const expected = expectedResults.readyToPublish;

      expect(store.configItems[0].statusText).toBe(expected.basicInfo.statusText);
      expect(store.configItems[0].buttonText).toBe(expected.basicInfo.buttonText);
      expect(store.configItems[0].buttonTheme).toBe(expected.basicInfo.buttonTheme);
      expect(store.configItems[0].completed).toBe(expected.basicInfo.completed);

      expect(store.configItems[1].statusText).toBe(expected.serviceInfo.statusText);
      expect(store.configItems[1].buttonText).toBe(expected.serviceInfo.buttonText);
      expect(store.configItems[1].buttonTheme).toBe(expected.serviceInfo.buttonTheme);
      expect(store.configItems[1].completed).toBe(expected.serviceInfo.completed);

      expect(store.configItems[2].statusText).toBe(expected.brandInfo.statusText);
      expect(store.configItems[2].buttonText).toBe(expected.brandInfo.buttonText);
      expect(store.configItems[2].buttonTheme).toBe(expected.brandInfo.buttonTheme);
      expect(store.configItems[2].completed).toBe(expected.brandInfo.completed);
    });

    it('发布按钮应该启用', () => {
      expect(store.canPublish).toBe(expectedResults.readyToPublish.canPublish);
    });
  });

  describe('优惠券接入方式测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess =
        mockCouponAccessState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess =
        mockCouponAccessState.latestServicePublishProcess;
      store.brandMemberAccess = mockCouponAccessState.brandMemberAccess;
      store.shakeCouponAccess = mockCouponAccessState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('通过优惠券接入时，品牌配置项应该显示已接入状态', () => {
      const brandInfo = store.configItems[2];
      const expected = expectedResults.couponAccess.brandInfo;

      expect(brandInfo.statusText).toBe(expected.statusText);
      expect(brandInfo.buttonText).toBe(expected.buttonText);
      expect(brandInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(brandInfo.completed).toBe(expected.completed);
    });

    it('通过优惠券接入时发布按钮应该启用', () => {
      expect(store.canPublish).toBe(expectedResults.couponAccess.canPublish);
    });
  });

  describe('API错误状态测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess = mockApiErrorState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess = mockApiErrorState.latestServicePublishProcess;
      store.brandMemberAccess = mockApiErrorState.brandMemberAccess;
      store.shakeCouponAccess = mockApiErrorState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('API错误时发布按钮应该被禁用', () => {
      expect(store.canPublish).toBe(expectedResults.apiError.canPublish);
    });
  });

  describe('待发布状态测试', () => {
    beforeEach(() => {
      store.latestCardInfoPublishProcess = mockApprovedState.latestCardInfoPublishProcess;
      store.latestServicePublishProcess = mockApprovedState.latestServicePublishProcess;
      store.brandMemberAccess = mockApprovedState.brandMemberAccess;
      store.shakeCouponAccess = mockApprovedState.shakeCouponAccess;

      store.updateConfigItemsStatus();
    });

    it('基础信息应该显示审核通过状态', () => {
      const basicInfo = store.configItems[0];
      const expected = expectedResults.approved.basicInfo;

      expect(basicInfo.statusText).toBe(expected.statusText);
      expect(basicInfo.buttonText).toBe(expected.buttonText);
      expect(basicInfo.buttonTheme).toBe(expected.buttonTheme);
      expect(basicInfo.completed).toBe(expected.completed);
    });

    it('待发布状态下发布按钮应该启用', () => {
      expect(store.canPublish).toBe(expectedResults.approved.canPublish);
    });
  });

  describe('完成度计算测试', () => {
    it('应该正确计算完成度百分比', () => {
      // 设置一个配置项为完成状态
      store.configItems[0].completed = true;
      store.configItems[1].completed = false;
      store.configItems[2].completed = false;

      expect(store.completionPercentage).toBe(33); // 1/3 = 33%

      // 设置两个配置项为完成状态
      store.configItems[1].completed = true;
      expect(store.completionPercentage).toBe(67); // 2/3 = 67%

      // 所有配置项完成
      store.configItems[2].completed = true;
      expect(store.completionPercentage).toBe(100); // 3/3 = 100%
    });
  });
});
