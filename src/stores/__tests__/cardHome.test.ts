/**
 * CardHome Store 完整测试套件
 * 基于acceptance.md中的验收标准
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';

import { useCardHomeStore } from '../cardHome';
import { cardHomeApi } from '@/services/cardHome';

// Mock API服务
vi.mock('@/services/cardHome', () => ({
  cardHomeApi: {
    createTutorialPublishProcess: vi.fn(),
    getCardInfo: vi.fn(),
    checkRouterStatus: vi.fn(),
    createCard: vi.fn(),
  },
}));

describe('CardHome Store - 完整测试', () => {
  let store: ReturnType<typeof useCardHomeStore>;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useCardHomeStore();
    vi.clearAllMocks();
  });

  describe('Feature: 商户卡片首页状态管理', () => {
    describe('Scenario: 初始化页面状态', () => {
      it('Given 用户首次访问页面, When 页面加载, Then 应该显示正确的初始状态', () => {
        expect(store.cardState).toEqual({
          useable: false,
        });
        expect(store.tutorialState).toEqual({
          processId: '',
        });
        expect(store.cardInfo).toEqual({
          isConfigured: false,
          cardType: '',
          lastUpdated: '',
        });
        expect(store.loading).toBe(false);
        expect(store.error).toBe(null);
      });

      it('Given 初始状态, When 检查计算属性, Then 应该返回正确的值', () => {
        expect(store.canStartConfig).toBe(true); // !cardState.useable
        expect(store.hasProcessId).toBe(false); // !tutorialState.processId
      });
    });

    describe('Scenario: 检查卡片配置状态', () => {
      it('Given API返回已配置状态, When 检查卡片状态, Then 应该更新状态为已配置', async () => {
        // Given
        vi.mocked(cardHomeApi.getCardInfo).mockResolvedValue({
          isConfigured: true,
          cardType: 'business',
          lastUpdated: '2024-01-01T00:00:00.000Z',
        });

        // When
        await store.checkCardStatus();

        // Then
        expect(store.cardInfo.isConfigured).toBe(true);
        expect(store.cardInfo.cardType).toBe('business');
        expect(store.cardInfo.lastUpdated).toBe('2024-01-01T00:00:00.000Z');
        expect(store.cardState.useable).toBe(true);
        expect(store.canStartConfig).toBe(false);
      });

      it('Given API返回未配置状态, When 检查卡片状态, Then 应该保持未配置状态', async () => {
        // Given
        vi.mocked(cardHomeApi.getCardInfo).mockResolvedValue({
          isConfigured: false,
          cardType: '',
          lastUpdated: '',
        });

        // When
        await store.checkCardStatus();

        // Then
        expect(store.cardInfo.isConfigured).toBe(false);
        expect(store.cardState.useable).toBe(false);
        expect(store.canStartConfig).toBe(true);
      });

      it('Given API调用失败, When 检查卡片状态, Then 应该设置错误状态', async () => {
        // Given
        const errorMessage = '网络错误';
        vi.mocked(cardHomeApi.getCardInfo).mockRejectedValue(new Error(errorMessage));

        // When
        await store.checkCardStatus();

        // Then
        expect(store.error).toBe(errorMessage);
        expect(store.loading).toBe(false);
      });
    });

    describe('Scenario: 创建新手引导流程', () => {
      it('Given 用户点击开始配置, When 创建新手引导流程成功, Then 应该返回true并设置processId', async () => {
        // Given
        const mockProcessId = 'tutorial-process-123';
        vi.mocked(cardHomeApi.createTutorialPublishProcess).mockResolvedValue({
          processId: mockProcessId,
        });

        // When
        const result = await store.createNewTutorial();

        // Then
        expect(result).toBe(true);
        expect(store.tutorialState.processId).toBe(mockProcessId);
        expect(store.hasProcessId).toBe(true);
        expect(store.loading).toBe(false);
        expect(store.error).toBe(null);
      });

      it('Given 用户点击开始配置, When 创建新手引导流程失败, Then 应该返回false并设置错误信息', async () => {
        // Given
        const errorMessage = '创建流程失败';
        vi.mocked(cardHomeApi.createTutorialPublishProcess).mockRejectedValue(
          new Error(errorMessage)
        );

        // When
        const result = await store.createNewTutorial();

        // Then
        expect(result).toBe(false);
        expect(store.tutorialState.processId).toBe('');
        expect(store.hasProcessId).toBe(false);
        expect(store.error).toBe(errorMessage);
        expect(store.loading).toBe(false);
      });

      it('Given API返回空的processId, When 创建新手引导流程, Then 应该抛出错误', async () => {
        // Given
        vi.mocked(cardHomeApi.createTutorialPublishProcess).mockResolvedValue({
          processId: '',
        });

        // When
        const result = await store.createNewTutorial();

        // Then
        expect(result).toBe(false);
        expect(store.error).toBe('创建新手引导流程失败');
      });
    });

    describe('Scenario: 状态重置功能', () => {
      it('Given 页面有各种状态数据, When 调用重置方法, Then 应该恢复到初始状态', () => {
        // Given - 设置一些状态
        store.cardState.useable = true;
        store.tutorialState.processId = 'test-id';
        store.cardInfo.isConfigured = true;
        store.cardInfo.cardType = 'business';
        store.cardInfo.lastUpdated = '2024-01-01';
        store.error = '测试错误';
        store.loading = true;

        // When
        store.resetState();

        // Then
        expect(store.cardState).toEqual({ useable: false });
        expect(store.tutorialState).toEqual({ processId: '' });
        expect(store.cardInfo).toEqual({
          isConfigured: false,
          cardType: '',
          lastUpdated: '',
        });
        expect(store.error).toBe(null);
        expect(store.loading).toBe(false);
      });
    });

    describe('Scenario: 页面初始化流程', () => {
      it('Given 用户访问页面, When 初始化页面数据, Then 应该自动检查卡片状态', async () => {
        // Given
        vi.mocked(cardHomeApi.getCardInfo).mockResolvedValue({
          isConfigured: true,
          cardType: 'premium',
          lastUpdated: '2024-01-01T12:00:00.000Z',
        });

        // When
        await store.initializePageData();

        // Then
        expect(cardHomeApi.getCardInfo).toHaveBeenCalledTimes(1);
        expect(store.cardInfo.isConfigured).toBe(true);
        expect(store.cardInfo.cardType).toBe('premium');
      });
    });
  });

  describe('Feature: 加载状态管理', () => {
    describe('Scenario: API调用期间的加载状态', () => {
      it('Given 调用API, When API执行期间, Then loading应该为true', async () => {
        // Given
        let resolvePromise: (value: {
          isConfigured: boolean;
          cardType: string;
          lastUpdated: string;
        }) => void;
        const promise = new Promise((resolve) => {
          resolvePromise = resolve;
        });
        vi.mocked(cardHomeApi.getCardInfo).mockReturnValue(promise);

        // When
        const checkPromise = store.checkCardStatus();

        // Then - 检查loading状态
        expect(store.loading).toBe(true);

        // 完成Promise
        resolvePromise!({
          isConfigured: false,
          cardType: '',
          lastUpdated: '',
        });
        await checkPromise;

        expect(store.loading).toBe(false);
      });
    });
  });

  describe('Feature: 错误处理', () => {
    describe('Scenario: 网络错误处理', () => {
      it('Given 网络不可用, When 调用API, Then 应该正确处理错误', async () => {
        // Given
        const networkError = new Error('Network Error');
        vi.mocked(cardHomeApi.createTutorialPublishProcess).mockRejectedValue(
          networkError
        );

        // When
        const result = await store.createNewTutorial();

        // Then
        expect(result).toBe(false);
        expect(store.error).toBe('Network Error');
        expect(store.loading).toBe(false);
      });

      it('Given 服务器错误, When 调用API, Then 应该显示友好的错误信息', async () => {
        // Given
        vi.mocked(cardHomeApi.getCardInfo).mockRejectedValue(
          new Error('500 Internal Server Error')
        );

        // When
        await store.checkCardStatus();

        // Then
        expect(store.error).toBe('500 Internal Server Error');
      });
    });
  });
});
