import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  CardState,
  TutorialState,
  CardInfo,
} from '@/pages/card-mna/card-home-vue/types/index';
import { cardHomeApi } from '@/services/cardHome';

/**
 * Card-Home页面状态管理
 * 基于业务规则文档中定义的状态结构
 */
export const useCardHomeStore = defineStore('cardHome', () => {
  // 状态定义 - 基于业务规则文档
  const cardState = ref<CardState>({
    useable: false, // 控制"开始配置"按钮的启用状态
  });

  const tutorialState = ref<TutorialState>({
    processId: '', // 新手引导流程ID
  });

  const cardInfo = ref<CardInfo>({
    isConfigured: false, // 是否已配置
    cardType: '', // 卡片类型
    lastUpdated: '', // 最后更新时间
  });

  // 加载状态
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 计算属性
  const canStartConfig = computed(() => !cardState.value.useable);
  const hasProcessId = computed(() => !!tutorialState.value.processId);

  // Actions - 业务逻辑方法

  /**
   * 创建新手引导流程
   * 对应业务规则中的 CreateNewTutorial 交互
   */
  const createNewTutorial = async (): Promise<boolean> => {
    try {
      loading.value = true;
      error.value = null;

      const response = await cardHomeApi.createTutorialPublishProcess();

      if (response.processId) {
        tutorialState.value.processId = response.processId;
        return true;
      }

      throw new Error('创建新手引导流程失败');
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建新手引导流程失败';
      return false;
    } finally {
      loading.value = false;
    }
  };

  /**
   * 检查卡片状态
   * 获取商户卡片基础信息
   */
  const checkCardStatus = async (): Promise<void> => {
    try {
      loading.value = true;
      error.value = null;

      const response = await cardHomeApi.getCardInfo();

      if (response) {
        cardInfo.value = {
          isConfigured: response.isConfigured || false,
          cardType: response.cardType || '',
          lastUpdated: response.lastUpdated || '',
        };

        // 根据配置状态更新按钮可用性
        cardState.value.useable = response.isConfigured || false;
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取卡片信息失败';
      console.error('检查卡片状态失败:', err);
    } finally {
      loading.value = false;
    }
  };

  /**
   * 重置状态
   */
  const resetState = (): void => {
    cardState.value = { useable: false };
    tutorialState.value = { processId: '' };
    cardInfo.value = { isConfigured: false, cardType: '', lastUpdated: '' };
    error.value = null;
    loading.value = false;
  };

  /**
   * 初始化页面数据
   */
  const initializePageData = async (): Promise<void> => {
    await checkCardStatus();
  };

  return {
    // 状态
    cardState,
    tutorialState,
    cardInfo,
    loading,
    error,

    // 计算属性
    canStartConfig,
    hasProcessId,

    // 方法
    createNewTutorial,
    createTutorialPublishProcess: createNewTutorial, // 别名，保持向后兼容
    checkCardStatus,
    resetState,
    initializePageData,
  };
});
