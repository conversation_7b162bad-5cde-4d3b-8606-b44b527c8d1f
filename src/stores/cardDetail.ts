import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type {
  CardDetailConfig,
  BasicInfo,
  ContactInfo,
  BusinessInfo,
  StyleConfig,
  PageState,
  PreviewState,
  FormValidation,
  ConfigStep,
  PreviewDevice,
  ValidationStatus,
  ErrorInfo,
} from '@/pages/card-mna/card-detail-vue/types/index';
import { cardDetailApi } from '@/services/cardDetail';

/**
 * Card-Detail页面状态管理
 * 基于需求规格文档中定义的状态结构
 */
export const useCardDetailStore = defineStore('cardDetail', () => {
  // ==================== 核心配置数据状态 ====================

  /**
   * 基础信息配置
   */
  const basicInfo = ref<BasicInfo>({
    merchantName: '',
    merchantDesc: '',
    logo: '',
    businessHours: {
      isAllDay: false,
      weekdays: [{ start: '09:00', end: '18:00' }],
      weekends: [{ start: '10:00', end: '17:00' }],
      holidays: [],
    },
    address: {
      province: '',
      city: '',
      district: '',
      detail: '',
      latitude: 0,
      longitude: 0,
    },
  });

  /**
   * 联系方式配置
   */
  const contactInfo = ref<ContactInfo>({
    phones: [],
    wechat: {
      wechatId: '',
      qrCode: '',
      nickname: '',
    },
    email: '',
    website: '',
    socialMedia: [],
  });

  /**
   * 业务介绍配置
   */
  const businessInfo = ref<BusinessInfo>({
    description: '',
    images: [],
    categories: [],
    keywords: [],
    features: [],
  });

  /**
   * 样式配置
   */
  const styleConfig = ref<StyleConfig>({
    theme: {
      id: 'default',
      name: '默认主题',
      preview: '',
    },
    layout: {
      headerStyle: 'standard',
      contentLayout: 'vertical',
      footerStyle: 'simple',
    },
    colors: {
      primary: '#1890ff',
      secondary: '#52c41a',
      background: '#ffffff',
      text: '#333333',
      accent: '#fa8c16',
    },
    fonts: {
      family: 'PingFang SC',
      size: {
        title: 18,
        subtitle: 16,
        body: 14,
        caption: 12,
      },
      weight: {
        title: 600,
        subtitle: 500,
        body: 400,
        caption: 400,
      },
    },
  });

  // ==================== 页面状态管理 ====================

  /**
   * 页面状态
   */
  const pageState = ref<PageState>({
    currentStep: ConfigStep.BASIC_INFO,
    isLoading: false,
    isSaving: false,
    isPublishing: false,
    hasUnsavedChanges: false,
    lastSavedTime: '',
  });

  /**
   * 预览状态
   */
  const previewState = ref<PreviewState>({
    device: PreviewDevice.MOBILE,
    isFullscreen: false,
    isRefreshing: false,
    scale: 1,
  });

  /**
   * 表单验证状态
   */
  const formValidation = ref<FormValidation>({
    basicInfo: ValidationStatus.PENDING,
    contact: ValidationStatus.PENDING,
    business: ValidationStatus.PENDING,
    style: ValidationStatus.VALID,
    overall: ValidationStatus.PENDING,
  });

  /**
   * 错误信息列表
   */
  const errors = ref<ErrorInfo[]>([]);

  /**
   * 配置ID（用于编辑已有配置）
   */
  const configId = ref<string>('');

  /**
   * 配置版本号
   */
  const configVersion = ref<number>(1);

  // ==================== 计算属性 ====================

  /**
   * 完整的配置数据
   */
  const fullConfig = computed<CardDetailConfig>(() => ({
    id: configId.value,
    basicInfo: basicInfo.value,
    contactInfo: contactInfo.value,
    businessInfo: businessInfo.value,
    styleConfig: styleConfig.value,
    version: configVersion.value,
    createdAt: '',
    updatedAt: new Date().toISOString(),
  }));

  /**
   * 是否可以进行下一步
   */
  const canProceedNext = computed(() => {
    const currentStepValidation = getCurrentStepValidation();
    return currentStepValidation === ValidationStatus.VALID;
  });

  /**
   * 是否可以保存配置
   */
  const canSaveConfig = computed(
    () =>
      pageState.value.hasUnsavedChanges &&
      !pageState.value.isSaving &&
      formValidation.value.overall !== ValidationStatus.INVALID,
  );

  /**
   * 是否可以发布配置
   */
  const canPublishConfig = computed(
    () =>
      formValidation.value.overall === ValidationStatus.VALID &&
      !pageState.value.isPublishing &&
      !pageState.value.hasUnsavedChanges,
  );

  /**
   * 配置完成度百分比
   */
  const completionPercentage = computed(() => {
    const validSteps = Object.values(formValidation.value).filter((status) => status === ValidationStatus.VALID).length;
    return Math.round((validSteps / 4) * 100);
  });

  // ==================== 辅助方法 ====================

  /**
   * 获取当前步骤的验证状态
   */
  const getCurrentStepValidation = (): ValidationStatus => {
    switch (pageState.value.currentStep) {
      case ConfigStep.BASIC_INFO:
        return formValidation.value.basicInfo;
      case ConfigStep.CONTACT:
        return formValidation.value.contact;
      case ConfigStep.BUSINESS:
        return formValidation.value.business;
      case ConfigStep.STYLE:
        return formValidation.value.style;
      default:
        return ValidationStatus.PENDING;
    }
  };

  /**
   * 更新整体验证状态
   */
  const updateOverallValidation = (): void => {
    const validations = [
      formValidation.value.basicInfo,
      formValidation.value.contact,
      formValidation.value.business,
      formValidation.value.style,
    ];

    if (validations.every((status) => status === ValidationStatus.VALID)) {
      formValidation.value.overall = ValidationStatus.VALID;
    } else if (validations.some((status) => status === ValidationStatus.INVALID)) {
      formValidation.value.overall = ValidationStatus.INVALID;
    } else {
      formValidation.value.overall = ValidationStatus.PENDING;
    }
  };

  // ==================== Actions ====================

  /**
   * 初始化页面数据
   */
  const initializePageData = async (processId?: string): Promise<void> => {
    try {
      pageState.value.isLoading = true;
      errors.value = [];

      // 如果有processId，加载已有配置
      if (processId) {
        const config = await cardDetailApi.getConfig(processId);
        if (config) {
          loadConfigData(config);
          configId.value = config.id || '';
          configVersion.value = config.version || 1;
        }
      }

      // 初始化验证状态
      await validateAllSteps();
    } catch (error) {
      console.error('初始化页面数据失败:', error);
      addError('init', '初始化页面数据失败');
    } finally {
      pageState.value.isLoading = false;
    }
  };

  /**
   * 加载配置数据到状态中
   */
  const loadConfigData = (config: CardDetailConfig): void => {
    basicInfo.value = { ...config.basicInfo };
    contactInfo.value = { ...config.contactInfo };
    businessInfo.value = { ...config.businessInfo };
    styleConfig.value = { ...config.styleConfig };
    pageState.value.hasUnsavedChanges = false;
  };

  /**
   * 更新基础信息
   */
  const updateBasicInfo = (info: Partial<BasicInfo>): void => {
    basicInfo.value = { ...basicInfo.value, ...info };
    pageState.value.hasUnsavedChanges = true;
    validateBasicInfo();
  };

  /**
   * 更新联系方式
   */
  const updateContactInfo = (info: Partial<ContactInfo>): void => {
    contactInfo.value = { ...contactInfo.value, ...info };
    pageState.value.hasUnsavedChanges = true;
    validateContactInfo();
  };

  /**
   * 更新业务介绍
   */
  const updateBusinessInfo = (info: Partial<BusinessInfo>): void => {
    businessInfo.value = { ...businessInfo.value, ...info };
    pageState.value.hasUnsavedChanges = true;
    validateBusinessInfo();
  };

  /**
   * 更新样式配置
   */
  const updateStyleConfig = (config: Partial<StyleConfig>): void => {
    styleConfig.value = { ...styleConfig.value, ...config };
    pageState.value.hasUnsavedChanges = true;
    validateStyleConfig();
  };

  /**
   * 切换配置步骤
   */
  const changeConfigStep = (step: ConfigStep): void => {
    pageState.value.currentStep = step;
  };

  /**
   * 切换预览设备
   */
  const changePreviewDevice = (device: PreviewDevice): void => {
    previewState.value.device = device;
  };

  /**
   * 切换全屏预览
   */
  const toggleFullscreenPreview = (): void => {
    previewState.value.isFullscreen = !previewState.value.isFullscreen;
  };

  /**
   * 刷新预览
   */
  const refreshPreview = async (): Promise<void> => {
    previewState.value.isRefreshing = true;
    // 模拟刷新延迟
    await new Promise((resolve) => setTimeout(resolve, 500));
    previewState.value.isRefreshing = false;
  };

  // ==================== 验证方法 ====================

  /**
   * 验证基础信息
   */
  const validateBasicInfo = (): ValidationStatus => {
    const info = basicInfo.value;

    if (!info.merchantName.trim()) {
      formValidation.value.basicInfo = ValidationStatus.INVALID;
      addError('merchantName', '商户名称不能为空');
      return ValidationStatus.INVALID;
    }

    if (!info.merchantDesc.trim()) {
      formValidation.value.basicInfo = ValidationStatus.INVALID;
      addError('merchantDesc', '商户简介不能为空');
      return ValidationStatus.INVALID;
    }

    removeError('merchantName');
    removeError('merchantDesc');
    formValidation.value.basicInfo = ValidationStatus.VALID;
    updateOverallValidation();
    return ValidationStatus.VALID;
  };

  /**
   * 验证联系方式
   */
  const validateContactInfo = (): ValidationStatus => {
    const info = contactInfo.value;

    if (info.phones.length === 0 && !info.wechat.wechatId && !info.email) {
      formValidation.value.contact = ValidationStatus.INVALID;
      addError('contact', '至少需要填写一种联系方式');
      return ValidationStatus.INVALID;
    }

    removeError('contact');
    formValidation.value.contact = ValidationStatus.VALID;
    updateOverallValidation();
    return ValidationStatus.VALID;
  };

  /**
   * 验证业务介绍
   */
  const validateBusinessInfo = (): ValidationStatus => {
    const info = businessInfo.value;

    if (!info.description.trim()) {
      formValidation.value.business = ValidationStatus.INVALID;
      addError('businessDesc', '业务描述不能为空');
      return ValidationStatus.INVALID;
    }

    removeError('businessDesc');
    formValidation.value.business = ValidationStatus.VALID;
    updateOverallValidation();
    return ValidationStatus.VALID;
  };

  /**
   * 验证样式配置
   */
  const validateStyleConfig = (): ValidationStatus => {
    // 样式配置通常都是有效的，除非有特殊要求
    formValidation.value.style = ValidationStatus.VALID;
    updateOverallValidation();
    return ValidationStatus.VALID;
  };

  /**
   * 验证所有步骤
   */
  const validateAllSteps = async (): Promise<void> => {
    validateBasicInfo();
    validateContactInfo();
    validateBusinessInfo();
    validateStyleConfig();
  };

  // ==================== 错误处理方法 ====================

  /**
   * 添加错误信息
   */
  const addError = (field: string, message: string, code?: string): void => {
    const existingIndex = errors.value.findIndex((error) => error.field === field);
    const newError: ErrorInfo = { field, message, code };

    if (existingIndex >= 0) {
      errors.value[existingIndex] = newError;
    } else {
      errors.value.push(newError);
    }
  };

  /**
   * 移除错误信息
   */
  const removeError = (field: string): void => {
    const index = errors.value.findIndex((error) => error.field === field);
    if (index >= 0) {
      errors.value.splice(index, 1);
    }
  };

  /**
   * 清空所有错误
   */
  const clearErrors = (): void => {
    errors.value = [];
  };

  // ==================== 数据持久化方法 ====================

  /**
   * 保存配置（草稿或正式保存）
   */
  const saveConfig = async (isDraft = true): Promise<boolean> => {
    try {
      pageState.value.isSaving = true;
      clearErrors();

      const response = await cardDetailApi.saveConfig({
        config: fullConfig.value,
        isDraft,
      });

      if (response.configId) {
        configId.value = response.configId;
        configVersion.value = response.version;
        pageState.value.hasUnsavedChanges = false;
        pageState.value.lastSavedTime = response.savedAt;
        return true;
      }

      throw new Error('保存配置失败');
    } catch (error) {
      console.error('保存配置失败:', error);
      addError('save', '保存配置失败');
      return false;
    } finally {
      pageState.value.isSaving = false;
    }
  };

  /**
   * 发布配置
   */
  const publishConfig = async (publishNote?: string): Promise<boolean> => {
    try {
      pageState.value.isPublishing = true;
      clearErrors();

      // 先保存配置
      const saveSuccess = await saveConfig(false);
      if (!saveSuccess) {
        throw new Error('保存配置失败，无法发布');
      }

      const response = await cardDetailApi.publishConfig({
        configId: configId.value,
        publishNote,
      });

      if (response.publishId) {
        return true;
      }

      throw new Error('发布配置失败');
    } catch (error) {
      console.error('发布配置失败:', error);
      addError('publish', '发布配置失败');
      return false;
    } finally {
      pageState.value.isPublishing = false;
    }
  };

  /**
   * 自动保存配置
   */
  const autoSaveConfig = async (): Promise<void> => {
    if (pageState.value.hasUnsavedChanges && !pageState.value.isSaving) {
      await saveConfig(true);
    }
  };

  /**
   * 重置所有状态
   */
  const resetState = (): void => {
    // 重置配置数据
    basicInfo.value = {
      merchantName: '',
      merchantDesc: '',
      logo: '',
      businessHours: {
        isAllDay: false,
        weekdays: [{ start: '09:00', end: '18:00' }],
        weekends: [{ start: '10:00', end: '17:00' }],
        holidays: [],
      },
      address: {
        province: '',
        city: '',
        district: '',
        detail: '',
        latitude: 0,
        longitude: 0,
      },
    };

    contactInfo.value = {
      phones: [],
      wechat: { wechatId: '', qrCode: '', nickname: '' },
      email: '',
      website: '',
      socialMedia: [],
    };

    businessInfo.value = {
      description: '',
      images: [],
      categories: [],
      keywords: [],
      features: [],
    };

    styleConfig.value = {
      theme: { id: 'default', name: '默认主题', preview: '' },
      layout: {
        headerStyle: 'standard',
        contentLayout: 'vertical',
        footerStyle: 'simple',
      },
      colors: {
        primary: '#1890ff',
        secondary: '#52c41a',
        background: '#ffffff',
        text: '#333333',
        accent: '#fa8c16',
      },
      fonts: {
        family: 'PingFang SC',
        size: { title: 18, subtitle: 16, body: 14, caption: 12 },
        weight: { title: 600, subtitle: 500, body: 400, caption: 400 },
      },
    };

    // 重置页面状态
    pageState.value = {
      currentStep: ConfigStep.BASIC_INFO,
      isLoading: false,
      isSaving: false,
      isPublishing: false,
      hasUnsavedChanges: false,
      lastSavedTime: '',
    };

    previewState.value = {
      device: PreviewDevice.MOBILE,
      isFullscreen: false,
      isRefreshing: false,
      scale: 1,
    };

    formValidation.value = {
      basicInfo: ValidationStatus.PENDING,
      contact: ValidationStatus.PENDING,
      business: ValidationStatus.PENDING,
      style: ValidationStatus.VALID,
      overall: ValidationStatus.PENDING,
    };

    errors.value = [];
    configId.value = '';
    configVersion.value = 1;
  };

  return {
    // 状态
    basicInfo,
    contactInfo,
    businessInfo,
    styleConfig,
    pageState,
    previewState,
    formValidation,
    errors,
    configId,
    configVersion,

    // 计算属性
    fullConfig,
    canProceedNext,
    canSaveConfig,
    canPublishConfig,
    completionPercentage,

    // 方法
    initializePageData,
    loadConfigData,
    updateBasicInfo,
    updateContactInfo,
    updateBusinessInfo,
    updateStyleConfig,
    changeConfigStep,
    changePreviewDevice,
    toggleFullscreenPreview,
    refreshPreview,
    validateBasicInfo,
    validateContactInfo,
    validateBusinessInfo,
    validateStyleConfig,
    validateAllSteps,
    addError,
    removeError,
    clearErrors,
    saveConfig,
    publishConfig,
    autoSaveConfig,
    resetState,
  };
});
