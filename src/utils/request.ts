import { ReturnCode } from '@/helpers/constants/service';
import {
  request,
  axios,
  RequestErrorInfo,
  type RequestConfig,
  <PERSON><PERSON>,
} from '@tencent/xpage-web-sdk';
import { DialogPlugin } from 'tdesign-vue-next';
import {
  AoErrorCode,
  AoErrorCodeMap,
  knownErrorCode,
} from '@/helpers/types/models/error-code';
const getCsrfToken = () => Cookie.get('BRAND-XSRF-TOKEN');
export const baseUrl = '/xdc/mchcardinfomgrweb';

const BASE_URL = `${location.origin}${baseUrl}/`;
request.updateConfig({
  baseURL: BASE_URL,
  headers: {
    accept: 'application/json',
  },
});

// 请求拦截器 - 动态更新CSRF Token
request.interceptors.request.use(
  (config: RequestConfig) => {
    const reqConfig = { ...config };
    reqConfig.headers = {
      ...reqConfig.headers,
      'X-CSRF-TOKEN': getCsrfToken(),
      'X-XSRF-TOKEN': getCsrfToken(),
    };
    return reqConfig;
  },
  (error) => Promise.reject(error)
);

// 可自定义请求后置拦截器, 比如：统一处理错误码 code | 上报
request.interceptors.response.use(
  (response) => {
    // 没有权限时统一报错
    if (
      response.data?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_MCH_NOT_WHITE_MCH ||
      response.data?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_MCH_NOT_BRAND_MAIN_MCH
    ) {
      logger.error('not in white list', response.data);

      if (globalDialog) {
        return;
      }

      globalDialog = DialogPlugin.confirm({
        header: AoErrorCodeMap[response.data.code as AoErrorCode].title,
        body: AoErrorCodeMap[response.data.code as AoErrorCode].tips,
        confirmBtn: null,
        cancelBtn: '知道了',
        onCancel: () => {
          globalDialog = null;
        },
      });

      const msg =
        response.data?.msg || response.data?.returnMsg || '系统繁忙，请稍后再试';
      throw new RequestErrorInfo(
        msg,
        response.data?.code,
        response.config,
        response.request,
        response.response
      );
    }

    // 其他错误
    if (response.data?.code !== ReturnCode.SUCCESS) {
      console.error(`[😭 Request Fail][${response.config.url}]`, response.data);

      if (response.data?.code && knownErrorCode.includes(response.data?.code)) {
        return response;
      }
      // 302 重定向，比如登录态失效
      if (response.response.redirected) {
        window.location.href = response.response.url;
        return;
      }

      const msg = response.data?.msg || response.data?.message || '系统繁忙，请稍后再试';
      throw new RequestErrorInfo(
        msg,
        response.data?.code,
        response.config,
        response.request,
        response.response
      );
    }

    return response;
  },
  (error) => Promise.reject(error)
);

export { request, axios };
