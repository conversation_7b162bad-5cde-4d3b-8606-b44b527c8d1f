/**
 * Card-Home页面API服务
 */

import type {
  TutorialProcessResponse,
  RouterCheckResult,
} from '@/pages/card-mna/card-home-vue/types/index';

// 使用项目中的request实例
import { request } from '@/utils/request';

export const cardHomeApi = {
  /**
   * 创建新手引导发布流程
   */
  async createTutorialPublishProcess(): Promise<TutorialProcessResponse> {
    try {
      const response = await request.post('/create-tutorial-publish-process');

      if (response.code === 0 && response.data?.processId) {
        return {
          processId: response.data.processId,
        };
      }

      throw new Error('创建新手引导流程失败');
    } catch (error) {
      console.error('创建新手引导流程API调用失败:', error);
      throw error;
    }
  },

  /**
   * 检查路由状态
   */
  async checkRouterStatus(): Promise<RouterCheckResult> {
    try {
      // 1. 检查新手引导发布单状态
      const tutorialResult = await request.get('/get-latest-tutorial-publish-process');

      if (tutorialResult?.code === 0) {
        // 已完成新手引导 -> 跳转到仪表盘
        if (tutorialResult.data?.process?.state === 8) {
          // ProcessState.PUBLISHED
          return {
            code: 3000,
            message: '已完成配置',
            path: '/home/<USER>/dashboard',
          };
        }
        // 有未完成的新手单 -> 跳转到详情页继续配置
        return {
          code: 2000,
          message: '配置进行中',
          path: '/home/<USER>/card-detail',
        };
      }

      // 2. 检查基础信息和服务配置状态
      const [cardInfo, cardService] = await Promise.all([
        request.get('/get-card-info'),
        request.get('/get-service-config'),
      ]);

      // 都已配置 -> 跳转到仪表盘
      if (cardInfo.code === 0 && cardService.code === 0) {
        return {
          code: 3000,
          message: '配置已完成',
          path: '/home/<USER>/dashboard',
        };
      }

      // 新用户 -> 停留在首页
      return {
        code: 1000,
        message: '新用户',
        path: '/home/<USER>/card-home',
      };
    } catch (error) {
      console.error('检查路由状态失败:', error);
      // 出错时默认停留在首页
      return {
        code: 1000,
        message: '检查状态失败，默认显示首页',
        path: '/home/<USER>/card-home',
      };
    }
  },
};

// 导出单个API方法，方便按需导入
export const {
  getCardInfo,
  createCard,
  createTutorialPublishProcess,
  checkRouterStatus,
} = cardHomeApi;
