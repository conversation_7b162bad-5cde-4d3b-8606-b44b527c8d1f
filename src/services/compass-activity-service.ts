import {
  TV1GetCompassActivityDetailOverviewResponseParams,
  TV1GetCompassActivityOverviewResponseParams,
  TV1GetCompassActivityPersonaDetailRequestParams,
  TV1GetCompassActivityPersonaDetailResponseParams,
} from '@/helpers/types/models';
import { request } from './request';
import Logger from '@/helpers/utils/logger';

const logger = new Logger('compass-activity-service');

export const compassActivityService = {
  /**
   * 获取活动罗盘概览
   */
  async getCompassActivityOverview(
    getDateBegin: string,
    getDateEnd: string,
    activityType: number,
  ): Promise<TV1GetCompassActivityOverviewResponseParams['data']> {
    let res: TV1GetCompassActivityOverviewResponseParams | null;
    try {
      res = await request.get('/v1/compass-activity/overview', {
        get_date_begin: getDateBegin,
        get_date_end: getDateEnd,
        activity_type: activityType,
      });
    } catch (e) {
      logger.error('getCompassActivityOverview error', e);
      throw e;
    }

    logger.log('getCompassActivityOverview', res);
    return res!.data!;
  },

  /**
   * 获取活动罗盘概览
   */
  async getCompassActivityDetailOverview(
    activityId: string,
    getDateBegin: string,
    getDateEnd: string,
    activityType: number,
  ): Promise<TV1GetCompassActivityDetailOverviewResponseParams['data']> {
    let res: TV1GetCompassActivityDetailOverviewResponseParams | null;
    try {
      res = await request.get('/v1/compass-activity-detail/overview', {
        activity_id: activityId,
        get_date_begin: getDateBegin,
        get_date_end: getDateEnd,
        activity_type: activityType,
      });
    } catch (e) {
      logger.error('getCompassActivityDetailOverview error', e);
      throw e;
    }

    logger.log('getCompassActivityDetailOverview', res);
    return res!.data!;
  },

  /**
   * 获取活动罗盘 - 用户画像详情
   */
  async getPersonaDetail(
    req: TV1GetCompassActivityPersonaDetailRequestParams['query'],
  ): Promise<TV1GetCompassActivityPersonaDetailResponseParams['data']> {
    logger.log('getUserPersonaDetail invoke', req);
    const res = await request.get('/v1/compass-activity/persona-detail', req);
    logger.log('getUserPersonaDetail success', res);
    return res.data;
  },
};
