import {
  BrandManagerActivity,
  TV1GetActivityByIdResponseParams,
  TV1GetActivityRequestParams,
  TV1GetActivityResponseParams,
} from '@/helpers/types/models';
import { request } from './request';
import Logger from '@/helpers/utils/logger';
import { ActivityStatusMap, ActivityStatus } from '@/helpers/constants/model';
import { dayjs } from '@tencent/xpage-web-sdk';

const logger = new Logger('business-overview-service');

/**
 * 活动信息的扩展类型
 * 用于在页面中展示
 */
export type BrandManagerActivityExtend = BrandManagerActivity & {
  /** 活动状态文案 */
  displayActivityState: string;
  /** 活动时间段 */
  displayActivityTime: string;
  /** 创建时间 */
  displayCreateTime: string;
  /** 商家券 */
  displayStocks: string[];
  /** 投放人群 */
  displayLabel: string[];
};

export const activityService = {
  /**
   * 获取活动列表
   */
  async getAcitvityList(
    offset: number,
    limit: number,
    act_name: string | undefined,
    act_status: number | undefined,
  ): Promise<TV1GetActivityResponseParams['data']> {
    const req: TV1GetActivityRequestParams['query'] = {
      offset: String(offset),
      limit: String(limit),
    };
    if (act_name) {
      req.act_name = act_name;
    }
    if (act_status) {
      req.act_status = String(act_status);
    }

    let res: TV1GetActivityResponseParams | null;
    try {
      res = await request.get('/v1/activity', req);
    } catch (e) {
      logger.error('getAcitvityList error', e);
      throw e;
    }

    logger.log('getAcitvityList', res);
    return res!.data!;
  },

  /**
   * 获取活动详情 - 根据概览类型获取
   */
  async getActivityDetail(activity_id: number): Promise<BrandManagerActivity> {
    let res: TV1GetActivityByIdResponseParams | null;
    try {
      res = await request.get(`/v1/activity/${activity_id}`);
    } catch (e) {
      logger.error('getActivityDetail error', e);
      throw e;
    }

    logger.log('getActivityDetail', res);
    return res!.data!.activity_info!;
  },

  /**
   * 对后台返回的活动信息进行格式化
   */
  formatActivityItem(x: BrandManagerActivity): BrandManagerActivityExtend {
    return {
      ...x,
      displayActivityState: ActivityStatusMap[x.act_status as ActivityStatus].label,
      displayActivityTime: `${dayjs.unix(x.act_delivery_rule.delivery_start_time).format('YYYY/MM/DD')}-${dayjs.unix(x.act_delivery_rule.delivery_end_time).format('YYYY/MM/DD')}`,
      displayCreateTime: dayjs.unix(x.create_time).format('YYYY/MM/DD HH:mm:ss'),
      displayStocks: x.stock_list.map((x) => x.stock_name),
      displayLabel: x.act_delivery_rule.activity_label.map((x) => x.map((y) => y.label_display).join('；')),
    };
  },
};
