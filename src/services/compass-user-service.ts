import {
  TV1GetCompassUserPersonaDetailRequestParams,
  TV1GetCompassUserInsightResponseParams,
  TV1GetCompassUserPersonaDetailResponseParams,
  TV1GetCompassUserTrendResponseParams,
} from '@/helpers/types/models';
import { request } from './request';
import Logger from '@/helpers/utils/logger';

const logger = new Logger('compassUserService');

export const compassUserService = {
  /**
   * 获取罗盘用户洞察
   * @param getDate 获取日期 YYYY-MM-DD格式
   */
  async getUserInsight(getDate: string): Promise<TV1GetCompassUserInsightResponseParams['data']> {
    logger.log('getUserInsight invoke', getDate);

    let res: TV1GetCompassUserInsightResponseParams | null;
    try {
      res = await request.get('/v1/compass-user/insight', {
        get_date: getDate,
      });
    } catch (e) {
      logger.error('getUserInsight fail', e);
      throw e;
    }

    logger.log('getUserInsight success', res);
    return res!.data;
  },

  /**
   * 获取罗盘用户洞察
   * @param getDate 获取日期 YYYY-MM-DD格式
   */
  async getUserTrend(
    getDateBegin: string,
    getDateEnd: string,
    activeRuleVersion: number,
    activeBrandVersion: number,
  ): Promise<TV1GetCompassUserTrendResponseParams['data']> {
    logger.log('getUserInsight invoke', getDateBegin, getDateEnd, activeRuleVersion, activeBrandVersion);

    let res: TV1GetCompassUserTrendResponseParams | null;
    try {
      res = await request.get('/v1/compass-user/trend', {
        get_date_begin: getDateBegin,
        get_date_end: getDateEnd,
        active_rule_version: activeRuleVersion,
        active_brand_version: activeBrandVersion,
      });
    } catch (e) {
      logger.error('getUserInsight fail', e);
      throw e;
    }

    logger.log('getUserInsight success', res);
    return res!.data;
  },

  async getUserPersonaDetail(
    req: TV1GetCompassUserPersonaDetailRequestParams['query'],
  ): Promise<TV1GetCompassUserPersonaDetailResponseParams['data']> {
    logger.log('getUserPersonaDetail invoke', req);

    let res: TV1GetCompassUserPersonaDetailResponseParams | null;
    try {
      res = await request.get('/v1/compass-user/persona-detail', req);
    } catch (e) {
      logger.error('getUserPersonaDetail fail', e);
      throw e;
    }

    logger.log('getUserPersonaDetail success', res);
    return res!.data;
  },
};
