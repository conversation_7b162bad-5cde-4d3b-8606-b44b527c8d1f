/**
 * Card-Detail页面API服务
 * 配置进度跟踪页面的简化API服务
 */

import type {
  ApiResponse,
  ConfigStatusResponse,
  PreviewData,
} from '@/pages/card-mna/card-detail-vue/types/index';

// 这里假设项目中已有request实例，需要根据实际项目调整
// import { request } from '@/utils/request';

// 临时mock实现，实际开发时需要替换为真实的request调用
const mockRequest = {
  get: async (url: string): Promise<ApiResponse<any>> => {
    console.log(`Mock GET request to: ${url}`);

    // 模拟配置状态API
    if (url.includes('/card-detail/status/')) {
      return {
        code: 0,
        message: 'success',
        data: {
          basicInfo: {
            completed: true,
            lastUpdate: '2023-04-01',
          },
          serviceInfo: {
            completed: false,
            lastUpdate: undefined,
          },
          brandInfo: {
            completed: false,
            lastUpdate: undefined,
          },
          canPublish: false,
        },
      };
    }

    // 模拟预览数据API
    if (url.includes('/card-detail/preview/')) {
      return {
        code: 0,
        message: 'success',
        data: {
          cardId: 'card123456',
          cardName: '微信支付商家名片',
          status: 'active',
          merchantName: '示例商户',
          logo: '',
          businessHours: '每日 9:00-18:00',
          phones: ['************'],
        },
      };
    }

    // 默认响应
    return {
      code: 0,
      message: 'success',
      data: null,
    };
  },

  post: async (url: string, data?: any): Promise<ApiResponse<any>> => {
    console.log(`Mock POST request to: ${url}`, data);

    return {
      code: 0,
      message: 'success',
      data: { success: true },
    };
  },
};

/**
 * Card-Detail API服务类
 */
class CardDetailApiService {
  /**
   * 获取配置状态
   */
  async getConfigStatus(
    processId?: string | null
  ): Promise<ApiResponse<ConfigStatusResponse>> {
    const url = processId
      ? `/api/card-detail/status/${processId}`
      : '/api/card-detail/status';

    return await mockRequest.get(url);
  }

  /**
   * 获取预览数据
   */
  async getPreviewData(processId?: string | null): Promise<ApiResponse<PreviewData>> {
    const url = processId
      ? `/api/card-detail/preview/${processId}`
      : '/api/card-detail/preview';

    return await mockRequest.get(url);
  }

  /**
   * 刷新配置状态
   */
  async refreshStatus(
    processId?: string | null
  ): Promise<ApiResponse<ConfigStatusResponse>> {
    return await this.getConfigStatus(processId);
  }

  /**
   * 刷新预览数据
   */
  async refreshPreview(processId?: string | null): Promise<ApiResponse<PreviewData>> {
    return await this.getPreviewData(processId);
  }

  /**
   * 获取最新的卡片信息发布单
   */
  async getLatestCardInfoPublishProcess(): Promise<ApiResponse<any>> {
    return await mockRequest.get('/api/card-info/latest-publish-process');
  }

  /**
   * 获取最新的服务发布单
   */
  async getLatestServicePublishProcess(): Promise<ApiResponse<any>> {
    return await mockRequest.get('/api/service/latest-publish-process');
  }

  /**
   * 获取品牌会员权限状态
   */
  async getBrandMemberAccess(): Promise<ApiResponse<any>> {
    return await mockRequest.get('/api/brand-member/access-state');
  }

  /**
   * 获取摇一摇优惠权限状态
   */
  async getShakeCouponAccess(): Promise<ApiResponse<any>> {
    return await mockRequest.get('/api/shake-coupon/access-state');
  }
}

// 导出API服务实例
export const cardDetailApi = new CardDetailApiService();

// 导出API服务类（用于测试或其他需求）
export { CardDetailApiService };
