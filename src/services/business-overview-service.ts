import {
  BusinessOverview,
  TV1GetCompassBusinessOverviewByBusinessTypeResponseParams,
  TV1GetCompassBusinessOverviewResponseParams,
} from '@/helpers/types/models';
import { request } from './request';
import Logger from '@/helpers/utils/logger';

const logger = new Logger('business-overview-service');

export const businessOverviewService = {
  /**
   * 获取经营概览
   */
  async getBusinessOverview(
    get_date_begin: string,
    get_date_end: string,
  ): Promise<TV1GetCompassBusinessOverviewResponseParams['data']> {
    let res: TV1GetCompassBusinessOverviewResponseParams | null;
    try {
      res = await request.get('/v1/compass-business/overview', {
        get_date_begin,
        get_date_end,
      });
    } catch (e) {
      logger.error('getBusinessOverview error', e);
      throw e;
    }

    logger.log('getBusinessOverview success', res);
    return res!.data!;
  },

  /**
   * 获取经营概览 - 根据概览类型获取
   */
  async getBusinessOverviewByBusinessType(
    business_type: number,
    get_date_begin: string,
    get_date_end: string,
    active_brand_version: number,
    active_begin_time: string,
  ): Promise<BusinessOverview> {
    let res: TV1GetCompassBusinessOverviewByBusinessTypeResponseParams | null;
    try {
      res = await request.get(`/v1/compass-business/overview/${business_type}`, {
        get_date_begin,
        get_date_end,
        active_brand_version,
        active_begin_time,
      });
    } catch (e) {
      logger.error('getBusinessOverviewOfTrade error', e);
      throw e;
    }

    logger.log('getBusinessOverviewOfTrade success', res);
    return res!.data!.business_data;
  },
};
