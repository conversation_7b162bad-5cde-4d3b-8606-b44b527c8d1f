/**
 * CardHome API服务测试
 * 测试API调用逻辑和错误处理
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';

// 导入模拟后的模块
import { cardHomeApi } from '../cardHome';

// 直接模拟整个模块，避免导入真实模块
vi.mock('../cardHome', () => ({
  cardHomeApi: {
    getCardInfo: vi.fn(),
    createCard: vi.fn(),
    createTutorialPublishProcess: vi.fn(),
    checkRouterStatus: vi.fn(),
  },
}));

describe('CardHome API服务', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('getCardInfo', () => {
    it('应该成功获取卡片信息', async () => {
      const mockResponse = {
        isConfigured: true,
        cardType: 'business',
        lastUpdated: '2024-01-01T00:00:00.000Z',
      };

      vi.mocked(cardHomeApi.getCardInfo).mockResolvedValue(mockResponse);

      const result = await cardHomeApi.getCardInfo();

      expect(result).toEqual(mockResponse);
      expect(cardHomeApi.getCardInfo).toHaveBeenCalledTimes(1);
    });

    it('应该处理获取卡片信息失败的情况', async () => {
      const errorMessage = '获取卡片信息失败';
      vi.mocked(cardHomeApi.getCardInfo).mockRejectedValue(new Error(errorMessage));

      await expect(cardHomeApi.getCardInfo()).rejects.toThrow(errorMessage);
    });
  });

  describe('createTutorialPublishProcess', () => {
    it('应该成功创建新手引导流程', async () => {
      const mockResponse = {
        processId: 'test-process-123',
      };

      vi.mocked(cardHomeApi.createTutorialPublishProcess).mockResolvedValue(mockResponse);

      const result = await cardHomeApi.createTutorialPublishProcess();

      expect(result).toEqual(mockResponse);
      expect(result.processId).toBe('test-process-123');
    });

    it('应该处理创建流程失败的情况', async () => {
      const errorMessage = '创建新手引导流程失败';
      vi.mocked(cardHomeApi.createTutorialPublishProcess).mockRejectedValue(new Error(errorMessage));

      await expect(cardHomeApi.createTutorialPublishProcess()).rejects.toThrow(errorMessage);
    });
  });

  describe('checkRouterStatus', () => {
    it('应该返回新用户状态', async () => {
      const mockResponse = {
        code: 1000,
        message: '新用户',
        path: '/home/<USER>/card-home',
      };

      vi.mocked(cardHomeApi.checkRouterStatus).mockResolvedValue(mockResponse);

      const result = await cardHomeApi.checkRouterStatus();

      expect(result.code).toBe(1000);
      expect(result.path).toBe('/home/<USER>/card-home');
    });

    it('应该返回配置进行中状态', async () => {
      const mockResponse = {
        code: 2000,
        message: '配置进行中',
        path: '/home/<USER>/card-detail',
      };

      vi.mocked(cardHomeApi.checkRouterStatus).mockResolvedValue(mockResponse);

      const result = await cardHomeApi.checkRouterStatus();

      expect(result.code).toBe(2000);
      expect(result.path).toBe('/home/<USER>/card-detail');
    });

    it('应该返回已完成配置状态', async () => {
      const mockResponse = {
        code: 3000,
        message: '已完成配置',
        path: '/home/<USER>/dashboard',
      };

      vi.mocked(cardHomeApi.checkRouterStatus).mockResolvedValue(mockResponse);

      const result = await cardHomeApi.checkRouterStatus();

      expect(result.code).toBe(3000);
      expect(result.path).toBe('/home/<USER>/dashboard');
    });
  });

  describe('createCard', () => {
    it('应该成功创建卡片', async () => {
      const cardData = {
        name: '测试商户',
        type: 'business',
      };
      const mockResponse = {
        id: 'card-123',
        ...cardData,
      };

      vi.mocked(cardHomeApi.createCard).mockResolvedValue(mockResponse);

      const result = await cardHomeApi.createCard(cardData);

      expect(result).toEqual(mockResponse);
      expect(cardHomeApi.createCard).toHaveBeenCalledWith(cardData);
    });

    it('应该处理创建卡片失败的情况', async () => {
      const cardData = { name: '测试商户' };
      const errorMessage = '创建卡片失败';

      vi.mocked(cardHomeApi.createCard).mockRejectedValue(new Error(errorMessage));

      await expect(cardHomeApi.createCard(cardData)).rejects.toThrow(errorMessage);
    });
  });
});
