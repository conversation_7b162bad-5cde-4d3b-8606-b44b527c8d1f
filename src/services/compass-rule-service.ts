import { CompassRule, TV1GetCompassRuleResponseParams, TV1PutCompassRuleResponseParams } from '@/helpers/types/models';
import { request } from './request';
import Logger from '@/helpers/utils/logger';

const logger = new Logger('compassRuleService');

export const compassRuleService = {
  /**
   * 获取罗盘规则
   */
  async getCompassRule(): Promise<CompassRule> {
    logger.log('getCompassRule invoke');

    let res: TV1GetCompassRuleResponseParams | null;
    try {
      res = await request.get('/v1/compass-rule');
    } catch (e) {
      logger.error('getCompassRule fail', e);
      throw e;
    }

    logger.log('getCompassRule success', res);
    return res!.data!.compass_rule;
  },

  /**
   * 更新罗盘规则
   */
  async setCompassRule(compassRule: CompassRule): Promise<CompassRule> {
    logger.log('setCompassRule invoke', compassRule);

    let res: TV1PutCompassRuleResponseParams | null;
    try {
      res = await request.put('/v1/compass-rule', {
        compass_rule: compassRule,
      });
    } catch (e) {
      logger.error('setCompassRule fail', e);
      throw e;
    }

    logger.log('setCompassRule success', res);
    return res!.data!.compass_rule;
  },
};
