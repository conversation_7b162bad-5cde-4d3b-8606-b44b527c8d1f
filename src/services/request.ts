import { ReturnCode } from '@/helpers/constants/service';
import { request, RequestErrorInfo, type RequestConfig } from '@tencent/xpage-web-sdk';
import { DialogInstance, DialogPlugin } from 'tdesign-vue-next';
import { AoErrorCode, AoErrorCodeMap } from '@/helpers/types/models/error-code';
import Logger from '@/helpers/utils/logger';

const logger = new Logger('request');
const BASE_URL = `${location.origin}/`;

request.updateConfig({
  baseURL: BASE_URL,
});

let globalDialog: DialogInstance | null;

// 请求前置拦截器，比如设置统一header等
request.interceptors.request.use(
  (config: RequestConfig) => config,
  (error) => Promise.reject(error)
);

// 可自定义请求后置拦截器, 比如：统一处理错误码 code | 上报
request.interceptors.response.use(
  (response) => {
    // 没有权限时统一报错
    if (
      response.data?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_MCH_NOT_WHITE_MCH ||
      response.data?.code ===
        AoErrorCode.ERR_BRAND_MANAGEMENT_MARKETING_DATA_MCH_NOT_BRAND_MAIN_MCH
    ) {
      logger.error('not in white list', response.data);

      if (globalDialog) {
        return;
      }

      globalDialog = DialogPlugin.confirm({
        header: AoErrorCodeMap[response.data.code as AoErrorCode].title,
        body: AoErrorCodeMap[response.data.code as AoErrorCode].tips,
        confirmBtn: null,
        cancelBtn: '知道了',
        onCancel: () => {
          globalDialog = null;
        },
      });

      const msg =
        response.data?.msg || response.data?.returnMsg || '系统繁忙，请稍后再试';
      throw new RequestErrorInfo(
        msg,
        response.data?.code,
        response.config,
        response.request,
        response.response
      );
    }

    // 其他错误
    if (response.data?.code !== ReturnCode.SUCCESS) {
      console.error(`[😭 Request Fail][${response.config.url}]`, response.data);

      // 302 重定向，比如登录态失效
      if (response.response.redirected) {
        window.location.href = response.response.url;
        return;
      }

      const msg = response.data?.msg || response.data?.message || '系统繁忙，请稍后再试';
      throw new RequestErrorInfo(
        msg,
        response.data?.code,
        response.config,
        response.request,
        response.response
      );
    }

    return response;
  },
  (error) => Promise.reject(error)
);

export { request };
