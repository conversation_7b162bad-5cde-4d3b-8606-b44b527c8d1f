/**
 * Shared enums
 */
export enum CustomerServiceType {
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_NONE,
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM,
  M<PERSON>AY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM,
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE,
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_SERVICE_PHONE,
}

export enum SceneType {
  MERCHANT_CARD_SCENE_TYPE_NONE,
  MERCHANT_CARD_SCENE_TYPE_MP,
  MERCHANT_CARD_SCENE_TYPE_FINDER,
}

/**
 * Base entity interfaces
 */
export interface IEntityState {
  /**
   * Unique identifier for the entity instance
   */
  id?: string | number;
  /**
   * Timestamp when the entity was created
   */
  createdAt?: number;
  /**
   * Timestamp when the entity was last updated
   */
  updatedAt?: number;
}

export interface IEntityForm {
  /**
   * Whether the form data is valid
   */
  isValid?: boolean;
  /**
   * Form validation errors
   */
  errors?: Record<string, string>;
}

/**
 * Tutorial related types
 */
export interface TutorialState extends IEntityState {
  canTutorialPublish: boolean;
  canConfigCardInfo: boolean;
  canConfigCardService: boolean;
  cardInfoStatusText: string;
  canBrandAccess: boolean;
  cardServiceStatusText: string;
  brandAccessStatusText: string;
}

export type TutorialConfigStatus = '已接入' | '已配置' | '未配置';

export interface TutorialApiResult {
  tutorialPublish: unknown;
  cardInfo: unknown;
  serviceInfo: unknown;
  exposeConfig: unknown;
  brandMemberAccess: unknown;
  shakeCouponAccess: unknown;
}

/**
 * CardInfo related types
 */
export interface CardInfoFormData extends IEntityForm {
  // cardBrief?: unknown; 
  customerService?: {
    miniProgram?: Record<string, unknown>;
    wecom?: Record<string, unknown>;
    customize?: Record<string, unknown>;
    servicePhone?: string;
  };
  cardConfigSence?: {
    miniProgram?: Record<string, unknown>;
    finder?: Record<string, unknown>;
  };
}

export interface CardInfoState extends IEntityState {
  customerService?: {
    customerServiceType: CustomerServiceType;
    miniProgram?: Record<string, unknown>;
    wecom?: Record<string, unknown>;
    customize?: Record<string, unknown>;
    servicePhone?: string;
    onlineChecked?: boolean;
    customerTelChecked?: boolean;
  }[];
  sceneList?: {
    sceneType: SceneType;
    miniProgram?: Record<string, unknown>;
    finder?: Record<string, unknown>;
  }[];
  sceneConfig?: {
    miniProgram?: Record<string, unknown>;
    finder?: Record<string, unknown>;
    sceneMiniProgramChecked?: boolean;
    sceneFinderChecked?: boolean;
  };
}
