import type { IEntityState, IEntityForm } from './type';
/**
 * Base abstract class for all business entities
 */
export abstract class BusinessEntity<
  TState extends IEntityState = IEntityState,
  TForm extends IEntityForm = IEntityForm,
> {
  // Protected members
  protected state: TState;
  protected formData: TForm;

  // Abstract property
  abstract readonly entityId: string;

  constructor(initialState?: Partial<TState>, initialFormData?: Partial<TForm>) {
    this.state = initialState as TState;
    this.formData = initialFormData as TForm;
  }

  // Rest of the implementation...
}

/**
 * Interface for registrable entities
 */
export interface RegistrableEntity {
  register(): void;
}
