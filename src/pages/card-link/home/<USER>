<template>
  <div class="card-link-home">
    <div class="header">
      <h2 class="title">交易场景</h2>
      <button class="add-btn" @click="handleAddLinkScene">添加</button>
    </div>
    <div class="description">
      添加交易场景，用户支付成功后，点击微信支付公众号向用户下发的支付凭证，将跳转商家名片
    </div>
    <div v-if="shouldShowLinkedScene" class="grid-view">
      <LinkedSceneCardView
        v-for="(card, index) in cardList"
        :key="index"
        :title="card.title"
        :info="card.info"
        :desc="card.desc"
        @click="card.click"
      />
    </div>
    <div v-else class="card-link-example-block">
      <div class="card-link-example">
        <img
          src="@/assets/images/cardlink/card-link-home-example.png"
          class="card-link-example-image"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCardLinkHome } from './card-link-home';
import LinkedSceneCardView from '../components';
import './card-link-home.css';

interface LinkedSceneCardInfo {
  title: string;
  info: string;
  desc: string;
  click: () => void;
}

const {
  handleAddLinkScene,
  shouldShowLinkedScene,
  handleViewMiniAppScene,
  handleViewPayCodeScene,
  handleViewPayScoreScene,
  handleViewAppScene,
} = useCardLinkHome();

const cardList: LinkedSceneCardInfo[] = [
  {
    title: '小程序支付',
    info: `已配置 2 个交易场景`,
    desc: '用户在品牌的小程序中选择商品下单，跳转微信支付输入密码界面，完成支付',
    click: handleViewMiniAppScene,
  },
  {
    title: 'App支付',
    info: `已配置 3 个交易场景`,
    desc: '用户在品牌的APP中选择商品下单，跳转到微信的支付确认验密界面，完成支付',
    click: handleViewAppScene,
  },
  {
    title: '付款码支付',
    info: `已配置 100 个交易场景`,
    desc: '用户线下展示付款码，商户收银员用扫码设备扫描用户的付款码，用户确认支付',
    click: handleViewPayCodeScene,
  },
  {
    title: '支付分',
    info: `已配置 1 个交易场景`,
    desc: '微信支付分提供先使用服务，再付款的能力，如免押金借用充电宝、电商先用后付等',
    click: handleViewPayScoreScene,
  },
];
</script>
