// src/pages/card-link/home/<USER>
import { ref } from 'vue';
import { useRouter } from 'vue-router';

export const useCardLinkHome = () => {
  const router = useRouter();
  const shouldShowLinkedScene = ref(false);

  const handleAddLinkScene = () => {
    console.log('添加交易场景');
    router.push('/card-link/scene-add-guide');
  };

  const handleViewMiniAppScene = () => {
    console.log('查看小程序支付场景');
  };

  const handleViewAppScene = () => {
    console.log('查看App支付场景');
  };

  const handleViewPayScoreScene = () => {
    console.log('查看支付分支付场景');
  };

  const handleViewPayCodeScene = () => {
    console.log('查看付款码支付场景');
  };

  return {
    handleAddLinkScene,
    shouldShowLinkedScene,
    handleViewMiniAppScene,
    handleViewAppScene,
    handleViewPayScoreScene,
    handleViewPayCodeScene,
  };
};
