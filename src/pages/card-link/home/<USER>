/* src/pages/card-link/home/<USER>/
.card-link-home {
    background-color: white;
    width: 1224px;
    min-height: auto;
    opacity: 1;
    gap: 24px;
    border-radius: 16px;
    border: 1px solid rgba(0, 0, 0, 0.04);
    padding: 32px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.title {
    font-size: 18px;
    font-weight: bold;
    margin: 0;
}

.add-btn {
    width: 112px;
    height: 40px;
    opacity: 1;
    gap: 10px;
    border-radius: 8px;
    padding: 9px 24px;
    background: #07C160;
    color: white;
    border: none;
    cursor: pointer;
}

.description {
    color: #666;
    font-size: 14px;
    margin-bottom: 24px;
}

.view-btn {
    background-color: #f2f2f2;
    border: none;
    cursor: pointer;
    width: 112px;
    height: 40px;
    opacity: 1;
    gap: 10px;
    border-radius: 8px;
    padding: 9px 24px;
    background-color: rgba(0, 0, 0, 0.04);
}

.scene-card {
    width: 568px;
    height: 158px;
    opacity: 1;
    gap: 10px;
    border-radius: 8px;
    padding: 24px;
    background-color: #f7f7f7;
    position: relative;
    margin-bottom: 16px;
    display: flex;
    flex-direction: column;
}

.scene-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.scene-title {
    font-weight: 500;
    font-style: normal;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0px;
    color: rgba(0, 0, 0, 0.9);
    margin: 0;
}

.scene-info {
    font-weight: 400;
    font-size: 16px;
    line-height: 100%;
    letter-spacing: 0px;
    color: rgba(0, 0, 0, 0.3);
    margin: 0;
}

.scene-desc {
    font-weight: 400;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0px;
    color: rgba(0, 0, 0, 0.5);
    margin-bottom: 16px;
}

/* 网格视图样式 */
.grid-view {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    margin-top: 16px;
}

/* 示例区块样式 */
.card-link-example-block {
    width: 1160px;
    height: 522px;
    gap: 10px;
    opacity: 1;
    border-radius: 8px;
    padding: 24px;
    background: rgba(0, 0, 0, 0.02);
    margin-top: 24px;
    display: flex;
    flex-direction: column;
}

.card-link-example {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
}

.card-link-example-image {
    max-width: 880px;
    max-height: 474px;
    width: auto;
    height: auto;
    object-fit: contain;
    gap: 24px;
    angle: 0deg;
    opacity: 1;

}
