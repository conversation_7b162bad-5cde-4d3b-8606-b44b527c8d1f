<template>
  <div class="search-container">
    <div class="service-id-label">
      {{ label }}
    </div>
    <div class="input-wrapper" :style="{ width: labelWidth }">
      <input
        v-model="inputValue"
        type="text"
        class="service-id-input"
        :placeholder="placeholder"
        @keyup.enter="handleSearch"
      />
      <button class="search-btn" @click="handleSearch">
        <img
          v-if="inputValue.trim()"
          :src="searchClearIconUrl"
          alt="清除"
          class="search-clear-icon"
          @click.stop="handleClear"
        />
        <img
          v-if="inputValue.trim()"
          :src="searchSeparatorIconUrl"
          alt="分隔符"
          class="search-separator-icon"
        />
        <img :src="searchIconUrl" alt="搜索" class="search-main-icon" />
      </button>
    </div>
    <button v-if="showClearBtn" class="clear-btn" @click="handleClear">删除</button>
  </div>

  <div v-if="props.errMsg" class="error-status">
    <span>{{ props.errMsg }}</span>
  </div>
  <div v-else-if="isSearching" class="searching-status">
    <div class="loading-icon">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
        <circle
          cx="8"
          cy="8"
          r="6"
          stroke="currentColor"
          stroke-width="2"
          fill="none"
          opacity="0.3"
        />
        <path
          d="M14 8a6 6 0 0 1-6 6"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
        />
      </svg>
    </div>
    <span>搜索中</span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import searchIconUrl from '@/assets/images/cardlink/search-icon.svg?url';
import searchClearIconUrl from '@/assets/images/cardlink/search-clear-icon.svg?url';
import searchSeparatorIconUrl from '@/assets/images/cardlink/search-separator-icon.svg?url';

export interface CardLinkSearchViewProps {
  /** 标签文案 */
  label?: string;
  /** 输入框占位符文案 */
  placeholder?: string;
  /** 搜索框宽度 */
  labelWidth?: string;
  /** 输入值 */
  modelValue?: string;
  /** 是否正在搜索 */
  searching?: boolean;
  /** 错误信息 */
  errMsg?: string;
  /** 是否显示清除按钮 */
  showClearBtn?: boolean;
}

export interface CardLinkSearchViewEmits {
  /** 输入值变化或搜索事件 */
  (e: 'update:modelValue' | 'search', value: string): void;
  /** 清除事件 */
  (e: 'clear'): void;
}

const props = withDefaults(defineProps<CardLinkSearchViewProps>(), {
  label: '服务ID',
  placeholder: '输入服务ID，回车或点击按钮搜索',
  labelWidth: '628px',
  modelValue: '',
  searching: false,
  errMsg: '',
  showClearBtn: false,
});

const emit = defineEmits<CardLinkSearchViewEmits>();

const inputValue = computed({
  get: () => props.modelValue,
  set: (value: string) => emit('update:modelValue', value),
});

const isSearching = ref(props.searching);

// 监听外部传入的 searching 状态
watch(
  () => props.searching,
  (newVal) => {
    isSearching.value = newVal;
  }
);

const handleSearch = () => {
  if (!inputValue.value.trim()) return;
  emit('search', inputValue.value.trim());
};

const handleClear = () => {
  inputValue.value = '';
  isSearching.value = false;
  emit('clear');
};
</script>

<style scoped lang="less">
.search-container {
  display: flex;
  align-items: center;
}

.service-id-label {
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  white-space: nowrap;
  width: 160px;
  height: 22px;
}

.input-wrapper {
  position: relative;
  height: 40px;
  margin-left: 16px;
}

.service-id-input {
  width: 100%;
  height: 40px;
  padding: 9px 16px;
  border: none;
  border-radius: 8px;
  background: rgba(0, 0, 0, 0.04);
  font-size: 16px;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.9);
  opacity: 1;
  box-sizing: border-box;

  &::placeholder {
    color: rgba(0, 0, 0, 0.4);
    font-size: 16px;
    font-weight: 400;
  }

  &:focus {
    outline: none;
    background: rgba(0, 0, 0, 0.06);
  }

  &:hover {
    background: rgba(0, 0, 0, 0.06);
  }
}

.search-btn {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  height: 16px;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 0;

  .search-clear-icon,
  .search-separator-icon,
  .search-main-icon {
    width: 16px;
    height: 16px;
    opacity: 0.5;
    flex-shrink: 0;
  }

  .search-clear-icon {
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }

  .search-separator-icon {
    // 分隔符不处理hover
  }

  .search-main-icon {
    cursor: pointer;

    &:hover {
      opacity: 0.7;
    }
  }
}

.clear-btn {
  height: 40px;
  padding: 9px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.16);
  cursor: pointer;
  box-sizing: border-box;
}

.searching-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 24px 0;
  color: rgba(0, 0, 0, 0.5);
  font-size: 16px;
  font-weight: 400;
  margin-left: 176px;
}

.error-status {
  display: flex;
  align-items: center;
  padding-top: 12px;
  margin-left: 176px;
  height: 20px;

  span {
    font-weight: 400;
    font-size: 14px;
    line-height: 100%;
    letter-spacing: 0px;
    color: #f14752;
  }
}

.loading-icon {
  animation: spin 1s linear infinite;

  svg {
    display: block;
    width: 16px;
    height: 16px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
