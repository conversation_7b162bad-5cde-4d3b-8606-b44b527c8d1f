import { App } from 'vue';
import ActionBar from '../../../components/layouts/action-bar';
import PageHeader from '../../../components/layouts/page-header';
import SearchView from './search-view';
import { PayScoreMerchantCard } from './pay-score-merchant-card';

export { ActionBar, PageHeader, SearchView, PayScoreMerchantCard };
export { LinkState, ErrTipsType } from './pay-score-merchant-card';

export type {
  ActionBarProps,
  ActionBarEmits,
  ActionItem,
  ComponentSize,
} from '../../../components/layouts/action-bar';

export type {
  PageHeaderProps,
  PageHeaderEmits,
} from '../../../components/layouts/page-header';

export type { CardLinkSearchViewProps, CardLinkSearchViewEmits } from './search-view';

export default {
  install(app: App, options: { prefix?: string } = {}) {
    const { prefix = 'CardLink' } = options;

    app.component(`${prefix}ActionBar`, ActionBar);
    app.component(`${prefix}PageHeader`, PageHeader);
    app.component(`${prefix}SearchView`, SearchView);
    app.component(`${prefix}PayScoreMerchantCard`, PayScoreMerchantCard);
  },
};
