<template>
  <main-page-container
    :title="sceneConfig.title"
    :show-add-button="showAddButton"
    @add="goAddScene"
  >
    <template #description>
      <div class="scene-description">
        <span class="scene-description-text">{{ sceneConfig.description }}</span>
        <span class="scene-description-link" @click="showDemo">查看图示</span>
      </div>
    </template>

    <template #content>
      <slot name="content" />
    </template>

    <template v-if="showActions" #actions-status>
      <slot name="actions-status">
        <span class="status-text">
          {{ sceneConfig.statusText.replace('{count}', selectedCount.toString()) }}
        </span>
      </slot>
    </template>

    <template v-if="showActions" #actions-buttons>
      <slot name="actions-buttons">
        <div class="cark-link-actions">
          <t-button
            :disabled="isSubmitDisable"
            :theme="isSubmitDisable ? 'default' : 'primary'"
            variant="base"
            @click="handleAddScene"
            >确认添加
          </t-button>
          <t-button theme="default" variant="base" @click="handleBack"> 返回 </t-button>
        </div>
      </slot>
    </template>
  </main-page-container>
</template>
<script setup lang="ts">
import { computed } from 'vue';
import { Button as TButton } from 'tdesign-vue-next';
import { MainPageContainer } from '@/components/layouts';
import { CardLinkScene } from '@/helpers/constants/card-link';

defineOptions({
  name: 'CardLinkSceneContainer',
});

interface SceneContainerProps {
  sceneType: CardLinkScene;
  selectedCount?: number;
  showAddButton?: boolean;
  showActions?: boolean;
  customTitle?: string;
  customDescription?: string;
}

interface SceneConfig {
  title: string;
  description: string;
  statusText: string;
}
const props = withDefaults(defineProps<SceneContainerProps>(), {
  selectedCount: 0,
  showAddButton: false,
  showActions: true,
});

// Emits 定义
const emit = defineEmits<{
  goAddScene: [];
  submitAdd: [];
  back: [];
}>();

// 场景配置映射
const sceneConfigs: Record<CardLinkScene, SceneConfig> = {
  [CardLinkScene.MiniApp]: {
    title: '小程序支付',
    description: '用户在品牌的小程序中选择商品下单，跳转微信支付确认验密界面，完成支付',
    statusText: '共选 {count} 个交易场景',
  },
  [CardLinkScene.App]: {
    title: 'App支付',
    description: '用户在品牌的APP中选择商品下单，跳转到微信的支付确认验密界面，完成支付',
    statusText: '共选 {count} 个APP场景',
  },
  [CardLinkScene.PaymentCode]: {
    title: '付款码支付',
    description: '用户线下展示付款码，商户收银员用扫码设备扫描用户的付款码，用户确认支付',
    statusText: '共添加 {count} 个交易场景',
  },
  [CardLinkScene.PayScore]: {
    title: '支付分场景',
    description:
      '微信支付分提供先使用服务，再付款的能力，如免押金借用充电宝、电商先用后付等',
    statusText: '共添加 {count} 个交易场景',
  },
};

const sceneConfig = computed(() => {
  const baseConfig = sceneConfigs[props.sceneType];
  return {
    ...baseConfig,
    title: props.customTitle || baseConfig.title,
    description: props.customDescription || baseConfig.description,
  };
});

// 选中数量
const selectedCount = computed(() => props.selectedCount);

const isSubmitDisable = computed(() => selectedCount.value === 0);

const showDemo = () => {
  // TODO: 展示图示弹窗
};

const goAddScene = () => {
  emit('goAddScene');
};

const handleAddScene = () => {
  emit('submitAdd');
};

const handleBack = () => {
  emit('back');
};
</script>
<style scoped lang="less">
.scene-description {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;

  span {
    color: rgba(0, 0, 0, 0.5);
    font-size: 14px;
    font-weight: 400;
    line-height: normal;
  }

  .scene-description-link {
    color: #4848dd;
    cursor: pointer;
  }
}

.cark-link-actions {
  display: flex;
  gap: 8px;
}

.default-content {
  padding: 48px 24px;
  text-align: center;
  color: #999;
  background: #fafafa;
  border-radius: 8px;
  border: 1px dashed #ddd;
}

.status-text {
  color: rgba(0, 0, 0, 0.9);
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
</style>
