import { CardLinkScene } from '@/helpers/constants/card-link';

export interface SceneConfig {
  title: string;
  description: string;
  statusText: string;
}

export interface SceneContainerProps {
  sceneType: CardLinkScene;
  selectedCount?: number;
  showAddButton?: boolean;
  customTitle?: string;
  customDescription?: string;
}

export interface SceneContainerEmits {
  goAddScene: [];
  submitAdd: [];
  back: [];
}

export type SceneConfigMap = Record<CardLinkScene, SceneConfig>;

export { CardLinkScene };
