<template>
  <div class="pay-score-merchant-card">
    <div class="card-content">
      <div class="merchant-info">
        <div class="merchant-logo">
          <img :src="merchantLogo" :alt="merchantName" />
        </div>
        <div class="merchant-details">
          <div class="merchant-name">{{ merchantName }}</div>
          <div class="merchant-pay-desc">{{ merchantPayDesc }}</div>
          <div class="merchant-desc">{{ merchantDesc }}</div>
        </div>
      </div>
      <div class="link-state" :class="linkStateClass">
        {{ linkStateText }}
      </div>
    </div>
    <div v-if="errTipsType" class="error-tips">
      <div class="error-icon">!</div>
      <div class="error-text">
        {{ errorTipsText }}
        <span class="error-link" @click="handleLinkClick">关联商户号</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { LinkState, ErrTipsType } from './types';

interface Props {
  merchantLogo: string;
  merchantName: string;
  merchantPayDesc: string;
  merchantDesc: string;
  linkState: LinkState;
  errTipsType?: ErrTipsType;
}

const props = withDefaults(defineProps<Props>(), {
  merchantLogo: '',
  merchantName: '',
  merchantPayDesc: '',
  merchantDesc: '',
  linkState: LinkState.Available,
  errTipsType: undefined,
});

const emit = defineEmits<{
  linkClick: [];
}>();

const linkStateClass = computed(() => ({
  'state-available': props.linkState === LinkState.Available,
  'state-added': props.linkState === LinkState.Added,
  'state-unavailable': props.linkState === LinkState.Unavailable,
}));

const linkStateText = computed(() => {
  switch (props.linkState) {
    case LinkState.Available:
      return '可添加';
    case LinkState.Added:
      return '已添加';
    case LinkState.Unavailable:
      return '暂不支持添加';
    default:
      return '可添加';
  }
});

const errorTipsText = computed(() => {
  switch (props.errTipsType) {
    case ErrTipsType.MerchantIdNotBound:
      return '该服务ID关联的商户号不在品牌的收款商户号列表中，请确认服务ID关联的商户号并和品牌绑定';
    default:
      return '';
  }
});

const handleLinkClick = () => {
  emit('linkClick');
};
</script>

<style scoped>
.pay-score-merchant-card {
  display: flex;
  flex-direction: column;
  width: 628px;
  min-height: 122px;
  padding: 24px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin-top: 16px;
  opacity: 1;
  box-sizing: border-box;
}

.card-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
}

.merchant-info {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.merchant-logo {
  width: 48px;
  height: 48px;
  margin-right: 12px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.merchant-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.merchant-details {
  flex: 1;
}

.merchant-name {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: #000000e5;
  margin-bottom: 4px;
}

.merchant-pay-desc {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  text-align: left;
  margin-top: 8px;
  vertical-align: middle;
  color: #000000e5;
  margin-bottom: 2px;
}

.merchant-desc {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  margin-top: 8px;
  vertical-align: middle;
  color: #0000004d;
  max-width: 520px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.link-state {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  white-space: nowrap;
  flex-shrink: 0;
  margin-left: 16px;
}

.state-available {
  color: #39ad36;
}

.state-added {
  color: #1890ff;
}

.state-unavailable {
  color: #f14752;
}

.error-tips {
  display: flex;
  align-items: flex-start;
  margin-top: 16px;
  gap: 8px;
}

.error-icon {
  width: 16px;
  height: 16px;
  background: #f14752;
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-text {
  font-family:
    'PingFang SC',
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #00000080;
  flex: 1;
}

.error-link {
  color: #4848dd;
  cursor: pointer;
  text-decoration: none;
}

.error-link:hover {
  text-decoration: underline;
}
</style>
