<template>
  <div class="scene-add-card">
    <div class="scene-add-header">
      <div class="scene-add-title">
        {{ props.card.title }}
      </div>
      <div :class="props.card.needDeploy ? 'scene-add-info-deploy' : 'scene-add-info'">
        {{ props.card.needDeploy ? props.card.info : '未配置' }}
      </div>
    </div>
    <div class="scene-add-desc">
      {{ props.card.desc }}
    </div>
    <div class="scene-guide-img-view">
      <img class="guide-img" :src="props.card.guideImg" @click="onClickGuideImg" />
    </div>
    <div class="scene-add-btn-view">
      <button class="scene-add-btn" @click="click">添加</button>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps({
  card: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['click']);

const click = () => {
  emit('click');
};

const onClickGuideImg = () => {};
</script>

<style scoped>
.scene-add-card {
  width: 568px;
  height: 458px;
  opacity: 1;
  border-radius: 8px;
  padding: 24px;
  background-color: rgba(0, 0, 0, 0.02);
  position: relative;
  margin-bottom: 24px;
  display: flex;
  flex-direction: column;
}

.scene-add-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.scene-add-title {
  font-weight: 500;
  font-style: normal;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.9);
  margin-right: 12px;
}

.scene-add-info-deploy {
  color: #07c160;
  font-size: 16px;
  font-style: normal;
  font-weight: 400;
  line-height: 100%;
  margin: 0;
}

.scene-add-info {
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.3);
  margin: 0;
}

.scene-add-desc {
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 12px;
}

.scene-guide-img-view {
  width: 520px;
  height: 280px;
  padding: 0 21px;
  background: rgba(0, 0, 0, 0.03);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  margin-bottom: 24px;
}

.guide-img {
  width: 478px;
  height: 240px;
  display: block;
}

.scene-add-btn-view {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.scene-add-btn {
  width: 112px;
  height: 40px;
  opacity: 1;
  border-radius: 8px;
  padding: 9px 24px;
  background: #07c160;
  color: white;
  border: none;
  cursor: pointer;
}
</style>
