<template>
  <div class="scene-card">
    <div class="scene-header">
      <div class="scene-title">
        {{ props.title }}
      </div>
      <div class="scene-info">
        {{ props.info }}
      </div>
    </div>
    <div class="scene-desc">
      {{ props.desc }}
    </div>
    <button class="view-btn" @click="click">查看</button>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'LinkSceneCard',
});
const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  info: {
    type: String,
    required: true,
  },
  desc: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['click']);

const click = () => {
  emit('click');
};
</script>

<style scoped>
.scene-card {
  width: 568px;
  height: 158px;
  opacity: 1;
  gap: 10px;
  border-radius: 8px;
  padding: 24px;
  background-color: #f7f7f7;
  position: relative;
  margin-bottom: 16px;
  display: flex;
  flex-direction: column;
}

.scene-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.scene-title {
  font-family: 'PingFang SC';
  font-weight: 500;
  font-style: normal;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.9);
  margin: 0;
}

.scene-info {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 16px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.3);
  margin: 0;
}

.scene-desc {
  font-family: 'PingFang SC';
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.5);
  margin-bottom: 16px;
}

.view-btn {
  background-color: #f2f2f2;
  border: none;
  cursor: pointer;
  width: 112px;
  height: 40px;
  opacity: 1;
  gap: 10px;
  border-radius: 8px;
  padding: 9px 24px;
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
