import CardLinkSceneContainer from '../components/card-link-scene-container';
import { CardLinkScene } from '@/helpers/constants/card-link';
import CardLinkSearchView from '../components/search-view';
import {
  PayScoreMerchantCard,
  ErrTipsType,
  LinkState,
} from '../components/pay-score-merchant-card';

import { ref, reactive } from 'vue';
import { useRouter } from 'vue-router';

// 定义数据结构类型

interface PayScoreMerchantInfo {
  merchantName: string;
  merchantPayDesc: string;
  merchantDesc: string;
  linkState: LinkState;
  errTipsType?: ErrTipsType;
}

interface PayScoreServiceInfo {
  serviceId?: string;
  payScoreMerchantInfo?: PayScoreMerchantInfo;
  isSearching: boolean;
  errMsg?: string;
}

export const usePayScoreAdd = () => {
  const router = useRouter();
  const sceneNums = ref(1);
  const searchErrMsg = ref('');

  // 初始化服务信息列表，包含一个示例数据
  const payScoreServiceInfoList = reactive<PayScoreServiceInfo[]>([
    {
      isSearching: false,
    },
  ]);

  const handleAdd = () => {
    console.log('add');
  };

  const handleSubmit = () => {
    console.log('submit');
  };

  const handleBack = () => {
    console.log('back');
    router.back();
  };

  const handleSearch = (searchValue: string, index: number) => {
    // 只影响当前项的搜索状态
    payScoreServiceInfoList[index].isSearching = true;
    payScoreServiceInfoList[index].errMsg = '';

    // 更新对应索引的 serviceId
    payScoreServiceInfoList[index].serviceId = searchValue;

    // 模拟搜索
    setTimeout(() => {
      payScoreServiceInfoList[index].isSearching = false;
      // 模拟错误情况
      if (searchValue === 'serviceid-err') {
        payScoreServiceInfoList[index].errMsg = '服务ID不存在，请检查后重新输入';
        // 清除商户信息
        payScoreServiceInfoList[index].payScoreMerchantInfo = undefined;
      } else if (searchValue === 'merchant-error') {
        console.log('搜索服务ID:', searchValue);
        // 清除当前项的错误信息
        payScoreServiceInfoList[index].errMsg = '';
        // 模拟商户号未绑定的情况
        payScoreServiceInfoList[index].payScoreMerchantInfo = {
          merchantName: '拼多多',
          merchantPayDesc: '收货后自动付款',
          merchantDesc:
            '免付款下单，确认收货后自动支付实际产生的商品费用免付款下单，确认收货后自动支付实际产生的商品费用',
          linkState: LinkState.Unavailable,
          errTipsType: ErrTipsType.MerchantIdNotBound, // 显示商户号未绑定错误提示
        };
      } else {
        console.log('搜索服务ID:', searchValue);
        // 清除当前项的错误信息
        payScoreServiceInfoList[index].errMsg = '';
        // 模拟搜索成功，设置商户信息
        payScoreServiceInfoList[index].payScoreMerchantInfo = {
          merchantName: '拼多多',
          merchantPayDesc: '收货后自动付款',
          merchantDesc:
            '免付款下单，确认收货后自动支付实际产生的商品费用免付款下单，确认收货后自动支付实际产生的商品费用',
          linkState: LinkState.Available,
        };
      }
    }, 1500);
  };

  const handleClear = (index: number) => {
    // 删除整个 payScoreServiceInfo 项
    payScoreServiceInfoList.splice(index, 1);
    // 更新场景数量
    sceneNums.value = payScoreServiceInfoList.length;
    searchErrMsg.value = '';
  };

  const handleAddService = () => {
    // 添加新的服务ID项
    payScoreServiceInfoList.push({
      serviceId: '',
      isSearching: false,
      payScoreMerchantInfo: undefined,
    });
    sceneNums.value = payScoreServiceInfoList.length;
    console.log('添加支付分服务ID');
  };

  const handleLinkClick = () => {
    // 处理"关联商户号"点击事件
    console.log('点击关联商户号');
    // 这里可以跳转到商户号绑定页面或弹出绑定弹窗
  };

  return {
    sceneNums,
    errMsg: searchErrMsg,
    handleAdd,
    handleSubmit,
    handleBack,
    handleSearch,
    handleClear,
    handleAddService,
    handleLinkClick,
    payScoreServiceInfoList,
  };
};

// 导出组件依赖
export {
  CardLinkSceneContainer,
  CardLinkScene,
  CardLinkSearchView,
  PayScoreMerchantCard,
};
export type { PayScoreMerchantInfo, PayScoreServiceInfo };
export { LinkState, ErrTipsType };
