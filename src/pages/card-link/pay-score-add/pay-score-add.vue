<template>
  <CardLinkSceneContainer
    :scene-type="CardLinkScene.PayScore"
    :selected-count="sceneNums"
    :show-add-button="true"
    @go-add-scene="handleAdd"
    @submit-add="handleSubmit"
    @back="handleBack"
  >
    <template #content>
      <div class="pay-score-content">
        <div
          v-for="(serviceInfo, index) in payScoreServiceInfoList"
          :key="index"
          class="service-id-section"
        >
          <CardLinkSearchView
            v-model="serviceInfo.serviceId"
            label="支付分服务ID"
            placeholder="输入服务ID，回车或点击按钮搜索"
            label-width="628px"
            :searching="serviceInfo.isSearching"
            :err-msg="serviceInfo.errMsg"
            :show-clear-btn="true"
            @search="(value) => handleSearch(value, index)"
            @clear="() => handleClear(index)"
          />
          <PayScoreMerchantCard
            v-if="serviceInfo.payScoreMerchantInfo"
            :merchant-logo="pddIcon"
            :merchant-name="serviceInfo.payScoreMerchantInfo.merchantName"
            :merchant-pay-desc="serviceInfo.payScoreMerchantInfo.merchantPayDesc"
            :merchant-desc="serviceInfo.payScoreMerchantInfo.merchantDesc"
            :link-state="serviceInfo.payScoreMerchantInfo.linkState"
            :err-tips-type="serviceInfo.payScoreMerchantInfo.errTipsType"
            @link-click="handleLinkClick"
          />
        </div>
        <div class="add-service-link" @click="handleAddService">添加支付分服务ID</div>
      </div>
    </template>
  </CardLinkSceneContainer>
</template>
<script setup lang="ts">
import {
  CardLinkSceneContainer,
  CardLinkScene,
  CardLinkSearchView,
  PayScoreMerchantCard,
  usePayScoreAdd,
} from './pay-score-add';
import pddIcon from '@/assets/images/cardlink/test-pdd-icon.png';

const {
  sceneNums,
  handleAdd,
  handleSubmit,
  handleBack,
  handleSearch,
  handleClear,
  handleAddService,
  handleLinkClick,
  payScoreServiceInfoList,
} = usePayScoreAdd();
</script>
<style scoped>
@import './pay-score-add.css';
</style>
