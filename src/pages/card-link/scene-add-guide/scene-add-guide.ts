// src/pages/card-link/scene-add-guide/scene-add-guide.ts
import { useRouter } from 'vue-router';

export const handleSceneAdd = () => {
  const router = useRouter();

  const addMiniAppPayScene = () => {
    console.log('添加小程序支付场景');
  };

  const addAppPayScene = () => {
    console.log('添加App支付场景');
  };

  const addPayScoreScene = () => {
    console.log('添加支付分场景');
    router.push('/card-link/pay-score-add');
  };

  const addPayCodeScene = () => {
    console.log('添加付款码支付场景');
  };

  return {
    addMiniAppPayScene,
    addAppPayScene,
    addPayScoreScene,
    addPayCodeScene,
  };
};
