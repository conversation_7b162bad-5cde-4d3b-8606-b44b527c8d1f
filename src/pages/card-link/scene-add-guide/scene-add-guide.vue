<template>
  <div class="card-link-pay-scene-add">
    <div class="header">
      <h2 class="title">选择需要添加的交易场景类型</h2>
    </div>
    <div class="grid-view">
      <LinkedSceneAddGuideCard
        v-for="(card, index) in cardList"
        :key="index"
        :card="card"
        @click="card.click"
      />
    </div>
    <div class="footer">更多场景敬请期待</div>
  </div>
</template>

<script setup lang="ts">
import { handleSceneAdd } from './scene-add-guide';
import LinkedSceneAddGuideCard from '../components/link-scene-add-guide-card/link-scene-add-guide-card.vue';
import cardLinkHomeExample from '@/assets/images/cardlink/card-link-miniapp-pay-guide.png';
import cardLinkAppPayGuide from '@/assets/images/cardlink/card-link-app-pay-guide.png';
import cardLinkPayCodeGuide from '@/assets/images/cardlink/card-link-pay-code-guide.png';

import './scene-add-guide.css';

interface LinkedSceneAddGuideCardInfo {
  title: string;
  needDeploy: boolean;
  info: string;
  desc: string;
  guideImg: string;
  click: () => void;
}

const { addMiniAppPayScene, addAppPayScene, addPayScoreScene, addPayCodeScene } =
  handleSceneAdd();

const cardList: LinkedSceneAddGuideCardInfo[] = [
  {
    title: '小程序支付',
    info: `已配置 2 个交易场景`,
    desc: '用户在品牌的小程序中选择商品下单，跳转微信支付输入密码界面，完成支付',
    guideImg: cardLinkHomeExample,
    needDeploy: true,
    click: addMiniAppPayScene,
  },
  {
    title: 'App支付',
    info: `未配置`,
    desc: '用户在品牌的APP中选择商品下单，跳转到微信的支付确认验密界面，完成支付',
    guideImg: cardLinkAppPayGuide,
    needDeploy: false,
    click: addAppPayScene,
  },
  {
    title: '付款码支付',
    info: `已配置 100 个交易场景`,
    desc: '用户线下展示付款码，商户收银员用扫码设备扫描用户的付款码，用户确认支付',
    guideImg: cardLinkPayCodeGuide,
    needDeploy: true,
    click: addPayCodeScene,
  },
  {
    title: '支付分',
    info: `已配置 1 个交易场景`,
    desc: '微信支付分提供先使用服务，再付款的能力，如免押金借用充电宝、电商先用后付等',
    guideImg: cardLinkAppPayGuide,
    needDeploy: true,
    click: addPayScoreScene,
  },
];
</script>
