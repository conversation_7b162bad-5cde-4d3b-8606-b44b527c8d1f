/* src/pages/card-link/scene-add-guide/scene-add-guide.css */
.card-link-pay-scene-add {
  background-color: white;
  width: 1224px;
  min-height: auto;
  opacity: 1;
  gap: 24px;
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  padding: 32px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin: 0;
}

/* 网格视图样式 */
.grid-view {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-top: 16px;
}

.footer {
  color: rgba(0, 0, 0, 0.30);
  text-align: center;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
