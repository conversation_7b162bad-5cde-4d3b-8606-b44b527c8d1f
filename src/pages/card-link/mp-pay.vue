<template>
  <CardLinkSceneContainer
    :scene-type="CardLinkScene.MiniApp"
    :selected-count="sceneNums"
    :show-add-button="true"
    @go-add-scene="handleAdd"
    @submit-add="handleSubmit"
    @back="handleBack"
  >
    <template #content>
      <div>main content</div>
    </template>
  </CardLinkSceneContainer>
</template>
<script setup lang="ts">
import CardLinkSceneContainer from './components/card-link-scene-container';
import { CardLinkScene } from '@/helpers/constants/card-link';
import { ref } from 'vue';

const sceneNums = ref(0);

const handleAdd = () => {
  console.log('add');
};

const handleSubmit = () => {
  console.log('submit');
};

const handleBack = () => {
  console.log('back');
};
</script>
<style scoped lang="less"></style>
