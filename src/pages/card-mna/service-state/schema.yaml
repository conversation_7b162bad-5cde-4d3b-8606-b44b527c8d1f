schema:
  cssClasses:
    form-container:
      background-color: '#fff'
      padding: 32px
      height: calc(100vh - 214px)
      border-radius: 16px
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center
  layout: v-simplelayout
  meta:
    icon: card
    title: 微信支付 品牌经营
  main:
    interactions:
      - '#/interactions/api_getServicePublishProcess'
      - path: '#/interactions/renderStateInfo'
        slot: top1
  apis:
    getServicePublishProcess:
      url: /get-latest-service-publish-process
      method: GET
  definitions:
    serviceState:
      state:
        processId: ''
        state: 0
        rejectReason: ''
  interactions:
    api_getServicePublishProcess:
      steps:
        - stage: process
          api: '#/apis/getServicePublishProcess'
        - stage: handle
          states:
            - '#/definitions/serviceState/state': '$StepData$.data'
    renderStateInfo:
      steps:
        - stage: render
          from:
            - state: '#/definitions/serviceState/state'
              as: serviceState
          renderType: div
          children:
            - renderType: div
              props:
                class: form-container
              children:
                - renderType: v-state-preview
                  props:
                    style:
                      padding: '20px'
                      backgroundColor: '#FFFFFF'
                      borderRadius: '8px'
