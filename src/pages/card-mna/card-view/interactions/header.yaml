steps:
  - stage: render
    from:
      - state: "#/definitions/brandInfo/state"
        as: brandInfo
      - path: "card-view/:cardId"
        as: 
          - cardId
    renderType: div
    props:
      style:
        display: flex
        align-items: center
        justify-content: space-between
        width: 100%
        margin-bottom: 24px
    children:
      - renderType: div
        props:
          style:
            display: flex
            align-items: center
            gap: 16px
        children:
          - renderType: div
            props:
              style:
                font-size: 24px
                font-weight: 600
                color: rgba(0, 0, 0, 0.9)
            children:
              - 基础信息
      - renderType: div
        props:
          style:
            display: flex
            gap: 16px
        children:
          - renderType: v-button
            props:
              theme: default
              variant: base
              content: 修改
              events:
                click:
                  - jump: "/home/<USER>/card-config/${cardId}"
                  - interaction: '#/interactions'