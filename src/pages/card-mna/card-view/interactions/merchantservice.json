{"steps": [{"stage": "render", "from": [{"state": "#/definitions/customerService/state", "as": "customerService"}], "renderType": "div", "props": {"style": {"width": "100%"}}, "children": [{"renderType": "div", "children": [{"renderType": "div", "props": {"style": {"display": "flex", "flex-direction": "column", "gap": "16px", "width": "100%"}}, "condition": {"literal": "('miniProgram' in customerService && customerService.miniProgram != null) || ('wecom' in customerService && customerService.wecom != null)"}, "children": ["在线客服"]}, {"renderType": "div", "props": {"class": "inner-row-container"}, "condition": {"literal": "customerService.customize != null"}, "children": [{"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["客服类型"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["自建客服"]}]}, {"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["自建客服path"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${customerService.customize.path}"]}]}]}, {"renderType": "div", "props": {"class": "inner-row-container"}, "condition": {"literal": "customerService.miniProgram != null"}, "children": [{"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["客服类型"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["小程序客服"]}]}, {"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["小程序appid"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${customerService.miniProgram.appid}"]}]}]}, {"renderType": "div", "props": {"style": {"display": "flex", "flex-direction": "column", "gap": "16px", "width": "100%"}}, "condition": {"literal": "'servicePhone' in customerService"}, "children": ["客服电话"]}, {"renderType": "div", "condition": {"literal": "'servicePhone' in customerService"}, "props": {"class": "inner-row-container"}, "children": [{"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["客服电话"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${customerService.servicePhone}"]}]}]}]}]}]}