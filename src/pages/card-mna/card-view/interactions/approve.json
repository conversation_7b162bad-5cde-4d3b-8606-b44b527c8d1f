{"steps": [{"stage": "prepare", "from": [{"path": "card-view/:cardId", "as": ["cardId"]}]}, {"stage": "process", "api": "#/apis/getCardApprove"}, {"stage": "error", "handlers": [{"case": "code", "when": 1, "message": "发生错误", "interaction": "#/interactions/commonError", "slot": "dialogContainer"}]}, {"stage": "handle", "states": [{"#/definitions/cardApprove/state": {"status": "$StepData$.status", "message": "$StepData$.message"}}]}, {"stage": "render", "from": [{"state": "#/definitions/cardApprove/state", "as": "cardApprove"}], "renderType": "div", "children": [{"renderType": "v-alert", "condition": {"literal": "cardApprove.status != 0"}, "props": {"theme": "info", "message": "${cardApprove.message}", "icon": "v-time-icon", "style": {"margin": "16px 0", "background-color": "#2A9EFF1F"}}}]}]}