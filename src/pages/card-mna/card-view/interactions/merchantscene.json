{"steps": [{"stage": "render", "from": [{"state": "#/definitions/cardConfigSence/state", "as": "cardConfigSence"}], "renderType": "div", "props": {"style": {"width": "100%"}}, "children": [{"renderType": "div", "condition": {"literal": "'miniProgram' in cardConfigSence"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "flex-direction": "column", "gap": "16px", "width": "100%"}}, "children": ["小程序11"]}, {"renderType": "div", "props": {"class": "inner-row-container"}, "condition": {"path": "cardConfigSence", "conditions": [{"path": "miniProgram", "conditions": [{"op": "exists"}]}]}, "children": [{"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["跳转小程序"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${cardConfigSence.miniProgram.appid}"]}]}, {"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["场景标签"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${cardConfigSence.miniProgram.sceneTag}"]}]}, {"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["场景展示图片"]}, {"renderType": "v-thumb", "props": {"style": {"display": "flex", "align-items": "center"}, "images": "${cardConfigSence.miniProgram.imageList}"}}]}]}]}, {"renderType": "div", "condition": {"literal": "'finder' in cardConfigSence"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "flex-direction": "column", "gap": "16px", "width": "100%"}}, "children": ["视频号"]}, {"renderType": "div", "props": {"class": "inner-row-container"}, "condition": {"path": "cardConfigSence", "conditions": [{"path": "finder", "conditions": [{"op": "exists"}]}]}, "children": [{"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["跳转视频号"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${cardConfigSence.finder.appid}"]}]}, {"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["场景标签"]}, {"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center"}}, "children": ["${cardConfigSence.finder.sceneTag}"]}]}, {"renderType": "div", "props": {"class": "inner-row"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "align-items": "center", "width": "128px"}}, "children": ["场景展示图片"]}, {"renderType": "v-thumb", "props": {"style": {"display": "flex", "align-items": "center"}, "images": "${cardConfigSence.finder.imageList}"}}]}]}]}]}]}