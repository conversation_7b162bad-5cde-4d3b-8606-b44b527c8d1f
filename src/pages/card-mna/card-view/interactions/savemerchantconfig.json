{"schema": {"interactions": {"saveMerchantConfig": {"steps": [{"stage": "prepare", "from": [{"#/definitions/formCheckboxStatus/state": {"as": "formCheckboxStatus"}}, {"#/definitions/customerService/state": {"as": "customerService"}}, {"#/definitions/cardConfigSence/state": {"as": "cardConfigSence"}}, {"#/definitions/cardBrief/state": {"as": "cardBrief"}}], "handler": {"from": "userProvide", "path": "utils.prepareSaveCardInfo"}}, {"stage": "process", "api": "#/apis/saveCardConfig", "loading": "#/global/loading"}, {"stage": "render", "renderType": "v-dialog", "props": {"confirmBtn": "返回配置页", "cancelBtn": null, "onConfirm": [{"jump": "/home/<USER>/card-detail"}]}, "children": ["商家资料添加成功"]}]}}}}