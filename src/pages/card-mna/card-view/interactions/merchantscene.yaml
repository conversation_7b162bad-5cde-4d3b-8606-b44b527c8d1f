  steps:
  - stage: render
    from:
      - state: "#/definitions/cardConfigSence/state" # 所有事件产生的数据都放在eventData中
        as: cardConfigSence # 所有事件产生的数据都放在eventData中
    renderType: div
    props:
      style:
        width: "100%"
    children:
    - renderType: div
      # condition:
      #   path: cardConfigSence
      #   op: or
      #   conditions:
      #   - path: miniProgram
      #     conditions:
      #     - op: exists

      # 引入字面量条件表达式
      condition:
        literal: "'miniProgram' in cardConfigSence"
      children: 
      ## {{ miniProgram  start }}  ##
      - renderType: div
        props:
          style:
            display: "flex"
            flex-direction: "column"
            gap: "16px"
            width: "100%"
        
        children:
        - 小程序11
      - renderType: div
        props:
          class: inner-row-container
        condition:
          path: cardConfigSence
          conditions:
          - path: miniProgram
            conditions:
            - op: exists
        children: 
        - renderType: div
          props:
            class: inner-row
          children:
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
                width: 128px
            children:
            - 跳转小程序
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
            children:
            - ${cardConfigSence.miniProgram.appid}
        - renderType: div
          props:
            class: inner-row
          children:
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
                width: 128px
            children:
            - 场景标签
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
            children:
            - ${cardConfigSence.miniProgram.sceneTag}
        - renderType: div
          props:
            class: inner-row
          children:
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
                width: 128px
            children:
            - 场景展示图片
          - renderType: v-thumb
            props:
              style:
                display: "flex"
                align-items: "center"
              images: 
                ${cardConfigSence.miniProgram.imageList}
    ## {{ miniProgram  end }}  ##

    ## {{ find row start}}
    - renderType: div
      condition:
        # path: cardConfigSence
        # op: or
        # conditions:
        # - path: finder
        #   conditions:
        #   - op: exists
        literal: "'finder' in cardConfigSence"
      children: 
      - renderType: div
        props:
          style:
            display: "flex"
            flex-direction: "column"
            gap: "16px"
            width: "100%"
        
        children:
        - 视频号
      - renderType: div
        props:
          class: inner-row-container
        condition:
          path: cardConfigSence
          conditions:
          - path: finder
            conditions:
            - op: exists
        children: 
        - renderType: div
          props:
            class: inner-row
          children:
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
                width: 128px
            children:
            - 跳转视频号
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
            children:
            - ${cardConfigSence.finder.appid}
        - renderType: div
          props:
            class: inner-row
          children:
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
                width: 128px
            children:
            - 场景标签
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
            children:
            - ${cardConfigSence.finder.sceneTag}
        - renderType: div
          props:
            class: inner-row
          children:
          - renderType: div
            props:
              style:
                display: "flex"
                align-items: "center"
                width: 128px
            children:
            - 场景展示图片
          - renderType: v-thumb
            props:
              style:
                display: "flex"
                align-items: "center"
              images: 
                ${cardConfigSence.finder.imageList}