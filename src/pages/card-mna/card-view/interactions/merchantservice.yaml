steps:
  - stage: render
    from:
      - state: '#/definitions/customerService/state' # 所有事件产生的数据都放在eventData中
        as: customerService # 所有事件产生的数据都放在eventData中
    renderType: div
    props:
      style:
        width: '100%'
    children:
      - renderType: div
        children:
          - renderType: div
            props:
              style:
                display: 'flex'
                flex-direction: 'column'
                gap: '16px'
                width: '100%'
            # condition:
            #   path: customerService
            #   op: or
            #   conditions:
            #   - path: miniProgram
            #     conditions:
            #     - op: exists
            #   - path: wecom
            #     conditions:
            #     - op: exists
            condition:
              literal:
                "('miniProgram' in customerService && customerService.miniProgram != null)
                || ('wecom' in customerService && customerService.wecom != null)"
            children:
              - 在线客服
          - renderType: div
            props:
              class: inner-row-container
            # condition:
            #   path: customerService
            #   conditions:
            #   - path: customize
            #     conditions:
            #     - op: exists
            condition:
              literal: 'customerService.customize != null'
            children:
              - renderType: div
                props:
                  class: inner-row
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                        width: 128px
                    children:
                      - 客服类型
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                    children:
                      - 自建客服
              - renderType: div
                props:
                  class: inner-row
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                        width: 128px
                    children:
                      - 自建客服path
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                    children:
                      - ${customerService.customize.path}
          ## {customize end }  ##
          ## {miniProgram start} ##
          - renderType: div
            props:
              class: inner-row-container
            condition:
              literal: 'customerService.miniProgram != null'
            # condition:
            #   path: customerService
            #   conditions:
            #   - path: miniProgram
            #     conditions:
            #     - op: exists
            children:
              - renderType: div
                props:
                  class: inner-row
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                        width: 128px
                    children:
                      - 客服类型
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                    children:
                      - 小程序客服
              - renderType: div
                props:
                  class: inner-row
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                        width: 128px
                    children:
                      - 小程序appid
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                    children:
                      - ${customerService.miniProgram.appid}
          ## { miniProgram end } ##

          ## { 客服电话 start } ##
          - renderType: div
            props:
              style:
                display: 'flex'
                flex-direction: 'column'
                gap: '16px'
                width: '100%'
            condition:
              literal: "'servicePhone' in customerService"
            # condition:
            #   path: customerService
            #   op: or
            #   conditions:
            #   - path: servicePhone
            #     conditions:
            #     - op: exists
            children:
              - 客服电话
          - renderType: div
            condition:
              literal: "'servicePhone' in customerService"
            props:
              class: inner-row-container
            # condition:
            #   path: customerService
            #   conditions:
            #   - path: miniProgram
            #     conditions:
            #     - op: exists
            children:
              # - renderType: div
              #   props:
              #     class: inner-row
              #   children:
              #   - renderType: div
              #     props:
              #       style:
              #         display: "flex"
              #         align-items: "center"
              #         width: 128px
              #     children:
              #     - 客服类型
              #   - renderType: div
              #     props:
              #       style:
              #         display: "flex"
              #         align-items: "center"
              #     children:
              #     - 客服电话
              - renderType: div
                props:
                  class: inner-row
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                        width: 128px
                    children:
                      - 客服电话
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        align-items: 'center'
                    children:
                      - ${customerService.servicePhone}
        ## { 客服电话 end } ##
