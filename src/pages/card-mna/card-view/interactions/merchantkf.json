{"schema": {"interactions": {"merchantOnlineKF": {"steps": [{"stage": "render", "from": [{"variables": "eventData", "as": "eventData"}], "renderType": "div", "props": {}, "children": [{"watch": [{"variable": "eventData", "when": true}], "renderType": "schema-form", "props": {"title": "客服配置", "layout": "vertical", "labelAlign": "left", "labelWidth": "200px"}, "schema": {"type": "object", "formItemProps": {"labelWidth": "160px"}, "properties": {"customerServiceType": {"definition": "#/concepts/merchantService/customerServiceType", "ui:widget": "v-radio-group", "ui:options": {"direction": "horizontal", "size": "medium"}}, "customize.path": {"watchState": [{"state": "#/definitions/customerService/state", "by": "customerServiceType", "when": 3}], "definition": "#/concepts/merchantService/customize/properties/path", "ui:options": {"placeholder": "请输入自建客服路径", "style": {"width": "400px"}}}, "miniProgram.appid": {"watchState": [{"state": "#/definitions/customerService/state", "by": "customerServiceType", "when": 1}], "definition": "#/concepts/merchantService/miniprogram/properties/appid", "ui:options": {"placeholder": "请输入小程序AppID", "style": {"width": "400px"}}}, "wecom.path": {"watchState": [{"state": "#/definitions/customerService/state", "by": "customerServiceType", "when": 2}], "definition": "#/concepts/merchantService/qywx/properties/path", "ui:options": {"placeholder": "请输入企业微信客服路径", "style": {"width": "400px"}}}}}, "formData": "#/definitions/customerService/state"}]}]}, "merchantTelKF": {"steps": [{"stage": "render", "from": [{"variables": "eventData", "as": "eventData"}], "renderType": "div", "props": {}, "children": [{"watch": [{"variable": "eventData", "when": true}], "renderType": "schema-form", "props": {"title": "客服配置", "layout": "vertical", "labelAlign": "left"}, "schema": {"type": "object", "formItemProps": {"labelWidth": "160px"}, "properties": {"servicePhone": {"definition": "#/concepts/merchantService/servicePhone", "title": "客服电话xx", "ui:options": {"placeholder": "请输入企业微信客服路径", "style": {"width": "400px"}}}}}, "formData": "#/definitions/customerService/state"}]}]}}}}