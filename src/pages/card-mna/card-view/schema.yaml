schema:
  globalStyle:
    formtitle:
      background-color: '#fff'
      padding: '16px'
  cssClasses:
    main-layout:
      display: 'flex'
      width: '100%'
      height: '100%'
      min-height: '600px'
    left-panel:
      flex: '1'
      padding: '24px'
      background-color: '#ffffff'
    right-panel:
      width: '320px'
      padding: '16px'
      background-color: '#f7f8fa'
      border-left: '1px solid #E7E7E7'
    page-header:
      padding: '16px 24px'
      margin-bottom: '16px'
    config-item:
      margin-bottom: '12px'
      background-color: '#FAFBFC'
      border-radius: '6px'
    config-header:
      display: 'flex'
      align-items: 'center'
      justify-content: 'space-between'
      padding: '20px 24px'
    config-title:
      display: 'flex'
      align-items: 'center'
      font-size: '16px'
      font-weight: '500'
    config-body:
      padding: '0px 24px 28px 24px'
    config-status:
      display: 'inline-block'
      padding: '0 8px'
      border-radius: '2px'
      font-size: '12px'
      margin-left: '8px'
      height: '20px'
      line-height: '20px'
    status-active:
      background-color: '#E8F6EF'
      color: '#07C160'
    status-inactive:
      background-color: '#F1F2F5'
      color: '#909399'
    warning-dot:
      display: 'inline-block'
      width: '8px'
      height: '8px'
      border-radius: '50%'
      background-color: '#07C160'
      margin-right: '10px'
  meta:
    icon: card
    title: '微信支付 品牌经营'
  layout: v-leftright-layout-detail
  main:
    interactions:
      - '#/interactions/initapis'
      - path: '#/interactions/leftConfig'
        slot: 'left'
      - path: '#/interactions/rightPreview'
        slot: 'right'

  # 交互定义
  interactions:
    # 左边配置项区域
    leftConfig: !include interactions/leftConfig.yaml
    # 右边预览区域
    rightPreview: !include interactions/rightPreview.yaml

