schema:
  globalStyle:
    formtitle:
      background-color: '#fff'
      padding: '16px'
  cssClasses:
    merchant-avatar:
      width: '80px'
      height: '80px'
      border-radius: '50%'
      margin-right: '16px'
      background-color: '#f5f5f5'
      object-fit: 'cover'

    merchant-card:
      display: flex;
      padding: 24px;
      align-items: center;
      gap: 16px;
      align-self: stretch;
      border-radius: 8px;
      background: rgba(0, 0, 0, 0.02);
    merchant-name:
      color: rgba(0, 0, 0, 0.90);
      text-align: right;
      font-family: 'PingFang SC'
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    inner-row:
      display: flex;
      padding: 12px;
      align-items: flex-start;
      align-content: flex-start;
      gap: 48px;
      align-self: stretch;
      flex-wrap: wrap;
      border-radius: 8px;
    inner-row-container:
      background: rgba(0, 0, 0, 0.02);
      padding: 12px;
      border-radius: 8px;
      margin: 12px 0
    main-layout:
      display: 'flex'
      width: '100%'
      height: '100%'
      min-height: '600px'
    left-panel:
      flex: '1'
      padding: '24px'
      background-color: '#ffffff'
    right-panel:
      width: '320px'
      padding: '16px'
      background-color: '#f7f8fa'
      border-left: '1px solid #E7E7E7'
    page-header:
      padding: '16px 24px'
      margin-bottom: '16px'
    config-item:
      margin-bottom: '12px'
      background-color: '#FAFBFC'
      border-radius: '6px'
    config-header:
      display: 'flex'
      align-items: 'center'
      justify-content: 'space-between'
      padding: '20px 24px'
    config-title:
      display: 'flex'
      align-items: 'center'
      font-size: '16px'
      font-weight: '500'
    config-body:
      padding: '0px 24px 28px 24px'
    config-status:
      display: 'inline-block'
      padding: '0 8px'
      border-radius: '2px'
      font-size: '12px'
      margin-left: '8px'
      height: '20px'
      line-height: '20px'
    status-active:
      background-color: '#E8F6EF'
      color: '#07C160'
    status-inactive:
      background-color: '#F1F2F5'
      color: '#909399'
    warning-dot:
      display: 'inline-block'
      width: '8px'
      height: '8px'
      border-radius: '50%'
      background-color: '#07C160'
      margin-right: '10px'
  meta:
    icon: card
    title: 微信支付 品牌经营
  layout: v-simplelayout
  main:
    interactions:
      - '#/interactions/api_getBrandInfo'
      # - '#/interactions/api_getCardConfig'
      - path: '#/interactions/leftConfig'
        slot: 'top1'
      # - path: '#/interactions/rightPreview'
      #   slot: 'right'
  apis:
    getCardConfig:
      url: /get-card-info
      method: GET
    getBrandInfo:
      url: /get-brand-info
      method: GET
  definitions:
    formCheckboxStatus:
      state:
        onlineChecked: false
        customerTelChecked: false
        sceneMiniProgramChecked: false
        sceneFinderChecked: false
    customerService:
      state:
        customerServiceType: 1
        miniProgram:
          appid: ''
          username: ''
        wecom:
          path: ''
        customize:
          path: '33'
        servicePhone: '138****'
    cardConfigSence:
      state:
        sceneType: 1
        miniProgram:
          sceneId: ''
          appid: ''
          sceneTag: ''
          imageList: []
        finder:
          sceneId: ''
          finderUsername: ''
          sceneTag: ''
          imageList: []
    cardBrief:
      state:
        cardBrief: ''
    brandInfo:
      state:
        brandLogo:
        brandAlias:
        brandShowType:
        brandId:
        brandName:
    errors:
      state: {}
    cardApprove:
      state:
        status: 1
        message: 基础信息审核中，预计将在1个工作日内审核完成，请耐心等待

  # 交互定义
  interactions:
    api_getBrandInfo:
      steps:
        - stage: process
          api: '#/apis/getBrandInfo'
        - stage: handle
          states:
            - '#/definitions/brandInfo/state': '$StepData$.data'
    api_getCardConfig:
      steps:
        - stage: prepare
          from:
            - path: 'card-view'
              as:
                - card-id
        - stage: process
          # 蛮子condition的条件才会触发api请求
          condition:
            conditions:
              - op: in
                value: card-id
          api: '#/apis/getCardConfig'
        - stage: handle
          states:
            # 按页面需求，进行数据解构
            - '#/definitions/cardBrief/state':
                cardBrief: '$StepData$.data.cardBrief'
            - '#/definitions/customerService/state': '$StepData$.data.customerService'
            - '#/definitions/cardConfigSence/state':
                miniProgram: '$StepData$.data.sceneConfig.miniProgram'
                finder: '$StepData$.data.sceneConfig.finder'
            - '#/definitions/formCheckboxStatus/state':
                onlineChecked: $StepData$.data.customerService.onlineChecked
                customerTelChecked: $StepData$.data.customerService.customerTelChecked
                sceneMiniProgramChecked: $StepData$.data.sceneConfig.sceneMiniProgramChecked
                sceneFinderChecked: $StepData$.data.sceneConfig.sceneFinderChecked
          handler:
            before:
              from: userProvide
              path: utils.initState

    renderCardInfo:
      steps:
        - stage: render
          renderType: div
          props:
            class: home-container
          children: # 每个子节点可以通过另一个渲染结果填充
            # - interaction: '#/interactions/renderHeader'
            # - interaction: '#/interactions/renderApproveInfo'
            - interaction: '#/interactions/renderBrandInfo'
            # - interaction: '#/interactions/renderCardConfigForm'
    renderBrandInfo: !include "interactions/brandinfo.yaml"
    renderCardConfigForm:
      steps:
        - stage: render
          from:
            - state: '#/definitions/customerService/state'
              as: customerService
            - state: '#/definitions/cardConfigSence/state'
              as: cardConfigSence
            - state: '#/definitions/formCheckboxStatus/state'
              as: formCheckboxStatus
            - definition: 690
              as: rightColumnWidth
          renderType: div
          
          children:
            - renderType: div
              props:
                class: form-container
              children:
                - renderType: div
                  props:
                    style:
                      display: flex
                      justify-content: space-between
                      margin-bottom: 16px
                  children:
                    - renderType: div
                      props:
                        style:
                          display: flex
                          flex: 1
                          min-width: 0 # Prevent flex item overflow
                          max-width: ${rightColumnWidth}px # Fixed width
                          flex-direction: column
                      children:
                        - renderType: div
                          children:
                            - renderType: div
                              props:
                                class: form-item-row
                              children:
                                - renderType: div
                                  props:
                                    style:
                                      flex: 0 0 160px
                                      margin-right: 16px
                                  children:
                                    - renderType: div
                                      props:
                                        class: null
                                        style:
                                          font-size: 16px
                                          margin: 0
                                      children:
                                        - 品牌信息
                                - renderType: div
                                  props:
                                    style:
                                      display: flex
                                      gap: 24px
                                      align-items: flex-start # Add this for top alignment
                                  children:
                                    - interaction: "#/interactions/renderBrandInfo"

                            - renderType: div
                              props:
                                class: form-item-row
                              children:
                                - renderType: div
                                  props:
                                    style:
                                      flex: 0 0 160px
                                      margin-right: 16px
                                  children:
                                    - renderType: div
                                      props:
                                        class: null
                                        style:
                                          font-size: 16px
                                          margin: 0
                                      children:
                                        - 商家客服
                                - renderType: div
                                  props:
                                    style:
                                      display: flex
                                      gap: 24px
                                      align-items: flex-start # Add this for top alignment
                                  children:
                                    - renderType: div
                                      props:
                                        style:
                                          width: ${rightColumnWidth}px
                                          display: flex
                                          flex-direction: column
                                          gap: 32px
                                      children:
                                        - renderType: div
                                          props:
                                            style:
                                              flex: 0 0 auto
                                              min-width: 0
                                          children:
                                            - interaction: '#/interactions/merchantService'
                            - renderType: div
                              props:
                                class: form-item-row
                              children:
                                - renderType: div
                                  props:
                                    style:
                                      flex: 0 0 160px
                                      margin-right: 16px
                                  children:
                                    - renderType: div
                                      props:
                                        class: null
                                        style:
                                          font-size: 16px
                                          margin: 0
                                      children:
                                        - 场景跳转
                                - renderType: div
                                  props:
                                    style:
                                      display: flex
                                      gap:
                                        24px
                                        # Total width of columns and gap
                                      align-items: flex-start # Add this for top alignment
                                  children:
                                    - renderType: div
                                      props:
                                        style:
                                          width: ${rightColumnWidth}px
                                          display: flex
                                          flex-direction: column
                                          gap: 32px
                                      children:
                                        - renderType: div
                                          props:
                                            style:
                                              flex: 0 0 auto
                                              min-width: 0
                                          children:
                                            - interaction: '#/interactions/merchantScene'
                            - renderType: div
                              props:
                                class: form-item-row
                              children:
                                - renderType: div
                                  props:
                                    style:
                                      flex: 0 0 160px
                                      margin-right: 16px
                                  children:
                                    - renderType: div
                                      props:
                                        class: null
                                        style:
                                          font-size: 16px
                                          margin: 0
                                      children:
                                        - 名片简介
                                - renderType: div
                                  props:
                                    style:
                                      display: flex
                                      gap:
                                        24px
                                        # Total width of columns and gap
                                      align-items: flex-start # Add this for top alignment
                                  children:
                                    - renderType: div
                                      props:
                                        style:
                                          width: ${rightColumnWidth}px
                                          display: flex
                                          flex-direction: column
                                          gap: 32px
                                      children:
                                        - renderType: div
                                          props:
                                            style:
                                              flex: 0 0 auto
                                              min-width: 0
                                          children:
                                            - interaction: '#/interactions/cardBrief'

    # 左边配置项区域
    leftConfig:
      steps:
        - stage: render
          renderType: div
          props:
            class: home-container
          children: # 每个子节点可以通过另一个渲染结果填充
            # - interaction: '#/interactions/renderHeader'
            # - interaction: '#/interactions/renderApproveInfo'
            - interaction: '#/interactions/renderCardConfigForm'
            
            # - interaction: '#/interactions/renderCardConfigForm'

    # 右边预览区域
    rightPreview: !include interactions/rightPreview.yaml
    merchantScene: !include interactions/merchantscene.yaml
    merchantService: !include interactions/merchantservice.yaml
    cardBrief: !include interactions/cardbrief.yaml
    renderHeader: !include interactions/header.yaml
    renderApproveInfo: !include interactions/approve.yaml
