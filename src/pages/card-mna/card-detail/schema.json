{"schema": {"globalStyle": {"formtitle": {"background-color": "#fff", "padding": "16px"}}, "cssClasses": {"main-layout": {"display": "flex", "width": "100%", "height": "100%", "min-height": "600px"}, "left-panel": {"flex": "1", "padding": "24px", "background-color": "#ffffff"}, "right-panel": {"width": "320px", "padding": "16px", "background-color": "#f7f8fa", "border-left": "1px solid #E7E7E7"}, "page-header": {"padding": "16px 24px", "margin-bottom": "16px"}, "config-item": {"margin-bottom": "12px", "background-color": "#FAFBFC", "border-radius": "6px"}, "config-header": {"display": "flex", "align-items": "center", "justify-content": "space-between", "padding": "20px 24px"}, "config-title": {"display": "flex", "align-items": "center", "font-size": "16px", "font-weight": "500"}, "config-body": {"padding": "0px 24px 28px 24px"}, "config-status": {"display": "inline-block", "padding": "0 8px", "border-radius": "2px", "font-size": "12px", "margin-left": "8px", "height": "20px", "line-height": "20px"}, "status-active": {"background-color": "#E8F6EF", "color": "#07C160"}, "status-inactive": {"background-color": "#F1F2F5", "color": "#909399"}, "warning-dot": {"display": "inline-block", "width": "8px", "height": "8px", "border-radius": "50%", "background-color": "#07C160", "margin-right": "10px"}}, "meta": {"icon": "card", "title": "微信支付 品牌经营"}, "layout": "v-leftright-layout-detail", "main": {"interactions": ["#/interactions/initapis", {"path": "#/interactions/leftConfig", "slot": "left"}, {"path": "#/interactions/rightPreview", "slot": "right"}]}, "definitions": {"cardDetail": {"state": {"cardId": "card123456", "cardName": "微信支付商家名片", "status": "active", "data": "include ../service-config/interactions/data.yaml"}}, "configItems": {"state": {"basicInfo": {"completed": true, "lastUpdate": "2023-04-01"}, "serviceInfo": {"completed": true, "lastUpdate": "2023-04-05"}, "brandInfo": {"completed": true, "lastUpdate": "2023-04-10"}, "paymentInfo": {"completed": true, "lastUpdate": "2023-04-12"}, "pageStatus": {"state": {"tutorialPublish": null}}}}}, "interactions": {"leftConfig": {"steps": [{"stage": "render", "renderType": "div", "from": [{"state": "#/definitions/pageStatus/state", "as": "pageStatus"}], "props": {"id": "left-config", "style": {"display": "flex", "flex-direction": "column", "height": "100%"}}, "children": [{"renderType": "div", "props": {"style": {"borderRadius": "16px", "backgroundColor": "#FFFFFF"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "24px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "width": "22px", "height": "22px", "marginRight": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"display": "inline-block", "width": "12px", "height": "12px", "borderRadius": "50%", "backgroundColor": "#07C160"}}, "children": []}]}, {"renderType": "h3", "props": {"style": {"fontSize": "20px", "fontWeight": "500", "margin": "0", "color": "rgba(0, 0, 0, 0.9)"}}, "children": ["初次设置名片，需完成以下功能配置"]}]}, {"interaction": "#/interactions/cardStatus"}]}, {"renderType": "div", "props": {"style": {"borderRadius": "16px", "backgroundColor": "#FFFFFF", "paddingTop": "24px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "24px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "center", "alignItems": "center", "width": "22px", "height": "22px", "marginRight": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"display": "inline-block", "width": "12px", "height": "12px", "borderRadius": "50%", "backgroundColor": "rgba(0, 0, 0, 0.08)"}}, "children": []}]}, {"renderType": "h3", "props": {"style": {"fontSize": "20px", "fontWeight": "500", "margin": "0", "color": "rgba(0, 0, 0, 0.9)"}}, "children": ["完成以上功能配置后，可以发布名片对用户可见"]}]}, {"renderType": "div", "props": {"style": {"marginLeft": "32px", "backgroundColor": "rgba(0, 0, 0, 0.02)", "padding": "24px", "borderRadius": "8px", "display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"fontSize": "16px", "fontWeight": "500", "marginBottom": "8px"}}, "children": ["发布名片"]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "#666", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["完成以上功能配置，手机扫码体验确认无误后，点击发布立即对用户生效"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.canTutorialPublishTheme}", "content": "发布", "disabled": "boolean:${pageStatus.canTutorialPublish}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/card-publish/${pageStatus.processId}?source=publish"}]}}}]}]}]}]}, "rightPreview": {"steps": [{"stage": "render", "renderType": "v-businesscard", "props": {"class": "right-panel", "from": [{"state": "#/definitions/cardDetail/state", "as": "cardDetail"}], "style": {"height": "100%", "background-color": "#f7f8fa", "border-left": "1px solid #E7E7E7", "width": "320px"}, "cardDetail": "${cardDetail}", "data": {"topInfo": {"avatar": "https://dummyimage.com/80x80/222/fff&text=image", "name": "爱马哥123image爱马哥image爱马哥image", "tagsAvatars": ["https://dummyimage.com/18x18/eee/aaa&text=U", "https://dummyimage.com/18x18/eee/aaa&text=U", "https://dummyimage.com/80x80/222/fff&text=image"], "tags": ["等1000+人喜欢"]}, "cards": [{"title": "小程序", "desc": "新品上市", "images": ["https://img.js.design/assets/img/65f2e1e2b9b6b7b7b2e8b7b7.png", "https://img.js.design/assets/img/65f2e1e2b9b6b7b7b2e8b7b8.png"]}, {"title": "视频号", "desc": "新发布作品", "images": ["https://img.js.design/assets/img/65f2e1e2b9b6b7b7b2e8b7b9.png"]}], "otherTab": "优惠", "activeTab": "service", "showQRCode": "pop", "serviceList": [], "moreServices": [{"name": "关于image"}, {"name": "新品专辑"}], "likeList": [{"avatarText": "W", "title": "爱马哥爱马哥爱马哥爱马哥爱马哥爱马哥爱马哥", "tags": ["会员专场", "小程序下单", "小程序下单"], "desc": "仅有内容区域的卡片形式。卡片内容区域可以是文字、图片、表单、表格等形式信息内容。可使用大中小不同的卡片尺寸，按业务需求进行呈现。"}, {"avatarText": "W", "title": "无印良品", "tags": ["会员专场", "小程序下单"], "desc": "无印良品的描述内容。"}]}}}]}, "initapis": {"steps": [{"stage": "process", "loading": "#/global/loading", "apis": [["#/apis/GetTutorialPublishProcess", "#/apis/GetLatestCardInfoPublishProcess", "#/apis/GetLatestServicePublishProcess", "#/apis/GetBrandMemberAccessState", "#/apis/GetShakeCouponAccessState"]]}, {"stage": "handle", "handler": {"before": {"from": "userProvide", "path": "tutorial.initPageState"}}, "states": [{"#/definitions/pageStatus/state": "$StepData$"}]}]}, "cardStatus": {"steps": [{"stage": "render", "from": [{"state": "#/definitions/pageStatus/state", "as": "pageStatus"}], "renderType": "div", "props": {"style": {"borderLeft": "2px solid rgba(0, 0, 0, 0.08)", "paddingLeft": "19px", "marginLeft": "11px"}}, "children": [{"renderType": "div", "props": {"style": {"backgroundColor": "rgba(0, 0, 0, 0.02)", "padding": "24px", "marginBottom": "24px", "borderRadius": "8px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "from": [{"state": "#/definitions/configItems/state", "as": "configData"}], "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["基础信息"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "text": {"from": "userProvide", "text": "${pageStatus.cardInfoStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["配置品牌的基础信息，提供电话、在线客服等用户咨询途径，支持连接品牌在微信内的多个场景，如小程序、视频号"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.cardInfoStatusButtonStyle}", "content": "${pageStatus.cardInfoStatusText}", "disabled": "boolean:${pageStatus.canConfigCardInfo}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/card-config"}]}}}]}]}, {"renderType": "div", "props": {"style": {"backgroundColor": "rgba(0, 0, 0, 0.02)", "padding": "24px", "marginBottom": "24px", "borderRadius": "8px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "from": [{"state": "#/definitions/configItems/state", "as": "configData"}], "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["名片服务"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "text": {"from": "userProvide", "text": "${pageStatus.cardServiceStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["向用户提供自定义服务，如官方商城、新品速递、订单查询、售后服务等，提升用户服务质量"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.cardServiceStatusButtonStyle}", "content": "${pageStatus.cardServiceStatusText}", "disabled": "boolean:${pageStatus.canConfigCardService}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/service-config/config"}]}}}]}]}, {"renderType": "div", "props": {"style": {"backgroundColor": "rgba(0, 0, 0, 0.02)", "padding": "24px", "borderRadius": "8px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["品牌会员或品牌优惠"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "children": [{"renderType": "span", "text": {"from": "userProvide", "text": "${pageStatus.brandAndCouponAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["打通品牌会员体系，基于用户在微信支付的消费偏好，打造发现使用优惠的最短路径。品牌会员和品牌优惠至少需要接入其中一项"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.brandAndCouponAccessStatusButtonStyle}", "content": "${pageStatus.brandAndCouponAccessStatusText}", "disabled": "boolean:${pageStatus.canBrandAndCouponAccess}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/brand-vip"}]}}}]}]}]}]}}}}