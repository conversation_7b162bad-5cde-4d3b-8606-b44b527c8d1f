steps:
  - stage: render
    from:
      - state: '#/definitions/pageStatus/state'
        as: pageStatus
    renderType: div
    props:
      style:
        borderLeft: '2px solid rgba(0, 0, 0, 0.08)'
        paddingLeft: '19px'
        marginLeft: '11px'
    children:
      # 基础信息配置项
      - renderType: div
        props:
          style:
            backgroundColor: 'rgba(0, 0, 0, 0.02)'
            padding: '24px'
            marginBottom: '24px'
            borderRadius: '8px'
        children:
          - renderType: div
            props:
              style:
                display: 'flex'
                justifyContent: 'space-between'
                alignItems: 'center'
            from:
              - state: '#/definitions/configItems/state'
                as: 'configData'
            children:
              - renderType: div
                props:
                  style:
                    flex: '1'
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        alignItems: 'center'
                        marginBottom: '8px'
                    children:
                      - renderType: span
                        props:
                          style:
                            fontSize: '16px'
                            fontWeight: '500'
                        children:
                          - 基础信息
                      - renderType: span
                        # 能标明这里应该关联一条处理规则
                        props:
                          style:
                            marginLeft: '8px'
                        text:
                          from: userProvide
                          text: ${pageStatus.cardInfoStatusTipsText}
                          path: tutorial.renderStatusTag
                  - renderType: p
                    props:
                      style:
                        margin: '0'
                        color: 'rgba(0, 0, 0, 0.5)'
                        lineHeight: '22px'
                        fontSize: '14px'
                    children:
                      - 配置品牌的基础信息，提供电话、在线客服等用户咨询途径，支持连接品牌在微信内的多个场景，如小程序、视频号
              - renderType: v-button
                props:
                  theme: ${pageStatus.cardInfoStatusButtonStyle}
                  content: ${pageStatus.cardInfoStatusText}
                  disabled: boolean:${pageStatus.canConfigCardInfo}
                  style:
                    marginLeft: '16px'
                  events:
                    click:
                      # - jump: /home/<USER>/card-config
                      - jump: /home/<USER>/card-config
                        # from:
                        #   - state: "#/definitions/cardDetail/state"

      # 名片服务配置项
      - renderType: div
        props:
          style:
            backgroundColor: 'rgba(0, 0, 0, 0.02)'
            padding: '24px'
            marginBottom: '24px'
            borderRadius: '8px'
        children:
          - renderType: div
            props:
              style:
                display: 'flex'
                justifyContent: 'space-between'
                alignItems: 'center'
            from:
              - state: '#/definitions/configItems/state'
                as: 'configData'
            children:
              - renderType: div
                props:
                  style:
                    flex: '1'
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        alignItems: 'center'
                        marginBottom: '8px'
                    children:
                      - renderType: span
                        props:
                          style:
                            fontSize: '16px'
                            fontWeight: '500'
                        children:
                          - 名片服务
                      - renderType: span
                        props:
                          style:
                            marginLeft: '8px'
                        text:
                          from: userProvide
                          text: ${pageStatus.cardServiceStatusTipsText}
                          path: tutorial.renderStatusTag
                  - renderType: p
                    props:
                      style:
                        margin: '0'
                        color: 'rgba(0, 0, 0, 0.5)'
                        lineHeight: '22px'
                        fontSize: '14px'
                    children:
                      - 向用户提供自定义服务，如官方商城、新品速递、订单查询、售后服务等，提升用户服务质量
              - renderType: v-button
                props:
                  theme: ${pageStatus.cardServiceStatusButtonStyle}
                  content: ${pageStatus.cardServiceStatusText}
                  disabled: boolean:${pageStatus.canConfigCardService}
                  style:
                    marginLeft: '16px'
                  events:
                    click:
                      - jump: /home/<USER>/service-config/config

      # 品牌会员或品牌优惠配置项
      - renderType: div
        props:
          style:
            backgroundColor: 'rgba(0, 0, 0, 0.02)'
            padding: '24px'
            borderRadius: '8px'
        children:
          - renderType: div
            props:
              style:
                display: 'flex'
                justifyContent: 'space-between'
                alignItems: 'center'
            children:
              - renderType: div
                props:
                  style:
                    flex: '1'
                children:
                  - renderType: div
                    props:
                      style:
                        display: 'flex'
                        alignItems: 'center'
                        marginBottom: '8px'
                    children:
                      - renderType: span
                        props:
                          style:
                            fontSize: '16px'
                            fontWeight: '500'
                        children:
                          - 品牌会员或品牌优惠
                      - renderType: span
                        props:
                          style:
                            marginLeft: '8px'
                        children:
                          - renderType: span
                            text:
                              from: userProvide
                              text: ${pageStatus.brandAndCouponAccessStatusTipsText}
                              path: tutorial.renderStatusTag
                  - renderType: p
                    props:
                      style:
                        margin: '0'
                        color: 'rgba(0, 0, 0, 0.5)'
                        lineHeight: '22px'
                        fontSize: '14px'
                    children:
                      - 打通品牌会员体系，基于用户在微信支付的消费偏好，打造发现使用优惠的最短路径。品牌会员和品牌优惠至少需要接入其中一项
              - renderType: v-button
                props:
                  theme: ${pageStatus.brandAndCouponAccessStatusButtonStyle}
                  content: ${pageStatus.brandAndCouponAccessStatusText}
                  disabled: boolean:${pageStatus.canBrandAndCouponAccess}
                  style:
                    marginLeft: '16px'
                  events:
                    click:
                      - jump: /home/<USER>/brand-vip
