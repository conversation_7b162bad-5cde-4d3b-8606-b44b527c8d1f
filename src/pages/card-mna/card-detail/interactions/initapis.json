{"steps": [{"stage": "process", "loading": "#/global/loading", "apis": [["#/apis/GetTutorialPublishProcess", "#/apis/GetLatestCardInfoPublishProcess", "#/apis/GetLatestServicePublishProcess", "#/apis/GetBrandMemberAccessState", "#/apis/GetShakeCouponAccessState"]]}, {"stage": "handle", "handler": {"before": {"from": "userProvide", "path": "tutorial.initPageState"}}, "states": [{"#/definitions/pageStatus/state": "$StepData$"}]}]}