steps: 
  - stage: process
    loading: '#/global/loading'
    apis:
      # 1. 先查GetTutorialPublishProcess接口，
      #   1.1 判断有没有单跳转/home/<USER>/card-overview
      # 2. 分别查询GetCardInfo、GetServiceConfig、GetExposeConfigState、GetBrandMemberAccessState、GetShakeCouponAccessState
      # 3. GetLatestCardInfoPublishProcess、GetLatestServicePublishProcess查询这两个发布单的状态
      # - "#/apis/GetCardInfo"
      # - "#/apis/GetServiceConfig"
      # - "#/apis/GetExposeConfigState"
      -
        - "#/apis/GetTutorialPublishProcess"
        - "#/apis/GetLatestCardInfoPublishProcess"
        - "#/apis/GetLatestServicePublishProcess"
        - "#/apis/GetBrandMemberAccessState"
        - "#/apis/GetShakeCouponAccessState"
  - stage: handle
    handler: 
      before: 
        from: userProvide
        path: tutorial.initPageState
    states:
      # - "#/definitions/tutorialPublishState/state": "$StepData$.0.data"
      # - "#/definitions/cardInfo/state": "$StepData$.1.data"
      # - "#/definitions/serviceConfig/state": "$StepData$.2.data"
      # - "#/definitions/exposeConfigState/state": "$StepData$.3.data"
      # - "#/definitions/brandMemberAccessState/state": "$StepData$.4.data"
      # - "#/definitions/shakeCouponAccessState/state": "$StepData$.5.data"
      - "#/definitions/pageStatus/state": "$StepData$"