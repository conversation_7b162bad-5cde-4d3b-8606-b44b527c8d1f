schema:
  apis:
     # 查询商家名片信息
    GetCardInfo:
      url: /get-card-info
      method: 'get'
    # 查询名片服务
    GetServiceConfig:
      url: /get-service-config
      method: 'GET'
    # 查询曝光配置状态(交易连接名片)
    GetExposeConfigState:
      url: /get-expose-config-state
      method: 'get'
    # 品牌接入状态
    GetBrandMemberAccessState:
      url: /get-brand-member-access-state
      method: 'get'
    # 获取摇优惠接入状态
    GetShakeCouponAccessState:
      url: /get-shake-coupon-access-state
      method: 'get'
    # 查询以上接口，如果有一项没有配置则进入新手引导
    # 前两项如果没有会直接报错，返回错误码。
    # 后三项会返回是否有的状态枚举

    # 查询最新新手引导发布单
    GetTutorialPublishProcess:
      url: /get-latest-tutorial-publish-process
      method: 'get'
    # 创建发布单(先查询，没有再创建)
    CreateTutorialPublishProcess:
      url: /create-tutorial-publish-process
      method: 'post'

    # 查询最新名片信息发布单（用户展示基础信息的发布单状态）
    GetLatestCardInfoPublishProcess:
      url: /get-latest-card-info-publish-process
      method: 'get'
    # 查询最新名片服务发布单（用于展示名片服务的发布状态）
    GetLatestServicePublishProcess:
      url: /get-latest-service-publish-process
      method: 'get'
    # 创建名牌信息发布单
    CreateCardInfoPublishProcess:
      url: /create-card-info-publish-process
      method: 'post'
 

    # 提交新手引导发布单
    SubmitTutorialPublishProcess:
      method: 'post'
      url: /submit-tutorial-publish-process
  concepts:
    merchantCard:
      cardId:
        type: string
        title: 名片ID
        rules:
          - required: true
      cardName:
        type: string
        title: 名片名称
        rules:
          - required: true
          - maxLength: 30
            error: 名片名称不能超过30个字符
      cardType:
        type: string
        title: 名片类型
        enum:
          - transaction
          - service
          - promotion
        enumNames:
          - 交易连接名片
          - 商品服务名片
          - 宣传推广名片
        rules:
          - required: true
      status:
        type: string
        title: 状态
        enum:
          - active
          - inactive
          - pending
        enumNames:
          - 已启用
          - 已停用
          - 审核中
        rules:
          - required: true
      createTime:
        type: string
        title: 创建时间
        format: date-time
      updateTime:
        type: string
        title: 更新时间
        format: date-time
    customerService:
      customerServiceType:
        type: number
        title: 客服类型
        default: ''
        enum:
          - 3 # 品牌自建客服（网页）
          - 4 # 品牌自建客服（小程序）
          - 2 # 企业微信客服
          - 1 # 小程序客服组件
        enumNames:
          - 品牌自建客服（网页）
          - 品牌自建客服（小程序）
          - 企业微信客服
          - 小程序客服组件
        enumDescriptions:
          - 品牌自主或通过第三方搭建的客服系统，在网页内向用户提供咨询服务
          - 品牌自主或通过第三方搭建的客服系统，在小程序内向用户提供咨询服务
          - "企业微信官方提供的微信客服组件 [企业微信客服组件指南](https://developer.work.weixin.qq.com/document/path/99370){color=#4848dd;text-decoration: none}"
          - "小程序官方提供的客服组件 [小程序客服消息使用指南](https://developers.weixin.qq.com/miniprogram/introduction/custom.html){color=#4848dd;text-decoration: none}"
      customizeWeb:
        type: object
        properties:
          path:
            title: 网页客服链接
            type: string
            rules:
              - pattern: '^https?:\/\/((?:www\.)?((?!-)[A-Za-z0-9\u4e00-\u9fa5\uac00-\ud7af\u1100-\u11ff\u3130-\u318f\u30a0-\u30ff\u31f0-\u31ff\u4e00-\u9faf-]{1,63}(?<!-)\.)+[A-Za-z\u4e00-\u9fa5\uac00-\ud7af\u1100-\u11ff\u3130-\u318f\u30a0-\u30ff\u31f0-\u31ff\u4e00-\u9faf]{2,})((\/[-a-zA-Z0-9@:%_+.~#?&!*();=$,\[\]\u4e00-\u9fa5\u3130-\u318f\u30a0-\u30ff\u31f0-\u31ff\uac00-\ud7af\u1100-\u11ff\u4e00-\u9faf]*)*\/?)$'
                error: 请输入有效的网页链接
      customizeMp:
        type: object
        properties:
          appid:
            title: 小程序AppID
            type: string
            dataSource: "#/apis/getMiniprogramList"
            dataMap:
              value: appid
              label: appName
              items: data.brandAppidRelationList
          path:
            title: 跳转路径
            type: string
            rules:
            - pattern: '^[^/\s][^\s?#]*$'
              error: '请输入有效的小程序路径，不能以/开头'
      wecom:
        type: object
        properties:
          path:
            title: 企业微信客服账号链接
            rules:
            - pattern: '^(https?|http)\:\/\/work\.weixin\.qq\.com[^\s]*$'
              error: '请输入有效的企业微信客服链接'
      miniProgram:
        type: object
        properties:
          appid:
            dataSource: "#/apis/getMiniprogramList"
            type: string
            dataMap:
              value: appid
              label: appName
              items: data.brandAppidRelationList
      servicePhone:
        type: string
        title: 客服电话号码
        rules:
        - pattern: '^(1[3-9]\d{9}|400[0-9]{7}|(0\d{2,3}-)?[1-9]\d{6,7}(-\d{1,6})?)$'
          error: '请输入有效的手机号、400服务号或座机号' 
    merchantScene:
      sceneType:
        type: string
        title: 名片类型
        enum:
          - 1
          - 2
        enumNames:
          - 小程序
          - 视频号
        rules:
          - required: true
      miniProgram:
        type: object
        properties:
          appid:
            type: string
            dataSource: "#/apis/getMiniprogramList"
            dataMap:
              value: appid
              label: appName
              items: data.brandAppidRelationList
          sceneTag:
            type: string
            default: 前往小程序
            rules: 
            - maxLength: 10
              error: 场景标签不能超过10个字符
            - pattern: ^[0-9a-zA-Z\u4e00-\u9fff]+$
              error: 场景标签为数字、中英文字符
          imageList:
            type: array
            items: 
              type: string
      finder:
        type: object
        properties:
          finderUsername:
            type: string
            dataSource: "#/apis/getFinderList"
            dataMap:
              value: finderUsername
              label: finderNickname
              items: data.brandFinderRelationList
          sceneTag:
            type: string
            default: 前往视频号
            rules: 
            - maxLength: 10
              error: 场景标签不能超过10个字符
            - pattern: "^[0-9a-zA-Z\u4e00-\u9fff]+$"
              error: 场景标签为数字、中英文字符
          imageList:
            type: array
            items: 
              type: string
    cardBrief:
      type: string
  global:
    loading:
      type: v-page-loading
      props:
        text: 加载中...
        size: '30'
        color: '#409eff'
        fullscreen: true
    error:
      type: v-error
      props:
        content: error handling...
        visible: true
        duration: 5000
        theme: error
        style:
          position: fixed
          left: 50%
          transform: translate(-50%, 200px)
  interactions:
    commonError:
      steps:
      - stage: render
        renderType: v-dialog
        children: 
        - renderType: h2
          props: 
            style: 
              color: red
          children:
          - $StepData$.message
