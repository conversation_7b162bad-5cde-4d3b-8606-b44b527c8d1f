import { request } from '@/utils/request';

export type ButtonStatus = boolean;
export type ConfigStatus = '已配置' | '未配置' | '已接入' | '未接入';
export type ConfigResult = {
  status: boolean;
  text: string;
  tipsText: string;
  buttonStyle?: string;
};
export enum ProcessState {
  INIT = 0, // 初始状态
  DRAFT = 1, // 草稿
  AUDITING = 2, // 待审核
  AUDIT_PASS = 3, // 审核通过
  REJECTED = 4, // 审核驳回
  CANCELED = 5, // 已作废
  APPROVED = 6, // 待发布
  PUBLISHING = 7, // 发布中
  PUBLISHED = 8, // 已完成
}

export enum PublishType {
  MERCHANT_CARD_PUBLISH_TYPE_NONE,
  MERCHANT_CARD_PUBLISH_TYPE_IMMEDIATELY, // 立即发布
  MERCHANT_CARD_PUBLISH_TYPE_SCHEDULED, // 定时发布
}
// 对应状态：
// 配置: 0，5，8
// 查看：2，3，6，7，
// 修改：1，4，

// https://xcontract.woa.com/#/contract/proto-manage/modify?protoId=11768
// state，0为缺省，1为已接入，2为未接入
export enum state {
  NONE = 0,
  OPEN = 1,
  CLOSE = 2,
}

// 检查是否有任何API调用返回错误
const hasAnyError = (responses: any[]): boolean => {
  return responses.some((response) => response.code);
};

// 检查进程状态是否为已通过审核或已发布
const isProcessApprovedOrPublished = (processData: any): boolean => {
  const processState = processData?.data?.process?.state;
  return (
    processState === ProcessState.AUDIT_PASS || processState === ProcessState.PUBLISHED
  );
};

// 检查访问权限是否已开启
const isAccessOpen = (accessData: any): boolean => {
  return accessData?.data?.state === state.OPEN;
};

// 检查所有条件是否满足发布要求
const areAllConditionsMet = (
  latestCardInfoPublishProcess: any,
  latestServicePublishProcess: any,
  brandMemberAccess: any,
  shakeCouponAccess: any
): boolean => {
  return (
    isProcessApprovedOrPublished(latestCardInfoPublishProcess) &&
    isProcessApprovedOrPublished(latestServicePublishProcess) &&
    (isAccessOpen(brandMemberAccess) || isAccessOpen(shakeCouponAccess))
  );
};

// 新手发布单
export const calcPublishStatus = (data: any): ButtonStatus => {
  const [
    tutorialPublish,
    latestCardInfoPublishProcess,
    latestServicePublishProcess,
    brandMemberAccess,
    shakeCouponAccess,
  ] = data;

  // 任意一个返回异常，都禁用
  if (
    hasAnyError([
      tutorialPublish,
      latestCardInfoPublishProcess,
      latestServicePublishProcess,
      brandMemberAccess,
      shakeCouponAccess,
    ])
  ) {
    return true;
  }

  // 检查所有条件是否满足
  if (
    areAllConditionsMet(
      latestCardInfoPublishProcess,
      latestServicePublishProcess,
      brandMemberAccess,
      shakeCouponAccess
    )
  ) {
    return false;
  }

  return true;
};

// 定义状态组类型
type StateGroup = 'configure' | 'view' | 'auditing' | 'rejected' | 'draft' | 'approved';

// 处理配置状态的通用函数
const getCardConfigResult = (
  data: Record<string, any>,
  stateToConfig: Record<StateGroup, { text: string; tipsText: string }>,
  isVeteran: boolean
): ConfigResult => {
  // 检查错误或无效状态
  if (
    data.code ||
    !data.data?.process?.state ||
    [ProcessState.INIT, ProcessState.CANCELED, ProcessState.PUBLISHED].includes(
      data.data?.process?.state
    )
  ) {
    if (isVeteran) {
      return {
        status: false,
        ...stateToConfig.view,
      };
    }
    return {
      status: false,
      ...stateToConfig.configure,
    };
  }

  const processState = data.data.process.state;
  const stateGroup = getProcessStateGroup(processState);

  return {
    status: false,
    ...stateToConfig[stateGroup],
  };
};

// 将进程状态映射到状态组
const getProcessStateGroup = (processState: ProcessState): StateGroup => {
  // 查看状态
  if ([ProcessState.AUDIT_PASS, ProcessState.PUBLISHING].includes(processState)) {
    return 'view';
  }

  if ([ProcessState.DRAFT].includes(processState)) {
    return 'draft';
  }

  // 审核中
  if ([ProcessState.AUDITING].includes(processState)) {
    return 'auditing';
  }

  if ([ProcessState.APPROVED].includes(processState)) {
    return 'approved';
  }

  // 驳回
  if ([ProcessState.REJECTED].includes(processState)) {
    return 'rejected';
  }

  // 配置状态 (默认)
  return 'configure';
};
const stateToConfig = {
  configure: { tipsText: '未配置', text: '配置', buttonStyle: 'primary' },
  draft: { tipsText: '配置中', text: '配置', buttonStyle: 'primary' },
  auditing: { tipsText: '审核中', text: '查看', buttonStyle: 'default' },
  approved: { tipsText: '审核通过', text: '查看', buttonStyle: 'default' },
  rejected: { tipsText: '审核驳回', text: '修改', buttonStyle: 'primary' },
  view: { tipsText: '已配置', text: '查看', buttonStyle: 'default' },
};

export const calcCardInfoConfig = (
  cardInfo: Record<string, any>,
  isVeteran: boolean
): ConfigResult => {
  return getCardConfigResult(cardInfo, stateToConfig, isVeteran);
};

export const calcCardServiceConfig = (
  cardService: Record<string, any>,
  isVeteran: boolean
): ConfigResult => {
  return getCardConfigResult(cardService, stateToConfig, isVeteran);
};

export const calcBrandOrCouponAccessConfig = (
  brandMemberAccess: Record<string, any>,
  shakeCouponAccess: Record<string, any>
): ConfigResult => {
  if (
    brandMemberAccess.data?.state === state.OPEN ||
    shakeCouponAccess.data?.state === state.OPEN
  ) {
    return {
      status: false,
      text: '查看',
      tipsText: '已接入',
      buttonStyle: 'default',
    };
  }

  return {
    status: false,
    text: '接入',
    tipsText: '未接入',
    buttonStyle: 'primary',
  };
};

export const calcBrandAccessConfig = (
  brandMemberAccess: Record<string, any>
): ConfigResult => {
  if (brandMemberAccess.code || brandMemberAccess.data?.state !== state.OPEN) {
    return {
      status: true,
      text: '接入',
      buttonStyle: 'primary',
      tipsText: '未接入',
    };
  }
  return {
    status: false,
    text: '查看',
    buttonStyle: 'default',
    tipsText: '已接入',
  };
};

export const calcCouponAccessConfig = (
  shakeCouponAccess: Record<string, any>
): ConfigResult => {
  if (shakeCouponAccess.code || shakeCouponAccess.data?.state !== state.OPEN) {
    return {
      status: true,
      text: '接入',
      buttonStyle: 'primary',
      tipsText: '未接入',
    };
  }
  return {
    status: false,
    buttonStyle: 'default',
    text: '查看',
    tipsText: '已接入',
  };
};

// export const calcCardServiceStatusText = (data: any): ConfigStatus => '未配置';

type ConfigItem = {
  title: string;
  status: number;
  description?: string;
};

export const configItems = (): ConfigItem[] => [
  {
    title: '基础信息',
    status: 0, // 依赖后台数据
  },
  {
    title: '',
    status: 0,
  },
  {
    title: '',
    status: 0,
  },
];

const __cached: Record<string, any> = {};
export const getLatestTutorialPublishProcess = async () => {
  if (__cached['/get-latest-tutorial-publish-process']) {
    return __cached['/get-latest-tutorial-publish-process'];
  }
  try {
    const res = await request.get('/get-latest-tutorial-publish-process');
    if (res && res.code === 0 && res.data?.process) {
      __cached['/get-latest-tutorial-publish-process'] = res.data?.process;
      return res.data?.process;
    }
    return { processId: '' };
  } catch (error) {
    console.error('获取新手发布单状态失败:', error);
  }
};

export const getIsFirst = async () => {
  try {
    const res = await request.get('/get-latest-tutorial-publish-process');

    if (res && res.code === 0 && res.data?.process) {
      __cached['/get-latest-tutorial-publish-process'] = res.data?.process;
      const { processId: id, state } = res.data.process;

      // 有id且 是废弃的 或者 是完成的5 8
      if (id && (state === ProcessState.PUBLISHED || state === ProcessState.CANCELED)) {
        return false;
      }
    }
    return true;
  } catch (error) {
    console.error('获取新手发布单状态失败:', error);
    return true;
  }
};
