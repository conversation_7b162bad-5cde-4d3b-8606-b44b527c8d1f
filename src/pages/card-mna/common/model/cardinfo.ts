import { request } from '@/utils/request';

export const createCardInfoPublishProcess = async () => {
  const url = '/create-card-info-publish-process';
  const created = await request.post({
    url,
    headers: {
      'content-type': 'application/json',
      accept: 'application/json',
    },
  });
  return {
    ...created,
  };
};

export const getCardInfo = async () => {
  const url = '/get-card-info';
  const cardInfo = await request.get({
    url,
    headers: {
      'content-type': 'application/json',
      accept: 'application/json',
    },
  });
  return {
    ...cardInfo,
  };
};

export const getLatestCardInfoPublishProcess = async () => {
  const url = '/get-latest-card-info-publish-process';
  const lastestCardInfo = await request.get({
    url,
    headers: {
      'content-type': 'application/json',
      accept: 'application/json',
    },
  });
  return {
    ...lastestCardInfo,
  };
};
