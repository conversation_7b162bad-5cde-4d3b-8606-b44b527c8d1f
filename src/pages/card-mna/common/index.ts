import {
  componentRegistry,
  useObjectRegistry,
  useRouterRegistry,
  useRequestProxyRegistry,
  useGlobalStateStore,
  getEntityByRefPath,
} from '@tencent/dsl-page';
import CardMnaLayoutLayout from '../layout/cardmnalayout.vue';
import leftRightLayout from '../layout/leftRight.vue';
import { request } from '../../../utils/request';
import {
  createCardInfoPublishProcess,
  getCardInfo,
  getLatestCardInfoPublishProcess,
} from './model/cardinfo';
import BusinessCard from '../../../components/BusinessCard/index.ts';
import CardService from '../../../components/CardService/index.ts';
import ServiceStatusAlert from '../../../components/CardService/compontents/ServiceStatusAlert.vue';
import MiniprogramSelectEmpty from '../../../components/miniprogram-select-empty.vue';
import FinderSelectEmpty from '../../../components/finder-select-empty.vue';
import StatePreview from '../../../components/state-preview/state-preview.vue';
import leftRightDetailLayout from '../layout/leftRightDetail.vue';
import CardView from '../../../components/card-view/card-view.vue';

import '@tencent/rod-vue-components/dist/style.css';
import Thumb from '@/components/thumb.vue';
import TimeIcon from '@/components/time-icon.vue';
import img1 from '@/assets/images/cardmna/1.png';
import img2 from '@/assets/images/cardmna/2.png';
import img3 from '@/assets/images/cardmna/3.png';
import img4 from '@/assets/images/cardmna/4.png';
import img5 from '@/assets/images/cardmna/5.jpg';
import img6 from '@/assets/images/cardmna/6.png';
import img01 from '@/assets/images/cardmna/01.png';
import img02 from '@/assets/images/cardmna/02.png';
import img03 from '@/assets/images/cardmna/03.png';
import img04 from '@/assets/images/cardmna/04.png';
import img05 from '@/assets/images/cardmna/05.png';
import img06 from '@/assets/images/cardmna/06.png';

import {
  calcPublishStatus,
  calcCardInfoConfig,
  calcCardServiceConfig,
  calcBrandOrCouponAccessConfig,
  calcCouponAccessConfig,
  calcBrandAccessConfig,
  getIsFirst,
  type ConfigStatus,
} from './model/tutorial';
import { h, watch } from 'vue';

componentRegistry.registerBatch({
  'v-simplelayout': CardMnaLayoutLayout,
  'v-leftright-layout': leftRightLayout,
  'v-thumb': Thumb,
  'v-time-icon': TimeIcon,
  'v-leftright-layout-detail': leftRightDetailLayout,
  'v-service-status-alert': ServiceStatusAlert,
  'v-miniprogram-select-empty': MiniprogramSelectEmpty,
  'v-finder-select-empty': FinderSelectEmpty,
  'v-card-view': CardView,
});

componentRegistry.register('v-businesscard', BusinessCard);
componentRegistry.register('v-cardservice', CardService);
componentRegistry.register('v-state-preview', StatePreview);
const { registerObjects } = useObjectRegistry();
// const { getRegisteredRouter } = useRouterRegistry();

const { registerRequestProxy } = useRequestProxyRegistry();

registerRequestProxy(async (config) => {
  try {
    if (typeof config === 'string') {
      return await request.get(config);
    }
    if (typeof config === 'object') {
      config.headers && (config.headers.Accept = 'application/json');
      if (config.method.toLowerCase() === 'get') {
        return await request.get(config);
      }
      // 确保data被正确处理
      if (config.data !== undefined) {
        config.body = JSON.stringify(config.data);
      }
      return await request.post(config);
    }
  } catch (e) {
    return e;
  }
});

registerObjects('asserts', {
  img1,
  img2,
  img3,
  img4,
  img5,
  img6,
  img01,
  img02,
  img03,
  img04,
  img05,
  img06,
});

enum CustomerServiceType {
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_NONE, // 不使用
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM, // 小程序客服
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM, // 企业微信客服
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_WEB, // 自建客服（网页）
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_MP, // 自建客服（小程序）
  MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_SERVICE_PHONE, // 联系电话
}

enum SceneType {
  MERCHANT_CARD_SCENE_TYPE_NONE,
  MERCHANT_CARD_SCENE_TYPE_MP,
  MERCHANT_CARD_SCENE_TYPE_FINDER,
}

const formatDate = (date: string | Date) => {
  // Parse input date (support both string and Date object)
  const d = typeof date === 'string' ? new Date(date) : date;

  // Check if date is valid
  if (Number.isNaN(d.getTime())) {
    console.warn('Invalid date:', date);
    return 'Invalid Date';
  }

  // Format date components with leading zeros
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, '0');
  const day = d.getDate().toString().padStart(2, '0');
  const hours = d.getHours().toString().padStart(2, '0');
  const minutes = d.getMinutes().toString().padStart(2, '0');
  const seconds = d.getSeconds().toString().padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

registerObjects('enums', CustomerServiceType);

registerObjects('events', {
  // 声明周期
  onCardConfigMainFinished: () => {
    const globalState = useGlobalStateStore();
    watch(
      () => {
        return {
          customerService: globalState.getStoreSync(
            '#/definitions/customerService/state'
          ),
          formCheckboxStatus: globalState.getStoreSync(
            '#/definitions/formCheckboxStatus/state'
          ),
          cardConfigSence: globalState.getStoreSync(
            '#/definitions/cardConfigSence/state'
          ),
        };
      },
      (newState) => {
        if (
          !newState.customerService ||
          !newState.formCheckboxStatus ||
          !newState.cardConfigSence
        ) {
          return;
        }
        const serviceFormDataState = '#/definitions/customerService/state';
        const cardConfigSenceState = '#/definitions/cardConfigSence/state';
        const userActonState = '#/definitions/userAction/state';
        const userAction = globalState.getStoreSync(userActonState);
        const serviceFormData = globalState.getStoreSync(serviceFormDataState);
        const isEqual =
          deepEqual(newState.customerService, serviceFormData) &&
          deepEqual(newState.cardConfigSence, cardConfigSenceState);
        console.log(isEqual);
        let saveButtonDisbaled;
        if (isEqual) {
          saveButtonDisbaled = true;
        }
        saveButtonDisbaled = !execValidate();
        globalState.updateSync(userActonState, {
          ...userAction,
          saveButtonDisbaled,
        });
      },
      { immediate: true }
    );
    watch(
      () => globalState.getStoreSync('#/definitions/merchantServiceCategory/state'),
      (newState) => {
        if (newState && newState.category === 1) {
          const serviceFormDataState = '#/definitions/customerService/state';
          const serviceFormData = globalState.getStoreSync(serviceFormDataState);
          if (serviceFormData?.customerServiceType === 5) {
            globalState.updateSync(serviceFormDataState, {
              ...serviceFormData,
              customerServiceType: '',
            });
          }
        }
      }
    );
  },
});

// registerObjects('home', {
//   handleApiError: (globalState, errorData) => {=> {
//     const router = getRegisteredRouter();
//     console.error('API Error:', errorData);
//     // 根据错误类型处理
//     if (errorData.code === 0 && errorData.data?.processId) {
//       router.push('/home/<USER>/card-detail');
//     }
//   },
// });
registerObjects('tutorial', {
  initPageState: (apisResult) => {
    const [
      tutorialPublish,
      latestCardInfoPublishProcess,
      latestServicePublishProcess,
      brandMemberAccess,
      shakeCouponAccess,
    ] = apisResult[0];

    const cardInfoStatus = calcCardInfoConfig(latestCardInfoPublishProcess, false);
    const cardServiceStatus = calcCardServiceConfig(latestServicePublishProcess, false);
    const brandAndCouponAccessStatus = calcBrandOrCouponAccessConfig(
      brandMemberAccess,
      shakeCouponAccess
    );

    const canTutorialPublish = calcPublishStatus(apisResult[0]);

    const result = {
      processId: tutorialPublish.data?.process?.processId,
      canTutorialPublishTheme: canTutorialPublish ? '' : 'primary',
      canTutorialPublish,

      canConfigCardInfo: cardInfoStatus.status,
      cardInfoStatusText: cardInfoStatus.text,
      cardInfoStatusButtonStyle: cardInfoStatus.buttonStyle,
      cardInfoStatusTipsText: cardInfoStatus.tipsText,

      canConfigCardService: cardServiceStatus.status,
      cardServiceStatusText: cardServiceStatus.text,
      cardServiceStatusButtonStyle: cardServiceStatus.buttonStyle,
      cardServiceStatusTipsText: cardServiceStatus.tipsText,

      canBrandAndCouponAccess: brandAndCouponAccessStatus.status,
      brandAndCouponAccessStatusText: brandAndCouponAccessStatus.text,
      brandAndCouponAccessStatusButtonStyle: brandAndCouponAccessStatus.buttonStyle,
      brandAndCouponAccessStatusTipsText: brandAndCouponAccessStatus.tipsText,
    };

    return result;
  },
  renderStatusTag: (xx: ConfigStatus) => {
    // 定义颜色映射
    const colorMap = {
      审核中: { color: '#2A9EFF' }, // primary 蓝色
      已接入: { color: '#07C160' }, // success 绿色
      已配置: { color: '#07C160' }, // success 绿色
      审核通过: { color: '#07C160' }, // success 绿色
      审核驳回: { color: '#F14752' }, // danger 红色
      未配置: { color: 'rgba(0, 0, 0, 0.3)' }, // 默认灰色
      未接入: { color: 'rgba(0, 0, 0, 0.3)' }, // 默认灰色
    };

    const style = colorMap[xx] || { color: 'rgba(0, 0, 0, 0.3)' };

    return h(
      'span',
      {
        style: {
          display: 'inline-block',
          padding: '2px 8px',

          fontSize: '16px',
          fontWeight: '400',
          lineHeight: '20px',
          ...style,
        },
      },
      xx
    );
  },
});

registerObjects('dashboard', {
  initPageState: (apisResult) => {
    const [
      latestCardInfoPublishProcess,
      latestServicePublishProcess,
      brandMemberAccess,
      shakeCouponAccess,
    ] = apisResult[0];

    const cardInfoStatus = calcCardInfoConfig(latestCardInfoPublishProcess, true);
    const cardServiceStatus = calcCardServiceConfig(latestServicePublishProcess, true);
    const brandAccessStatus = calcBrandAccessConfig(brandMemberAccess);
    const couponAccessStatus = calcCouponAccessConfig(shakeCouponAccess);

    return {
      canConfigCardInfo: cardInfoStatus.status,
      cardInfoStatusText: cardInfoStatus.text,
      cardInfoStatusButtonStyle: cardInfoStatus.buttonStyle,
      cardInfoStatusTipsText: cardInfoStatus.tipsText,

      canConfigCardService: cardServiceStatus.status,
      cardServiceStatusText: cardServiceStatus.text,
      cardServiceStatusButtonStyle: cardServiceStatus.buttonStyle,
      cardServiceStatusTipsText: cardServiceStatus.tipsText,

      canBrandAccess: brandAccessStatus.status,
      brandAccessStatusText: brandAccessStatus.text,
      brandAccessStatusButtonStyle: brandAccessStatus.buttonStyle,
      brandAccessStatusTipsText: brandAccessStatus.tipsText,

      canCouponAccess: couponAccessStatus.status,
      couponAccessStatusText: couponAccessStatus.text,
      couponAccessStatusButtonStyle: couponAccessStatus.buttonStyle,
      couponAccessStatusTipsText: couponAccessStatus.tipsText,
    };
  },
});

registerObjects('brandVip', {
  initPageState: (apisResult) => {
    const [brandMemberAccess, shakeCouponAccess] = apisResult[0];
    const brandAccessStatus = calcBrandAccessConfig(brandMemberAccess);
    const couponAccessStatus = calcCouponAccessConfig(shakeCouponAccess);

    return {
      canBrandAccess: brandAccessStatus.status,
      brandAccessStatusTipsText: brandAccessStatus.tipsText,

      canCouponAccess: couponAccessStatus.status,
      couponAccessStatusTipsText: couponAccessStatus.tipsText,
    };
  },
});

registerObjects('debug', {
  logStart: () => {
    console.log('CreateNewTutorial started');
  },
  logResponse: (_, responseData) => {
    console.log('API Response:', responseData);
  },
});

const execValidate = () => {
  const globalState = useGlobalStateStore();
  const customerServiceState = '#/definitions/customerService/state';
  const cardConfigSenceState = '#/definitions/cardConfigSence/state';
  const merchantServiceCategoryState = '#/definitions/merchantServiceCategory/state';
  const cardConfigSenceOptionState = '#/definitions/formCheckboxStatus/state';

  const customerService = globalState.getStoreSync(customerServiceState);
  const cardConfigSence = globalState.getStoreSync(cardConfigSenceState);
  const merchantServiceCategory = globalState.getStoreSync(merchantServiceCategoryState);
  const cardConfigSenceOption = globalState.getStoreSync(cardConfigSenceOptionState);

  const customerServiceFieldConfig = getEntityByRefPath('#/concepts/customerService');
  const cardConfigSenceFieldConfig = getEntityByRefPath('#/concepts/merchantScene');

  if (merchantServiceCategory.category === 2) {
    const realValue = customerService.servicePhone;
    const { rules } = customerServiceFieldConfig.servicePhone;

    // 验证客服电话号码
    if (!realValue) {
      return false;
    }
    if (rules && rules.length > 0) {
      const phoneRule = rules.find((rule) => rule.pattern);
      if (phoneRule && realValue) {
        const phoneRegex = new RegExp(phoneRule.pattern);
        if (!phoneRegex.test(realValue)) {
          return false;
        }
      }
    }
  } else {
    const { customerServiceType } = customerService;
    if (!customerServiceType) {
      return false;
    }
    if (
      customerServiceType ===
      CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_WEB
    ) {
      const realValue = customerService.customizeWeb.path;
      const { rules } = customerServiceFieldConfig.customizeWeb.properties.path;
      if (!realValue) {
        return false;
      }
      if (rules && rules.length > 0) {
        const phoneRule = rules.find((rule) => rule.pattern);
        if (phoneRule && realValue) {
          const phoneRegex = new RegExp(phoneRule.pattern);
          if (!phoneRegex.test(realValue)) {
            return false;
          }
        }
      }
    }
    if (
      customerServiceType ===
      CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_MP
    ) {
      // 验证品牌自建客服（小程序）
      const realValue = customerService.customizeMp;
      if (!realValue?.path) {
        return false;
      }
      const { rules } = customerServiceFieldConfig.customizeMp.properties.path;
      if (rules && rules.length > 0) {
        const pathRule = rules.find((rule: any) => rule.pattern);
        if (pathRule && realValue.path) {
          const pathRegex = new RegExp(pathRule.pattern);
          if (!pathRegex.test(realValue.path)) {
            return false;
          }
        }
      }
    }
    if (
      customerServiceType ===
      CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM
    ) {
      // 验证企业微信客服
      const realValue = customerService.wecom;
      if (!realValue?.path) {
        return false;
      }
      const { rules } = customerServiceFieldConfig.wecom.properties.path;
      if (rules && rules.length > 0) {
        const pathRule = rules.find((rule: any) => rule.pattern);
        if (pathRule && realValue.path) {
          const pathRegex = new RegExp(pathRule.pattern);
          if (!pathRegex.test(realValue.path)) {
            return false;
          }
        }
      }
    }
    if (
      customerServiceType ===
      CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM
    ) {
      // 验证小程序客服组件
      const realValue = customerService.miniProgram;
      if (!realValue?.appid) {
        return false;
      }
    }
  }
  if (cardConfigSenceOption.sceneMiniProgramChecked) {
    // 验证小程序场景配置
    const realValue = cardConfigSence.miniProgram;
    if (!realValue?.appid) {
      return false;
    }

    // 验证场景标签长度
    if (realValue.sceneTag) {
      const { rules } = cardConfigSenceFieldConfig.miniProgram.properties.sceneTag;
      if (rules && rules.length > 0) {
        const maxLengthRule = rules.find((rule: any) => rule.maxLength);
        if (maxLengthRule && realValue.sceneTag.length > maxLengthRule.maxLength) {
          return false;
        }
      }
    }
  }
  if (cardConfigSenceOption.sceneFinderChecked) {
    // 验证视频号场景配置
    const realValue = cardConfigSence.finder;
    if (!realValue?.finderUsername) {
      return false;
    }

    // 验证场景标签长度
    if (realValue.sceneTag) {
      const { rules } = cardConfigSenceFieldConfig.finder.properties.sceneTag;
      if (rules && rules.length > 0) {
        const maxLengthRule = rules.find((rule: any) => rule.maxLength);
        if (maxLengthRule && realValue.sceneTag.length > maxLengthRule.maxLength) {
          return false;
        }
      }
    }
  }
  return true;
};

registerObjects('configPage', {
  initPublishProcess: async () => {
    const isfirst = await getIsFirst();
    const getCustomerServiceState = (customerServiceList) => {
      const hasServicePhone = customerServiceList?.some(
        (item) =>
          item.customerServiceType ===
          CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_SERVICE_PHONE
      );

      return {
        category: hasServicePhone ? 2 : 1,
        customerServiceType: customerServiceList?.[0]?.customerServiceType,
        miniProgram: customerServiceList?.find(
          (item) =>
            item.customerServiceType ===
            CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM
        )?.miniProgram,
        wecom: customerServiceList?.find(
          (item) =>
            item.customerServiceType ===
            CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM
        )?.wecom,
        customizeWeb: customerServiceList?.find(
          (item) =>
            item.customerServiceType ===
            CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_WEB
        )?.customizeWeb,
        customizeMp: customerServiceList?.find(
          (item) =>
            item.customerServiceType ===
            CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_MP
        )?.customizeMp,
        servicePhone: customerServiceList?.find(
          (item) =>
            item.customerServiceType ===
            CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_SERVICE_PHONE
        )?.servicePhone,
      };
    };

    const getSceneConfigState = (sceneList) => ({
      sceneMiniProgramChecked: sceneList?.some(
        (item) => item.sceneType === SceneType.MERCHANT_CARD_SCENE_TYPE_MP
      ),
      sceneFinderChecked: sceneList?.some(
        (item) => item.sceneType === SceneType.MERCHANT_CARD_SCENE_TYPE_FINDER
      ),
      miniProgram: sceneList?.find(
        (item) => item.sceneType === SceneType.MERCHANT_CARD_SCENE_TYPE_MP
      )?.miniProgram,
      finder: sceneList?.find(
        (item) => item.sceneType === SceneType.MERCHANT_CARD_SCENE_TYPE_FINDER
      )?.finder,
    });

    const createDefaultState = (isfirst) => ({
      status: 0,
      isfirst,
      publishType: 0,
      customerService: {
        category: 1,
        customerServiceType: '',
        miniProgram: null,
        wecom: null,
        customizeWeb: null,
        customizeMp: null,
        servicePhone: null,
      },
      sceneConfig: {
        sceneMiniProgramChecked: false,
        sceneFinderChecked: false,
        miniProgram: null,
        finder: null,
      },
    });

    // const handleCardInfoResult = (result, processId) => ({
    //   ...createDefaultState(isfirst),
    //   cardInfo: result.data.cardInfo,
    //   processId,
    //   customerService: getCustomerServiceState(result.data.cardInfo?.customerServiceList),
    // });

    const handleProcessResult = (result) => {
      if (!result.data.process) {
        return {
          ...createDefaultState(isfirst),
          processId: result.data.processId,
        };
      }

      const { process } = result.data;
      const isFinished =
        process.state === 5 || process.state === 0 || !('state' in process);

      return {
        ...(isFinished ? createDefaultState(isfirst) : {}),
        isfirst,
        processId: process.processId,
        status: process.state,
        scheduleTimeWithFormat:
          process.processType === 2
            ? formatDate(new Date(+process.scheduleTime * 1000))
            : 0,
        publishType: process.publishType,
        customerService: getCustomerServiceState(process?.content?.customerServiceList),
        sceneConfig: getSceneConfigState(process?.content?.sceneList),
        processOrigin: {
          ...process,
          isfirst,
        },
      };
    };

    // 1. 先查最新的发布单
    const lastestCardInfo = await getLatestCardInfoPublishProcess();
    if (lastestCardInfo.code === 0) {
      if (
        lastestCardInfo.data.process.state !== 5 &&
        lastestCardInfo.data.process.state !== 8
      ) {
        if (!lastestCardInfo.data.process.content) {
          const cardInfo = await getCardInfo();
          if (cardInfo?.code === 0) {
            lastestCardInfo.data.process.content = cardInfo.data.cardInfo;
            return handleProcessResult(lastestCardInfo);
          }
          // 没有查到线上的cardinfo
          lastestCardInfo.data.process.content = createDefaultState(true);
          return handleProcessResult(lastestCardInfo);
        }
        return handleProcessResult(lastestCardInfo);
      }
    }
    // 2. 创建新单，并且把原有的cardinfo带入
    const created = await createCardInfoPublishProcess();
    if (created?.code === 0) {
      const { processId } = created.data;
      const cardInfo = await getCardInfo();
      return handleProcessResult({
        data: {
          process: {
            processId,
            state: 0,
            content:
              cardInfo.code === 0 ? cardInfo.data.cardInfo : createDefaultState(true),
          },
        },
      });
    }
    throw new Error('创建发布流程失败');
  },
  uploadFile: async (files, props) => {
    // 文件校验逻辑
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!Array.isArray(files)) {
      files = [files];
    }
    for (const file of files) {
      if (!ALLOWED_TYPES.includes(file.type)) {
        throw new Error(`文件类型 ${file.type} 不支持`);
      }
      if (file.size > MAX_FILE_SIZE) {
        throw new Error(`文件大小超过限制 (${MAX_FILE_SIZE / 1024 / 1024}MB)`);
      }
    }

    const formData = new FormData();

    // 根据实际网络请求，字段名应该是 'file' 而不是 'item'
    files.forEach((file, index) => {
      console.log(`File ${index}:`, file);
      console.log(`File ${index} raw:`, file.raw);

      if (file.raw instanceof File) {
        formData.append('file', file.raw);
        console.log(`Successfully appended file ${index} to FormData as 'file'`);
      } else {
        console.error(`File ${index} raw is not a File object:`, file.raw);
      }
    });

    // 添加 length 字段，表示文件数量
    formData.append('length', files.length.toString());

    // 调试：检查 FormData 内容
    console.log('FormData entries:');
    for (const pair of formData.entries()) {
      console.log(`${pair[0]}:`, pair[1]);
    }

    try {
      console.log('Sending request to:', props.action);
      console.log('FormData has file:', formData.has('file'));
      console.log('FormData has length:', formData.has('length'));

      // 使用 axios 来支持上传进度
      const response = await request.request({
        url: props.action,
        method: 'post',
        params: formData,
        headers: {
          accept: 'application/json',
          // 'Content-Type': 'multipart/form-data'
        },
      });

      console.log('Upload response:', response);

      // axios 的响应数据在 response.data 中
      if (response.code === 0) {
        return {
          status: 'success',
          response: {
            url: response.data?.fileUrl,
          },
        };
      }
      throw new Error(response?.message || '上传失败');
    } catch (error) {
      console.error('Upload error:', error);
      throw new Error('上传失败，请重试');
    }
  },
  uploadFileResponse: async (data) => {
    console.log('uploadFileResponse', data);
    return data;
  },

  execValidate,
});

const deepEqual = (obj1: any, obj2: any): boolean => {
  const compare = (a: any, b: any): boolean => {
    // 如果两个值严格相等，直接返回true
    if (a === b) return true;

    // 如果其中一个为null或undefined，另一个不是，返回false
    if (a == null || b == null) return a === b;

    // 如果类型不同，返回false
    if (typeof a !== typeof b) return false;

    // 处理基本类型
    if (typeof a !== 'object') return a === b;

    // 处理数组
    if (Array.isArray(a) !== Array.isArray(b)) return false;
    if (Array.isArray(a)) {
      if (a.length !== b.length) return false;
      for (let i = 0; i < a.length; i++) {
        if (!compare(a[i], b[i])) return false;
      }
      return true;
    }

    // 处理对象
    const keys1 = Object.keys(a);
    const keys2 = Object.keys(b);

    if (keys1.length !== keys2.length) return false;

    for (const key of keys1) {
      if (!keys2.includes(key)) return false;
      if (!compare(a[key], b[key])) return false;
    }

    return true;
  };

  return compare(obj1, obj2);
};

registerObjects('utils', {
  formatDate,
  // 深度对比函数
  handleConfirm: (confirmData) => {
    const globalState = useGlobalStateStore();
    const cardStatusState = '#/definitions/cardStatus/state';
    const latestCardInfoPublishState = '#/definitions/LatestCardInfoPublish/state';
    const cardStatus = globalState.getStoreSync(cardStatusState);
    const latestCardInfoPublish = globalState.getStoreSync(latestCardInfoPublishState);
    return {
      ...confirmData[1],
      jump: `/home/<USER>/${cardStatus.isfirst ? `service-state/config/${latestCardInfoPublish.processId}` : `common-publish/${latestCardInfoPublish.processId}?source=cardInfo`}`,
    };
  },
  prepareSaveCardInfo: (formData) => {
    const { cardBrief } = formData.cardBrief;
    const {
      latestCardInfoPublish: { processId },
      merchantServiceCategory: { category },
    } = formData;
    const sceneList = [];
    const customerService = [];
    const { sceneFinderChecked, sceneMiniProgramChecked } = formData.formCheckboxStatus;

    if (category === 2) {
      if (typeof formData.customerService.servicePhone !== 'undefined') {
        customerService.push({
          customerServiceType:
            CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_SERVICE_PHONE,
          servicePhone: formData.customerService.servicePhone,
        });
      }
    } else {
      switch (formData.customerService.customerServiceType) {
        case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_WEB:
          customerService.push({
            customerServiceType:
              CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_WEB,
            customizeWeb: formData.customerService.customizeWeb,
          });
          break;
        case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_MP:
          customerService.push({
            customerServiceType:
              CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE_MP,
            customizeMp: formData.customerService.customizeMp,
          });
          break;
        case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM:
          customerService.push({
            customerServiceType:
              CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM,
            wecom: formData.customerService.wecom,
          });
          break;
        case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM:
          customerService.push({
            customerServiceType:
              CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM,
            miniProgram: formData.customerService.miniProgram,
          });
          break;
        default:
          break;
      }
    }

    // 处理文件格式
    const formatImageList = (obj) =>
      obj.imageList.map((item) => (typeof item === 'object' ? item?.url : item));
    if (sceneMiniProgramChecked) {
      if (formData.cardConfigSence.miniProgram) {
        formData.cardConfigSence.miniProgram.imageList = formatImageList(
          formData.cardConfigSence.miniProgram
        );
      }
      if (Object.keys(formData.cardConfigSence.miniProgram).length > 0) {
        if (!formData.cardConfigSence.miniProgram?.sceneTag) {
          formData.cardConfigSence.miniProgram.sceneTag = '前往小程序';
        }
        sceneList.push({
          sceneType: SceneType.MERCHANT_CARD_SCENE_TYPE_MP,
          miniProgram: formData.cardConfigSence.miniProgram,
        });
      }
    }

    if (sceneFinderChecked) {
      if (formData.cardConfigSence.finder) {
        formData.cardConfigSence.finder.imageList = formatImageList(
          formData.cardConfigSence.finder
        );
      }
      if (Object.keys(formData.cardConfigSence.finder).length > 0) {
        if (!formData.cardConfigSence.finder?.sceneTag) {
          formData.cardConfigSence.finder.sceneTag = '前往视频号';
        }
        sceneList.push({
          sceneType: SceneType.MERCHANT_CARD_SCENE_TYPE_FINDER,
          finder: formData.cardConfigSence.finder,
        });
      }
    }

    return {
      content: { cardBrief, customerServiceList: customerService, sceneList },
      processId,
    };
  },
  handleSaveResponse: (response) => {
    console.log('保存响应:', response);
    return response;
  },
  initState: (states) => {
    const finalStates = { ...states };
    const matchCustomerServiceByType = (from) => {
      let miniProgram;
      let wecom;
      let customize;
      let servicePhone;
      let customerServiceType;
      if (Array.isArray(from)) {
        for (const item of from) {
          customerServiceType = item.customerServiceType;
          switch (customerServiceType) {
            case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM:
              miniProgram = item.miniProgram;
              break;
            case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM:
              wecom = item.wecom;
              break;
            case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE:
              customize = item.customize;
              break;
            case CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_SERVICE_PHONE:
              servicePhone = item.servicePhone;
              break;
          }
        }
      }
      const filterUndefined = {
        ...{ customerServiceType, miniProgram, wecom, customize, servicePhone },
      };
      // Define type mapping in a more declarative way
      const typeMapping = {
        miniProgram:
          CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM,
        wecom: CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM,
        customize: CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE,
      };

      // Find the first matching service type
      customerServiceType =
        Object.entries(typeMapping).find(([key]) => filterUndefined[key])?.[1] ||
        CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_NONE;

      filterUndefined.customerServiceType = customerServiceType;
      filterUndefined.onlineChecked = [
        CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_MINI_PROGRAM,
        CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_WECOM,
        CustomerServiceType.MMPAY_MCH_CONTACT_CUSTOMER_SERVICE_TYPE_CUSTOMIZE,
      ].includes(customerServiceType);
      filterUndefined.customerTelChecked = !!servicePhone;
      return filterUndefined;
    };

    const matchSceneByType = (from) => {
      let miniProgram;
      let finder;
      let sceneType;
      if (Array.isArray(from)) {
        for (const item of from) {
          sceneType = item.sceneType;
          switch (sceneType) {
            case SceneType.MERCHANT_CARD_SCENE_TYPE_MP:
              miniProgram = item.miniProgram;
              break;
            case SceneType.MERCHANT_CARD_SCENE_TYPE_FINDER:
              finder = item.finder;
              break;
          }
        }
      }
      const finalData = { miniProgram, finder };
      finalData.sceneMiniProgramChecked = !!miniProgram;
      finalData.sceneFinderChecked = !!finder;
      return finalData;
    };
    if (finalStates.data) {
      finalStates.data.customerService = matchCustomerServiceByType(
        finalStates.data.customerService
      );
      finalStates.data.sceneConfig = matchSceneByType(finalStates.data.sceneList);

      return finalStates;
    }
  },
  showState: () =>
    h('p', { class: 'shake-create-activity-tip' }, [
      '创建「摇一摇有优惠」请联系微信支付运营。',
    ]),

  renderMiniprogramSelect: (options) => {
    return options.map((option) =>
      h(
        Option,
        {
          ...option,
          key: option.value,
        },
        () =>
          h('div', {}, [
            h('div', {}, option.label),
            h('div', { class: 'appid-text' }, ['AppID:', h('span', {}, option.value)]),
          ])
      )
    );
  },
});

// .appid-text {
//   font-size: 14px;
//   color: #838383;
//   margin-top: 2px;
// }
export const registerRouter = (route) => {
  const { registerRouter: register } = useRouterRegistry();
  register(route);
};
