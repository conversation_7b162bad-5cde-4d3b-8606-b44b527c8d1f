<template>
  <div class="card-detail-test">
    <div class="test-header">
      <h2>Card-Detail 状态测试面板</h2>
      <p>通过切换不同场景来测试按钮状态变化</p>
    </div>

    <div class="scenario-selector">
      <h3>选择测试场景:</h3>
      <div class="scenario-buttons">
        <button
          v-for="scenario in scenarios"
          :key="scenario.scenario"
          :class="{ active: currentScenario === scenario.scenario }"
          @click="switchScenario(scenario.scenario)"
        >
          {{ scenario.description }}
        </button>
      </div>
    </div>

    <div class="current-scenario">
      <h3>当前场景: {{ getCurrentScenarioDescription() }}</h3>
      <div class="scenario-details" v-if="scenarioDetails">
        <h4>配置详情:</h4>
        <ul>
          <li>基础信息状态: {{ getStateName(scenarioDetails.config.cardInfoState) }}</li>
          <li>服务配置状态: {{ getStateName(scenarioDetails.config.serviceState) }}</li>
          <li>
            品牌会员状态:
            {{ getAccessStateName(scenarioDetails.config.brandMemberState) }}
          </li>
          <li>
            优惠券状态: {{ getAccessStateName(scenarioDetails.config.shakeCouponState) }}
          </li>
        </ul>
      </div>
    </div>

    <!-- Card-Detail 组件 -->
    <div class="card-detail-demo">
      <h3>Card-Detail 页面效果:</h3>
      <div class="demo-container">
        <CardDetail />
      </div>
    </div>

    <!-- 状态预期结果 -->
    <div class="expected-results">
      <h3>预期结果:</h3>
      <div class="results-grid">
        <div class="result-item">
          <h4>基础信息配置项</h4>
          <div class="result-details">
            <p>
              状态文本:
              <span class="highlight">{{
                expectedResults.basicInfo?.statusText || '加载中...'
              }}</span>
            </p>
            <p>
              按钮文本:
              <span class="highlight">{{
                expectedResults.basicInfo?.buttonText || '加载中...'
              }}</span>
            </p>
            <p>
              按钮主题:
              <span class="highlight">{{
                expectedResults.basicInfo?.buttonTheme || '加载中...'
              }}</span>
            </p>
            <p>
              完成状态:
              <span class="highlight">{{
                expectedResults.basicInfo?.completed ? '已完成' : '未完成'
              }}</span>
            </p>
          </div>
        </div>

        <div class="result-item">
          <h4>名片服务配置项</h4>
          <div class="result-details">
            <p>
              状态文本:
              <span class="highlight">{{
                expectedResults.serviceInfo?.statusText || '加载中...'
              }}</span>
            </p>
            <p>
              按钮文本:
              <span class="highlight">{{
                expectedResults.serviceInfo?.buttonText || '加载中...'
              }}</span>
            </p>
            <p>
              按钮主题:
              <span class="highlight">{{
                expectedResults.serviceInfo?.buttonTheme || '加载中...'
              }}</span>
            </p>
            <p>
              完成状态:
              <span class="highlight">{{
                expectedResults.serviceInfo?.completed ? '已完成' : '未完成'
              }}</span>
            </p>
          </div>
        </div>

        <div class="result-item">
          <h4>品牌会员配置项</h4>
          <div class="result-details">
            <p>
              状态文本:
              <span class="highlight">{{
                expectedResults.brandInfo?.statusText || '加载中...'
              }}</span>
            </p>
            <p>
              按钮文本:
              <span class="highlight">{{
                expectedResults.brandInfo?.buttonText || '加载中...'
              }}</span>
            </p>
            <p>
              按钮主题:
              <span class="highlight">{{
                expectedResults.brandInfo?.buttonTheme || '加载中...'
              }}</span>
            </p>
            <p>
              完成状态:
              <span class="highlight">{{
                expectedResults.brandInfo?.completed ? '已完成' : '未完成'
              }}</span>
            </p>
          </div>
        </div>

        <div class="result-item publish-result">
          <h4>发布按钮</h4>
          <div class="result-details">
            <p>
              发布状态:
              <span class="highlight">{{
                expectedResults.canPublish ? '可以发布' : '禁用发布'
              }}</span>
            </p>
            <p>
              完成度: <span class="highlight">{{ completionPercentage }}%</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { useCardDetailStore } from '@/stores/cardDetail';
import CardDetail from './CardDetail.vue';
// import { request } from '@/utils/request';

/**
 * Card-Detail 状态测试组件
 * 用于测试不同场景下按钮状态的变化
 */

const cardDetailStore = useCardDetailStore();
const { configItems, canPublish, completionPercentage } = storeToRefs(cardDetailStore);

// 场景数据
const scenarios = ref<any[]>([]);
const currentScenario = ref('new-user');
const scenarioDetails = ref<any>(null);

// 状态名称映射
const stateNames: Record<number | string, string> = {
  0: '初始状态',
  1: '草稿',
  2: '审核中',
  3: '审核通过',
  4: '审核驳回',
  5: '已作废',
  6: '待发布',
  7: '发布中',
  8: '已完成',
  ERROR: 'API错误',
};

const accessStateNames: Record<number, string> = {
  0: '未设置',
  1: '已开启',
  2: '已关闭',
};

// 计算预期结果
const expectedResults = computed(() => {
  if (!configItems.value.length) return {};

  return {
    basicInfo: {
      statusText: configItems.value[0]?.statusText,
      buttonText: configItems.value[0]?.buttonText,
      buttonTheme: configItems.value[0]?.buttonTheme,
      completed: configItems.value[0]?.completed,
    },
    serviceInfo: {
      statusText: configItems.value[1]?.statusText,
      buttonText: configItems.value[1]?.buttonText,
      buttonTheme: configItems.value[1]?.buttonTheme,
      completed: configItems.value[1]?.completed,
    },
    brandInfo: {
      statusText: configItems.value[2]?.statusText,
      buttonText: configItems.value[2]?.buttonText,
      buttonTheme: configItems.value[2]?.buttonTheme,
      completed: configItems.value[2]?.completed,
    },
    canPublish: canPublish.value,
  };
});

// 获取状态名称
const getStateName = (state: number | string) => {
  return stateNames[state] || `未知状态(${state})`;
};

const getAccessStateName = (state: number) => {
  return accessStateNames[state] || `未知状态(${state})`;
};

// 获取当前场景描述
const getCurrentScenarioDescription = () => {
  const scenario = scenarios.value.find((s) => s.scenario === currentScenario.value);
  return scenario?.description || '未知场景';
};

// 切换场景
const switchScenario = async (scenario: string) => {
  try {
    currentScenario.value = scenario;

    // 通过URL参数切换场景，重新初始化页面数据
    await cardDetailStore.initializePage(`test-process-${Date.now()}`);

    // 设置场景详情 (简化处理)
    scenarioDetails.value = {
      scenario,
      description: getCurrentScenarioDescription(),
      config: {
        cardInfoState: 0,
        serviceState: 0,
        brandMemberState: 0,
        shakeCouponState: 0,
      },
    };
  } catch (error) {
    console.error('切换场景失败:', error);
  }
};

// 加载可用场景
const loadScenarios = async () => {
  // 直接使用预定义的场景
  scenarios.value = [
    { scenario: 'new-user', description: '新手用户 - 所有配置项都未配置' },
    {
      scenario: 'partial-configured',
      description: '部分配置 - 基础信息已配置，其他未完成',
    },
    { scenario: 'auditing', description: '审核中 - 基础信息正在审核' },
    { scenario: 'rejected', description: '审核驳回 - 基础信息被驳回' },
    { scenario: 'ready-to-publish', description: '准备发布 - 所有配置完成' },
    { scenario: 'coupon-access', description: '优惠券接入 - 通过优惠券接入' },
    { scenario: 'approved', description: '待发布 - 审核通过待发布' },
    { scenario: 'api-error', description: 'API错误 - 模拟API失败' },
    { scenario: 'all-draft', description: '全部草稿 - 所有配置都是草稿状态' },
    { scenario: 'both-access', description: '双重接入 - 品牌会员和优惠券都接入' },
  ];
};

// 监听场景变化，更新URL参数
watch(currentScenario, (newScenario) => {
  // 更新URL参数，这样mock会根据新的scenario返回对应数据
  const url = new URL(window.location.href);
  url.searchParams.set('scenario', newScenario);
  window.history.replaceState({}, '', url.toString());
});

// 组件挂载时初始化
onMounted(async () => {
  await loadScenarios();
  await switchScenario(currentScenario.value);
});
</script>

<style lang="scss" scoped>
.card-detail-test {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;

  .test-header {
    margin-bottom: 32px;
    text-align: center;

    h2 {
      color: #262626;
      margin-bottom: 8px;
    }

    p {
      color: #666;
      font-size: 14px;
    }
  }

  .scenario-selector {
    margin-bottom: 32px;

    h3 {
      margin-bottom: 16px;
      color: #262626;
    }

    .scenario-buttons {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      button {
        padding: 12px 16px;
        border: 2px solid #d9d9d9;
        border-radius: 8px;
        background: #fff;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: #40a9ff;
          color: #40a9ff;
        }

        &.active {
          border-color: #1890ff;
          background: #1890ff;
          color: #fff;
        }
      }
    }
  }

  .current-scenario {
    margin-bottom: 32px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 8px;

    h3 {
      margin-bottom: 16px;
      color: #262626;
    }

    .scenario-details {
      h4 {
        margin-bottom: 12px;
        color: #595959;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          padding: 4px 0;
          color: #666;
        }
      }
    }
  }

  .card-detail-demo {
    margin-bottom: 32px;

    h3 {
      margin-bottom: 16px;
      color: #262626;
    }

    .demo-container {
      border: 1px solid #d9d9d9;
      border-radius: 8px;
      overflow: hidden;
      height: 600px;
    }
  }

  .expected-results {
    h3 {
      margin-bottom: 16px;
      color: #262626;
    }

    .results-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;

      .result-item {
        padding: 16px;
        background: #fafafa;
        border-radius: 8px;
        border: 1px solid #f0f0f0;

        h4 {
          margin-bottom: 12px;
          color: #262626;
          font-size: 16px;
        }

        .result-details {
          p {
            margin: 8px 0;
            font-size: 14px;
            color: #666;

            .highlight {
              color: #1890ff;
              font-weight: 500;
            }
          }
        }

        &.publish-result {
          border-color: #1890ff;
          background: #f6ffed;

          h4 {
            color: #1890ff;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card-detail-test {
    padding: 16px;

    .scenario-buttons {
      grid-template-columns: 1fr;
    }

    .results-grid {
      grid-template-columns: 1fr;
    }

    .demo-container {
      height: 400px;
    }
  }
}
</style>
