/**
 * Card-Detail 页面类型定义
 * 配置进度跟踪页面相关类型
 */

// ==================== 基础类型定义 ====================

/**
 * 配置项信息
 */
export interface ConfigItem {
  id: string; // 配置项ID
  title: string; // 配置项标题
  description: string; // 配置项描述
  completed: boolean; // 是否已完成配置
  route: string; // 跳转路由
  disabled?: boolean; // 是否禁用
  lastUpdate?: string; // 最后更新时间
  statusText: string; // 状态文本（如：已配置、待配置、审核中等）
  buttonText: string; // 按钮文本（如：查看、去配置、修改等）
  buttonTheme: string; // 按钮主题（primary、default等）
}

/**
 * 页面状态
 */
export interface PageState {
  isLoading: boolean; // 是否加载中
  processId: string | null; // 流程ID
  lastSavedTime?: string; // 最后保存时间
}

/**
 * 预览数据
 */
export interface PreviewData {
  cardId: string; // 卡片ID
  cardName: string; // 卡片名称
  status: string; // 状态
  [key: string]: any; // 其他预览数据
}

// ==================== API相关类型 ====================

/**
 * API响应基础类型
 */
export interface ApiResponse<T = unknown> {
  code: number; // 响应代码
  message: string; // 响应消息
  data?: T; // 响应数据
}

/**
 * 配置状态响应
 */
export interface ConfigStatusResponse {
  basicInfo: {
    completed: boolean;
    lastUpdate?: string;
  };
  serviceInfo: {
    completed: boolean;
    lastUpdate?: string;
  };
  brandInfo: {
    completed: boolean;
    lastUpdate?: string;
  };
  canPublish: boolean;
}

// ==================== 导出类型 ====================

export type { ConfigItem, PageState, PreviewData, ApiResponse, ConfigStatusResponse };
