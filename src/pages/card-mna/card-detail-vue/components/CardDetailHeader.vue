<template>
  <div class="card-detail-header">
    <!-- 左侧导航区域 -->
    <div class="header-left">
      <!-- 返回按钮 -->
      <t-button variant="text" size="medium" class="back-button" @click="handleBack">
        <template #icon>
          <t-icon name="chevron-left" />
        </template>
        返回
      </t-button>

      <!-- 页面标题 -->
      <div class="page-title">
        <h1>名片配置</h1>
        <div class="subtitle">
          {{ getStepTitle(currentStep) }}
        </div>
      </div>
    </div>

    <!-- 中间进度区域 -->
    <div class="header-center">
      <!-- 配置进度 -->
      <div class="progress-section">
        <div class="progress-info">
          <span class="progress-text">配置完成度</span>
          <span class="progress-percentage">{{ completionPercentage }}%</span>
        </div>
        <t-progress
          :percentage="completionPercentage"
          :stroke-width="6"
          :show-info="false"
          theme="success"
          class="progress-bar"
        />
      </div>

      <!-- 保存状态 -->
      <div class="save-status">
        <div v-if="isSaving" class="saving-indicator">
          <t-loading size="small" />
          <span>保存中...</span>
        </div>
        <div v-else-if="lastSavedTime" class="last-saved">
          <t-icon name="check-circle" class="saved-icon" />
          <span>{{ formatSaveTime(lastSavedTime) }}</span>
        </div>
        <div v-else class="not-saved">
          <t-icon name="time" class="pending-icon" />
          <span>未保存</span>
        </div>
      </div>
    </div>

    <!-- 右侧操作区域 -->
    <div class="header-right">
      <!-- 帮助按钮 -->
      <t-button variant="outline" size="medium" class="help-button" @click="handleHelp">
        <template #icon>
          <t-icon name="help-circle" />
        </template>
        帮助
      </t-button>

      <!-- 更多操作 -->
      <t-dropdown
        :options="moreOptions"
        placement="bottom-right"
        @click="handleMoreAction"
      >
        <t-button variant="outline" size="medium" class="more-button">
          <template #icon>
            <t-icon name="more" />
          </template>
        </t-button>
      </t-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ConfigStep } from '../types/index';

/**
 * Card-Detail页面头部组件
 * 包含导航、进度显示、操作按钮等功能
 */

// Props定义
// interface Props {
//   currentStep: ConfigStep;           // 当前配置步骤
//   completionPercentage: number;      // 完成度百分比
//   isSaving: boolean;                 // 是否正在保存
//   lastSavedTime: string;             // 最后保存时间
// }

// const props = withDefaults(defineProps<Props>(), {
//   currentStep: 'basic-info' as ConfigStep,
//   completionPercentage: 0,
//   isSaving: false,
//   lastSavedTime: ''
// });

// Emits定义
interface Emits {
  back: []; // 返回事件
  help: []; // 帮助事件
  moreAction: [action: string]; // 更多操作事件
}

const emit = defineEmits<Emits>();

// ==================== 计算属性 ====================

/**
 * 更多操作选项
 */
const moreOptions = computed(() => [
  {
    content: '预览效果',
    value: 'preview',
    prefixIcon: 'view',
  },
  {
    content: '导入配置',
    value: 'import',
    prefixIcon: 'upload',
  },
  {
    content: '导出配置',
    value: 'export',
    prefixIcon: 'download',
  },
  {
    content: '重置配置',
    value: 'reset',
    prefixIcon: 'refresh',
    theme: 'error',
  },
]);

// ==================== 方法 ====================

/**
 * 获取步骤标题
 */
const getStepTitle = (step: ConfigStep): string => {
  const stepTitles = {
    'basic-info': '基础信息配置',
    contact: '联系方式配置',
    business: '业务介绍配置',
    style: '样式设置配置',
  };

  return stepTitles[step] || '配置';
};

/**
 * 格式化保存时间
 */
const formatSaveTime = (timeString: string): string => {
  if (!timeString) return '';

  try {
    const saveTime = new Date(timeString);
    const now = new Date();
    const diffMs = now.getTime() - saveTime.getTime();
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffMinutes < 1) {
      return '刚刚保存';
    }
    if (diffMinutes < 60) {
      return `${diffMinutes}分钟前保存`;
    }
    if (diffMinutes < 24 * 60) {
      const diffHours = Math.floor(diffMinutes / 60);
      return `${diffHours}小时前保存`;
    }
    return `${saveTime.toLocaleDateString()} 保存`;
  } catch (error) {
    console.error('格式化保存时间失败:', error);
    return '保存时间异常';
  }
};

/**
 * 处理返回操作
 */
const handleBack = (): void => {
  emit('back');
};

/**
 * 处理帮助操作
 */
const handleHelp = (): void => {
  emit('help');
};

/**
 * 处理更多操作
 */
const handleMoreAction = (data: { value: string }): void => {
  emit('moreAction', data.value);
};
</script>

<style lang="scss" scoped>
.card-detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
  background-color: #ffffff;
  border-bottom: 1px solid #e6e6e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04);

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-button {
      color: #666666;

      &:hover {
        color: #1890ff;
        background-color: #f0f8ff;
      }
    }

    .page-title {
      h1 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: #333333;
        line-height: 1.2;
      }

      .subtitle {
        font-size: 12px;
        color: #999999;
        margin-top: 2px;
      }
    }
  }

  .header-center {
    display: flex;
    align-items: center;
    gap: 32px;

    .progress-section {
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 200px;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .progress-text {
          font-size: 12px;
          color: #666666;
        }

        .progress-percentage {
          font-size: 14px;
          font-weight: 600;
          color: #52c41a;
        }
      }

      .progress-bar {
        width: 100%;
      }
    }

    .save-status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;

      .saving-indicator {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #1890ff;
      }

      .last-saved {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #52c41a;

        .saved-icon {
          font-size: 14px;
        }
      }

      .not-saved {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #faad14;

        .pending-icon {
          font-size: 14px;
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .help-button {
      color: #666666;

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
      }
    }

    .more-button {
      color: #666666;

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .card-detail-header {
    .header-center {
      gap: 24px;

      .progress-section {
        min-width: 160px;
      }
    }
  }
}

@media (max-width: 768px) {
  .card-detail-header {
    padding: 0 16px;
    height: 56px;

    .header-left {
      gap: 12px;

      .page-title {
        h1 {
          font-size: 16px;
        }

        .subtitle {
          display: none;
        }
      }
    }

    .header-center {
      gap: 16px;

      .progress-section {
        min-width: 120px;

        .progress-info {
          .progress-text {
            display: none;
          }
        }
      }

      .save-status {
        span {
          display: none;
        }
      }
    }

    .header-right {
      gap: 8px;

      .help-button {
        :deep(.t-button__text) {
          display: none;
        }
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .card-detail-header {
    background-color: #1f1f1f;
    border-bottom-color: #333333;

    .header-left {
      .back-button {
        color: #cccccc;

        &:hover {
          color: #1890ff;
          background-color: rgba(24, 144, 255, 0.1);
        }
      }

      .page-title {
        h1 {
          color: #ffffff;
        }

        .subtitle {
          color: #999999;
        }
      }
    }

    .header-center {
      .progress-section {
        .progress-info {
          .progress-text {
            color: #cccccc;
          }
        }
      }
    }

    .header-right {
      .help-button,
      .more-button {
        color: #cccccc;
        border-color: #333333;

        &:hover {
          color: #1890ff;
          border-color: #1890ff;
        }
      }
    }
  }
}
</style>
