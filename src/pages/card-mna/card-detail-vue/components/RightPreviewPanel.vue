<template>
  <div class="right-preview-panel">
    <!-- 预览标题 -->
    <div class="preview-header">
      <h4 class="preview-title">名片预览</h4>
      <div class="preview-tips">手机扫码体验</div>
    </div>

    <div class="preview-container">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="preview-loading">
        <div class="loading-spinner"></div>
        <p>加载预览中...</p>
      </div>

      <!-- 预览内容 -->
      <div v-else-if="!previewError" class="preview-content">
        <!-- 使用 v-businesscard 组件 -->
        <v-businesscard
          v-if="hasValidPreviewData"
          :cardDetail="cardDetailData"
          :data="businessCardData"
          class="business-card-preview"
        />

        <!-- 预览占位符 -->
        <div v-else class="preview-placeholder">
          <div class="placeholder-icon">📱</div>
          <p class="placeholder-text">完成配置后可预览名片效果</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else class="preview-error">
        <div class="error-icon">⚠️</div>
        <p class="error-text">预览加载失败</p>
        <button class="retry-btn" @click="retryPreview">重试</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { storeToRefs } from 'pinia';
import { useCardDetailStore } from '@/stores/cardDetail';

/**
 * 右侧预览面板组件
 * 显示名片预览内容
 */

const cardDetailStore = useCardDetailStore();
const { previewData, pageState } = storeToRefs(cardDetailStore);

// 本地状态
const previewError = ref(false);

// 计算属性
const isLoading = computed(() => pageState.value.isLoading);

const hasValidPreviewData = computed(() => {
  // 现在总是有有效的预览数据（默认或实际数据）
  return true;
});

// v-businesscard 组件的 cardDetail 参数
const cardDetailData = computed(() => {
  return (
    previewData.value || {
      cardId: 'card123456',
      cardName: '微信支付商家名片',
      status: 'active',
    }
  );
});

// v-businesscard 组件的 data 参数 - 根据原始DSL数据结构
const businessCardData = computed(() => {
  // 这里应该与 service-config/interactions/data.yaml 中的数据结构一致
  return {
    showQRCode: 'pop',
    page: 'card-detail',
    closeable: false,
  };
});

// 重试预览
const retryPreview = async () => {
  try {
    previewError.value = false;
    await cardDetailStore.refreshPreviewData();
  } catch (error) {
    console.error('重试预览失败:', error);
    previewError.value = true;
  }
};

// 组件挂载时处理
onMounted(() => {
  // 监听预览数据加载错误
  if (!previewData.value && !pageState.value.isLoading) {
    previewError.value = false; // 暂时不显示错误，显示占位符
  }
});
</script>

<style lang="scss" scoped>
.right-preview-panel {
  width: 320px;
  height: 100%;
  background-color: #f7f8fa;
  border-left: 1px solid #e7e7e7;
  padding: 20px 16px;
  display: flex;
  flex-direction: column;

  .preview-header {
    margin-bottom: 16px;
    text-align: center;

    .preview-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin: 0 0 8px 0;
    }

    .preview-tips {
      font-size: 12px;
      color: #8c8c8c;
    }
  }

  .preview-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
  }

  // 加载状态
  .preview-loading {
    text-align: center;
    color: #8c8c8c;

    .loading-spinner {
      width: 32px;
      height: 32px;
      margin: 0 auto 12px;
      border: 3px solid #f0f0f0;
      border-top: 3px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    p {
      margin: 0;
      font-size: 14px;
    }
  }

  // 预览内容
  .preview-content {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .business-card-preview {
    width: 100%;
    max-width: 280px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background-color: #ffffff;
    overflow: hidden;
  }

  // 占位符
  .preview-placeholder {
    text-align: center;
    padding: 40px 20px;
    color: #8c8c8c;

    .placeholder-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .placeholder-text {
      margin: 0;
      font-size: 14px;
      line-height: 1.5;
    }
  }

  // 错误状态
  .preview-error {
    text-align: center;
    padding: 40px 20px;
    color: #ff4d4f;

    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
    }

    .error-text {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: #8c8c8c;
    }

    .retry-btn {
      padding: 8px 16px;
      background-color: #1890ff;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background-color: #40a9ff;
      }

      &:active {
        background-color: #096dd9;
      }
    }
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .right-preview-panel {
    width: 280px;

    .business-card-preview {
      max-width: 240px;
    }
  }
}

@media (max-width: 768px) {
  .right-preview-panel {
    width: 100%;
    height: 300px;
    border-left: none;
    border-bottom: 1px solid #e7e7e7;
    padding: 16px;

    .preview-container {
      min-height: 200px;
    }

    .preview-placeholder,
    .preview-error {
      padding: 20px;
    }
  }
}
</style>
