<template>
  <div class="config-status-item">
    <!-- 配置卡片 -->
    <div class="config-card">
      <div class="config-content">
        <div class="config-header">
          <span class="config-title">{{ item.title }}</span>
          <span class="config-status-tag" :class="statusClass">
            {{ statusText }}
          </span>
        </div>
        <p class="config-description">{{ item.description }}</p>
      </div>
      <t-button
        :theme="buttonTheme"
        :disabled="item.disabled"
        class="config-button"
        @click="handleClick"
      >
        {{ buttonText }}
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ConfigItem } from '../types';

/**
 * 配置状态项组件
 * 显示单个配置项的状态和操作按钮
 */

interface Props {
  item: ConfigItem;
}

interface Emits {
  (e: 'navigate', item: ConfigItem): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 状态标签样式
const statusClass = computed(() => ({
  'status-completed': props.item.completed,
  'status-pending': !props.item.completed,
}));

// 状态文本
const statusText = computed(
  () => props.item.statusText || (props.item.completed ? '已配置' : '待配置')
);

// 按钮主题
const buttonTheme = computed(
  () => props.item.buttonTheme || (props.item.completed ? 'default' : 'primary')
);

// 按钮文本
const buttonText = computed(
  () => props.item.buttonText || (props.item.completed ? '重新配置' : '去配置')
);

/**
 * 处理点击事件
 */
const handleClick = () => {
  emit('navigate', props.item);
};
</script>

<style lang="scss" scoped>
.config-status-item {
  margin-bottom: 24px;

  .config-card {
    background-color: rgba(0, 0, 0, 0.02);
    padding: 24px;
    border-radius: 8px;
    border: none;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .config-content {
    flex: 1;
    padding-right: 16px;

    .config-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .config-title {
        font-size: 16px;
        font-weight: 500;
        color: #262626;
        line-height: 22px;
      }

      .config-status-tag {
        margin-left: 8px;
        padding: 0 8px;
        border-radius: 2px;
        font-size: 12px;
        height: 20px;
        line-height: 20px;

        &.status-completed {
          background-color: #e8f6ef;
          color: #07c160;
        }

        &.status-pending {
          background-color: #f1f2f5;
          color: #909399;
        }
      }
    }

    .config-description {
      margin: 0;
      color: rgba(0, 0, 0, 0.5);
      line-height: 22px;
      font-size: 14px;
    }
  }

  .config-button {
    flex-shrink: 0;
    min-width: 88px;
    height: 36px;
    font-size: 14px;
    border-radius: 8px;
  }

  // 最后一个配置项去掉底部margin
  &:last-child {
    margin-bottom: 24px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-status-item {
    margin-bottom: 24px;

    .timeline-dot {
      left: -29px; // 适应移动端
    }

    .config-card {
      padding: 20px;
      flex-direction: column;
      align-items: stretch;
    }

    .config-content {
      padding-right: 0;
      margin-bottom: 16px;

      .config-header {
        flex-direction: column;
        align-items: flex-start;

        .config-status-tag {
          margin-left: 0;
          margin-top: 8px;
        }
      }
    }

    .config-button {
      width: 100%;
      min-width: auto;
    }
  }
}
</style>
