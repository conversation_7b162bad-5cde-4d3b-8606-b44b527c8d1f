<template>
  <div class="config-form">
    <div class="form-container">
      <h3>配置表单</h3>
      <p>这里是配置表单的内容</p>

      <!-- 基础信息配置 -->
      <div class="form-section">
        <h4>基础信息</h4>
        <div class="form-group">
          <label>商家名称</label>
          <input type="text" placeholder="请输入商家名称" />
        </div>
        <div class="form-group">
          <label>联系电话</label>
          <input type="tel" placeholder="请输入联系电话" />
        </div>
      </div>

      <!-- 样式配置 -->
      <div class="form-section">
        <h4>样式配置</h4>
        <div class="form-group">
          <label>主题色</label>
          <input type="color" value="#00C853" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 配置表单组件
</script>

<style scoped lang="scss">
.config-form {
  padding: 20px;
  background: #fff;
  border-radius: 8px;

  .form-container {
    max-width: 500px;

    h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .form-section {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }

      .form-group {
        margin-bottom: 16px;

        label {
          display: block;
          margin-bottom: 4px;
          font-size: 14px;
          color: #666;
        }

        input {
          width: 100%;
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 4px;
          font-size: 14px;

          &:focus {
            outline: none;
            border-color: #00c853;
          }
        }
      }
    }
  }
}
</style>
