<template>
  <div class="publish-section">
    <!-- 发布标题 -->
    <div class="publish-header">
      <div class="header-icon">
        <span class="status-dot inactive"></span>
      </div>
      <h3 class="publish-title">完成以上功能配置后，可以发布名片对用户可见</h3>
    </div>

    <!-- 发布区域内容 -->
    <div class="publish-content">
      <div class="publish-info">
        <div class="publish-label">发布名片</div>
        <p class="publish-description">
          完成以上功能配置，手机扫码体验确认无误后，点击发布立即对用户生效
        </p>
      </div>
      <t-button
        :theme="buttonTheme"
        :disabled="!canPublish"
        class="publish-button"
        @click="handlePublish"
      >
        发布
      </t-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

/**
 * 发布区域组件
 * 显示发布引导和发布按钮
 */

interface Props {
  canPublish: boolean;
  processId?: string;
}

interface Emits {
  (e: 'publish'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 按钮主题
const buttonTheme = computed(() => (props.canPublish ? 'primary' : 'default'));

/**
 * 处理发布操作
 */
const handlePublish = () => {
  if (props.canPublish) {
    emit('publish');
  }
};
</script>

<style lang="scss" scoped>
.publish-section {
  border-radius: 16px;
  background-color: #ffffff;
  padding-top: 24px;

  .publish-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .header-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 22px;
      height: 22px;
      margin-right: 8px;

      .status-dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;

        &.inactive {
          background-color: rgba(0, 0, 0, 0.08);
        }
      }
    }

    .publish-title {
      font-size: 20px;
      font-weight: 500;
      margin: 0;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .publish-content {
    margin-left: 32px;
    background-color: rgba(0, 0, 0, 0.02);
    padding: 24px;
    border-radius: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .publish-info {
      flex: 1;
      padding-right: 16px;

      .publish-label {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #262626;
      }

      .publish-description {
        margin: 0;
        color: #666;
        line-height: 22px;
        font-size: 14px;
      }
    }

    .publish-button {
      flex-shrink: 0;
      margin-left: 16px;
      min-width: 88px;
      height: 36px;
      font-size: 14px;
      border-radius: 8px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .publish-section {
    .publish-content {
      padding: 20px;
      flex-direction: column;
      align-items: stretch;

      .publish-info {
        padding-right: 0;
        margin-bottom: 16px;
      }

      .publish-button {
        width: 100%;
        min-width: auto;
      }
    }
  }
}
</style>
