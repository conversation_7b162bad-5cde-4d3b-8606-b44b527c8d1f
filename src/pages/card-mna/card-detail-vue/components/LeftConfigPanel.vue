<template>
  <div class="left-config-panel">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-icon">
        <span class="status-dot active"></span>
      </div>
      <h3 class="page-title">初次设置名片，需完成以下功能配置</h3>
    </div>

    <!-- 配置项列表 -->
    <div class="config-list">
      <ConfigStatusItem
        v-for="item in configItems"
        :key="item.id"
        :item="item"
        @navigate="handleNavigate"
      />
    </div>

    <!-- 发布区域 -->
    <PublishSection
      :can-publish="canPublish"
      :process-id="pageState.processId"
      @publish="handlePublish"
    />
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { useCardDetailStore } from '@/stores/cardDetail';
import ConfigStatusItem from './ConfigStatusItem.vue';
import PublishSection from './PublishSection.vue';
import type { ConfigItem } from '../types';

/**
 * 左侧配置面板组件
 * 显示配置项列表和发布区域
 */

const router = useRouter();
const cardDetailStore = useCardDetailStore();
const { configItems, canPublish, pageState } = storeToRefs(cardDetailStore);

/**
 * 处理配置项导航
 */
const handleNavigate = (item: ConfigItem) => {
  router.push(item.route);
};

/**
 * 处理发布操作
 */
const handlePublish = () => {
  if (canPublish.value) {
    router.push(
      `/home/<USER>/card-publish/${pageState.value.processId}?source=publish`
    );
  }
};
</script>

<style lang="scss" scoped>
.left-config-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #ffffff;
  border-radius: 0;
  padding: 32px 24px 24px;

  .page-header {
    display: flex;
    align-items: center;
    margin-bottom: 32px;

    .header-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 22px;
      height: 22px;
      margin-right: 12px;

      .status-dot {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #07c160;

        &.active {
          background-color: #07c160;
        }
      }
    }

    .page-title {
      font-size: 20px;
      font-weight: 500;
      margin: 0;
      color: #262626;
      line-height: 28px;
    }
  }

  .config-list {
    margin-bottom: 24px; // 控制与发布区域的间距
    border-left: 2px solid rgba(0, 0, 0, 0.08);
    padding-left: 19px;
    margin-left: 11px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .left-config-panel {
    padding: 20px 16px;

    .page-header {
      margin-bottom: 24px;

      .page-title {
        font-size: 18px;
        line-height: 24px;
      }
    }

    .config-list {
      padding-left: 18px;
      margin-left: 9px;
    }
  }
}
</style>
