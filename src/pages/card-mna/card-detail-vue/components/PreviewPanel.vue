<template>
  <div class="preview-panel">
    <div class="preview-container">
      <h3>预览面板</h3>
      <p>这里是预览面板的内容</p>

      <!-- 手机预览框 -->
      <div class="phone-preview">
        <div class="phone-frame">
          <div class="phone-screen">
            <div class="preview-content">
              <div class="card-preview">
                <h4>商家名片预览</h4>
                <p>这里显示配置后的名片效果</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预览控制 -->
      <div class="preview-controls">
        <button class="preview-btn">刷新预览</button>
        <button class="preview-btn primary">保存配置</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 预览面板组件
</script>

<style scoped lang="scss">
.preview-panel {
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;

  .preview-container {
    text-align: center;

    h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
    }

    .phone-preview {
      margin: 20px 0;

      .phone-frame {
        display: inline-block;
        width: 280px;
        height: 500px;
        background: #333;
        border-radius: 20px;
        padding: 20px;

        .phone-screen {
          width: 100%;
          height: 100%;
          background: #fff;
          border-radius: 15px;
          overflow: hidden;

          .preview-content {
            padding: 20px;
            height: 100%;

            .card-preview {
              h4 {
                margin: 0 0 10px 0;
                font-size: 16px;
                color: #333;
              }

              p {
                margin: 0;
                font-size: 14px;
                color: #666;
              }
            }
          }
        }
      }
    }

    .preview-controls {
      margin-top: 20px;

      .preview-btn {
        margin: 0 8px;
        padding: 8px 16px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: #fff;
        cursor: pointer;
        font-size: 14px;

        &:hover {
          background: #f5f5f5;
        }

        &.primary {
          background: #00c853;
          color: #fff;
          border-color: #00c853;

          &:hover {
            background: #00a843;
          }
        }
      }
    }
  }
}
</style>
