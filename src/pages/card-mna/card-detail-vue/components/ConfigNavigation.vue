<template>
  <div class="config-navigation">
    <!-- 导航标题 -->
    <div class="navigation-header">
      <h3>配置步骤</h3>
      <div class="progress-indicator">{{ completedSteps }}/{{ totalSteps }}</div>
    </div>

    <!-- 导航菜单 -->
    <div class="navigation-menu">
      <div
        v-for="step in navigationSteps"
        :key="step.key"
        :class="[
          'navigation-item',
          {
            active: step.key === currentStep,
            completed: isStepCompleted(step.key),
            error: isStepError(step.key),
            disabled: isStepDisabled(step.key),
          },
        ]"
        @click="handleStepClick(step.key)"
      >
        <!-- 步骤图标 -->
        <div class="step-icon">
          <div v-if="isStepCompleted(step.key)" class="icon-completed">
            <t-icon name="check" />
          </div>
          <div v-else-if="isStepError(step.key)" class="icon-error">
            <t-icon name="close" />
          </div>
          <div v-else-if="step.key === currentStep" class="icon-current">
            <t-icon :name="step.icon" />
          </div>
          <div v-else class="icon-pending">
            <span class="step-number">{{ step.order }}</span>
          </div>
        </div>

        <!-- 步骤内容 -->
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div class="step-description">{{ step.description }}</div>

          <!-- 验证状态提示 -->
          <div v-if="isStepError(step.key)" class="step-error">
            <t-icon name="error-circle" />
            <span>{{ getStepErrorMessage(step.key) }}</span>
          </div>
          <div v-else-if="isStepCompleted(step.key)" class="step-success">
            <t-icon name="check-circle" />
            <span>配置完成</span>
          </div>
        </div>

        <!-- 步骤连接线 -->
        <div v-if="step.order < totalSteps" class="step-connector">
          <div
            :class="[
              'connector-line',
              {
                completed: isStepCompleted(step.key),
              },
            ]"
          />
        </div>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="navigation-actions">
      <t-button
        v-if="canGoPrevious"
        variant="outline"
        size="small"
        class="action-button"
        @click="handlePreviousStep"
      >
        <template #icon>
          <t-icon name="chevron-left" />
        </template>
        上一步
      </t-button>

      <t-button
        v-if="canGoNext"
        theme="primary"
        size="small"
        class="action-button"
        @click="handleNextStep"
      >
        下一步
        <template #suffix>
          <t-icon name="chevron-right" />
        </template>
      </t-button>

      <t-button
        v-if="isLastStep && allStepsCompleted"
        theme="success"
        size="small"
        class="action-button complete-button"
        @click="handleComplete"
      >
        <template #icon>
          <t-icon name="check" />
        </template>
        完成配置
      </t-button>
    </div>

    <!-- 配置提示 -->
    <div class="navigation-tips">
      <div class="tip-item">
        <t-icon name="info-circle" class="tip-icon" />
        <span class="tip-text">{{ getCurrentStepTip() }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import type { ConfigStep, FormValidation, ValidationStatus } from '../types/index';

/**
 * 配置导航组件
 * 显示配置步骤、进度和导航功能
 */

// Props定义
interface Props {
  currentStep: ConfigStep; // 当前步骤
  validation: FormValidation; // 验证状态
}

const props = withDefaults(defineProps<Props>(), {
  currentStep: 'basic-info' as ConfigStep,
  validation: () => ({
    basicInfo: 'pending' as ValidationStatus,
    contact: 'pending' as ValidationStatus,
    business: 'pending' as ValidationStatus,
    style: 'valid' as ValidationStatus,
    overall: 'pending' as ValidationStatus,
  }),
});

// Emits定义
interface Emits {
  stepChange: [step: ConfigStep]; // 步骤切换事件
}

const emit = defineEmits<Emits>();

// ==================== 导航步骤配置 ====================

/**
 * 导航步骤定义
 */
const navigationSteps = [
  {
    key: 'basic-info' as ConfigStep,
    order: 1,
    title: '基础信息',
    description: '配置商户基本信息',
    icon: 'user',
    tip: '请填写商户名称、简介、Logo等基础信息',
  },
  {
    key: 'contact' as ConfigStep,
    order: 2,
    title: '联系方式',
    description: '设置联系方式信息',
    icon: 'phone',
    tip: '至少需要填写一种联系方式，建议填写客服电话',
  },
  {
    key: 'business' as ConfigStep,
    order: 3,
    title: '业务介绍',
    description: '编辑业务介绍内容',
    icon: 'file-text',
    tip: '详细介绍您的业务内容，有助于客户了解您的服务',
  },
  {
    key: 'style' as ConfigStep,
    order: 4,
    title: '样式设置',
    description: '自定义名片样式',
    icon: 'palette',
    tip: '选择合适的主题和颜色，让名片更具个性',
  },
];

// ==================== 计算属性 ====================

/**
 * 总步骤数
 */
const totalSteps = computed(() => navigationSteps.length);

/**
 * 已完成步骤数
 */
const completedSteps = computed(
  () => navigationSteps.filter((step) => isStepCompleted(step.key)).length
);

/**
 * 当前步骤索引
 */
const currentStepIndex = computed(() =>
  navigationSteps.findIndex((step) => step.key === props.currentStep)
);

/**
 * 是否可以返回上一步
 */
const canGoPrevious = computed(() => currentStepIndex.value > 0);

/**
 * 是否可以进入下一步
 */
const canGoNext = computed(
  () =>
    currentStepIndex.value < totalSteps.value - 1 && isStepCompleted(props.currentStep)
);

/**
 * 是否是最后一步
 */
const isLastStep = computed(() => currentStepIndex.value === totalSteps.value - 1);

/**
 * 所有步骤是否都已完成
 */
const allStepsCompleted = computed(() => completedSteps.value === totalSteps.value);

// ==================== 方法 ====================

/**
 * 判断步骤是否已完成
 */
const isStepCompleted = (step: ConfigStep): boolean => {
  const validationKey = getValidationKey(step);
  return props.validation[validationKey] === 'valid';
};

/**
 * 判断步骤是否有错误
 */
const isStepError = (step: ConfigStep): boolean => {
  const validationKey = getValidationKey(step);
  return props.validation[validationKey] === 'invalid';
};

/**
 * 判断步骤是否被禁用
 */
const isStepDisabled = (step: ConfigStep): boolean => {
  const stepIndex = navigationSteps.findIndex((s) => s.key === step);
  const currentIndex = currentStepIndex.value;

  // 只能访问当前步骤、已完成的步骤，或者下一步（如果当前步骤已完成）
  if (stepIndex <= currentIndex) {
    return false;
  }

  if (stepIndex === currentIndex + 1 && isStepCompleted(props.currentStep)) {
    return false;
  }

  return true;
};

/**
 * 获取验证状态对应的键名
 */
const getValidationKey = (step: ConfigStep): keyof FormValidation => {
  const keyMap: Record<ConfigStep, keyof FormValidation> = {
    'basic-info': 'basicInfo',
    contact: 'contact',
    business: 'business',
    style: 'style',
  };

  return keyMap[step];
};

/**
 * 获取步骤错误信息
 */
const getStepErrorMessage = (step: ConfigStep): string => {
  const errorMessages: Record<ConfigStep, string> = {
    'basic-info': '基础信息填写不完整',
    contact: '联系方式信息有误',
    business: '业务介绍内容不符合要求',
    style: '样式配置有问题',
  };

  return errorMessages[step] || '配置有误';
};

/**
 * 获取当前步骤提示
 */
const getCurrentStepTip = (): string => {
  const currentStepConfig = navigationSteps.find(
    (step) => step.key === props.currentStep
  );
  return currentStepConfig?.tip || '';
};

/**
 * 处理步骤点击
 */
const handleStepClick = (step: ConfigStep): void => {
  if (isStepDisabled(step)) {
    return;
  }

  emit('stepChange', step);
};

/**
 * 处理上一步
 */
const handlePreviousStep = (): void => {
  if (canGoPrevious.value) {
    const previousStep = navigationSteps[currentStepIndex.value - 1];
    emit('stepChange', previousStep.key);
  }
};

/**
 * 处理下一步
 */
const handleNextStep = (): void => {
  if (canGoNext.value) {
    const nextStep = navigationSteps[currentStepIndex.value + 1];
    emit('stepChange', nextStep.key);
  }
};

/**
 * 处理完成配置
 */
const handleComplete = (): void => {
  // 触发完成事件，由父组件处理
  emit('stepChange', 'style'); // 保持在最后一步
};
</script>

<style lang="scss" scoped>
.config-navigation {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fafafa;
  border-right: 1px solid #e6e6e6;

  .navigation-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e6e6e6;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #333333;
    }

    .progress-indicator {
      font-size: 12px;
      color: #666666;
      background-color: #f0f0f0;
      padding: 4px 8px;
      border-radius: 12px;
    }
  }

  .navigation-menu {
    flex: 1;
    padding: 16px 0;
    overflow-y: auto;

    .navigation-item {
      position: relative;
      display: flex;
      align-items: flex-start;
      padding: 16px 24px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover:not(.disabled) {
        background-color: #f0f8ff;
      }

      &.active {
        background-color: #e6f7ff;
        border-right: 3px solid #1890ff;

        .step-content .step-title {
          color: #1890ff;
          font-weight: 600;
        }
      }

      &.completed {
        .step-content .step-title {
          color: #52c41a;
        }

        .connector-line.completed {
          background-color: #52c41a;
        }
      }

      &.error {
        .step-content .step-title {
          color: #ff4d4f;
        }
      }

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .step-icon {
        width: 32px;
        height: 32px;
        margin-right: 12px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        font-size: 14px;

        .icon-completed {
          background-color: #52c41a;
          color: #ffffff;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }

        .icon-error {
          background-color: #ff4d4f;
          color: #ffffff;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }

        .icon-current {
          background-color: #1890ff;
          color: #ffffff;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
        }

        .icon-pending {
          background-color: #f0f0f0;
          color: #999999;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          border: 2px solid #d9d9d9;

          .step-number {
            font-size: 12px;
            font-weight: 600;
          }
        }
      }

      .step-content {
        flex: 1;
        min-width: 0;

        .step-title {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
          margin-bottom: 4px;
          line-height: 1.4;
        }

        .step-description {
          font-size: 12px;
          color: #666666;
          line-height: 1.4;
          margin-bottom: 8px;
        }

        .step-error,
        .step-success {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 11px;
          margin-top: 4px;

          :deep(.t-icon) {
            font-size: 12px;
          }
        }

        .step-error {
          color: #ff4d4f;
        }

        .step-success {
          color: #52c41a;
        }
      }

      .step-connector {
        position: absolute;
        left: 39px;
        top: 48px;
        width: 2px;
        height: 32px;

        .connector-line {
          width: 100%;
          height: 100%;
          background-color: #e6e6e6;
          transition: background-color 0.2s ease;

          &.completed {
            background-color: #52c41a;
          }
        }
      }
    }
  }

  .navigation-actions {
    padding: 16px 24px;
    border-top: 1px solid #e6e6e6;
    display: flex;
    gap: 8px;

    .action-button {
      flex: 1;

      &.complete-button {
        background: linear-gradient(135deg, #52c41a, #73d13d);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #73d13d, #95de64);
        }
      }
    }
  }

  .navigation-tips {
    padding: 12px 24px;
    background-color: #f6ffed;
    border-top: 1px solid #d9f7be;

    .tip-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;

      .tip-icon {
        font-size: 14px;
        color: #52c41a;
        margin-top: 1px;
        flex-shrink: 0;
      }

      .tip-text {
        font-size: 12px;
        color: #666666;
        line-height: 1.4;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .config-navigation {
    .navigation-header {
      padding: 16px 20px 12px;

      h3 {
        font-size: 14px;
      }
    }

    .navigation-menu {
      .navigation-item {
        padding: 12px 20px;

        .step-icon {
          width: 28px;
          height: 28px;
          margin-right: 10px;
        }

        .step-content {
          .step-title {
            font-size: 13px;
          }

          .step-description {
            font-size: 11px;
          }
        }

        .step-connector {
          left: 33px;
          top: 40px;
          height: 28px;
        }
      }
    }

    .navigation-actions {
      padding: 12px 20px;

      .action-button {
        font-size: 12px;
        padding: 0 12px;
      }
    }

    .navigation-tips {
      padding: 10px 20px;

      .tip-text {
        font-size: 11px;
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .config-navigation {
    background-color: #1f1f1f;
    border-right-color: #333333;

    .navigation-header {
      border-bottom-color: #333333;

      h3 {
        color: #ffffff;
      }

      .progress-indicator {
        background-color: #333333;
        color: #cccccc;
      }
    }

    .navigation-menu {
      .navigation-item {
        &:hover:not(.disabled) {
          background-color: rgba(24, 144, 255, 0.1);
        }

        &.active {
          background-color: rgba(24, 144, 255, 0.2);
        }

        .step-content {
          .step-title {
            color: #ffffff;
          }

          .step-description {
            color: #cccccc;
          }
        }

        .step-icon {
          .icon-pending {
            background-color: #333333;
            border-color: #555555;
            color: #cccccc;
          }
        }

        .step-connector {
          .connector-line {
            background-color: #333333;
          }
        }
      }
    }

    .navigation-actions {
      border-top-color: #333333;
    }

    .navigation-tips {
      background-color: #162312;
      border-top-color: #274916;

      .tip-text {
        color: #cccccc;
      }
    }
  }
}
</style>
