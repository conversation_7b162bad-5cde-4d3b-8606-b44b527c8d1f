<template>
  <div class="card-detail-footer">
    <!-- 左侧状态信息 -->
    <div class="footer-left">
      <!-- 自动保存状态 -->
      <div class="auto-save-section">
        <t-switch v-model="autoSaveEnabled" size="small" @change="handleAutoSaveToggle" />
        <span class="auto-save-label">自动保存</span>
      </div>

      <!-- 保存状态指示 -->
      <div class="save-indicator">
        <div v-if="hasUnsavedChanges" class="unsaved-changes">
          <t-icon name="edit" class="edit-icon" />
          <span>有未保存的更改</span>
        </div>
        <div v-else class="all-saved">
          <t-icon name="check-circle" class="check-icon" />
          <span>所有更改已保存</span>
        </div>
      </div>
    </div>

    <!-- 右侧操作按钮 -->
    <div class="footer-right">
      <!-- 保存按钮 -->
      <t-button
        :disabled="!canSave"
        :loading="isSaving"
        variant="outline"
        size="medium"
        class="save-button"
        @click="handleSave"
      >
        <template #icon>
          <t-icon name="save" />
        </template>
        {{ isSaving ? '保存中...' : '保存配置' }}
      </t-button>

      <!-- 预览按钮 -->
      <t-button
        variant="outline"
        size="medium"
        class="preview-button"
        @click="handlePreview"
      >
        <template #icon>
          <t-icon name="view" />
        </template>
        预览效果
      </t-button>

      <!-- 发布按钮 -->
      <t-button
        :disabled="!canPublish"
        :loading="isPublishing"
        theme="primary"
        size="medium"
        class="publish-button"
        @click="handlePublish"
      >
        <template #icon>
          <t-icon name="rocket" />
        </template>
        {{ isPublishing ? '发布中...' : '发布配置' }}
      </t-button>
    </div>

    <!-- 发布确认对话框 -->
    <t-dialog
      v-model:visible="showPublishDialog"
      header="确认发布配置"
      width="480px"
      :confirm-btn="publishDialogConfirmBtn"
      :cancel-btn="publishDialogCancelBtn"
      @confirm="confirmPublish"
      @cancel="cancelPublish"
    >
      <div class="publish-dialog-content">
        <div class="publish-warning">
          <t-icon name="info-circle" class="warning-icon" />
          <div class="warning-text">
            <p>发布后配置将立即生效，请确认以下信息：</p>
            <ul>
              <li>基础信息配置完整</li>
              <li>联系方式信息准确</li>
              <li>业务介绍内容合规</li>
              <li>样式设置符合预期</li>
            </ul>
          </div>
        </div>

        <div class="publish-note">
          <t-textarea
            v-model="publishNote"
            placeholder="发布说明（可选）"
            :maxlength="200"
            :autosize="{ minRows: 3, maxRows: 5 }"
            class="publish-note-input"
          />
        </div>
      </div>
    </t-dialog>

    <!-- 预览对话框 -->
    <t-dialog
      v-model:visible="showPreviewDialog"
      header="预览效果"
      width="800px"
      :footer="false"
      class="preview-dialog"
    >
      <div class="preview-dialog-content">
        <div class="preview-toolbar">
          <t-radio-group v-model="previewDevice" variant="default-filled" size="small">
            <t-radio-button value="mobile">
              <t-icon name="mobile" />
              手机
            </t-radio-button>
            <t-radio-button value="tablet">
              <t-icon name="tablet" />
              平板
            </t-radio-button>
            <t-radio-button value="desktop">
              <t-icon name="desktop" />
              桌面
            </t-radio-button>
          </t-radio-group>

          <t-button
            variant="text"
            size="small"
            class="refresh-button"
            @click="refreshPreview"
          >
            <template #icon>
              <t-icon name="refresh" />
            </template>
            刷新
          </t-button>
        </div>

        <div class="preview-container" :class="`preview-${previewDevice}`">
          <div class="preview-frame">
            <iframe :src="previewUrl" frameborder="0" class="preview-iframe" />
          </div>
        </div>
      </div>
    </t-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';

/**
 * Card-Detail页面底部组件
 * 包含保存、预览、发布等操作功能
 */

// Props定义
interface Props {
  canSave: boolean; // 是否可以保存
  canPublish: boolean; // 是否可以发布
  isSaving: boolean; // 是否正在保存
  isPublishing: boolean; // 是否正在发布
  hasUnsavedChanges: boolean; // 是否有未保存的更改
}

const props = withDefaults(defineProps<Props>(), {
  canSave: false,
  canPublish: false,
  isSaving: false,
  isPublishing: false,
  hasUnsavedChanges: false,
});

// Emits定义
interface Emits {
  save: []; // 保存事件
  publish: []; // 发布事件
  preview: []; // 预览事件
  autoSaveToggle: [enabled: boolean]; // 自动保存切换事件
}

const emit = defineEmits<Emits>();

// ==================== 本地状态 ====================

// 自动保存开关
const autoSaveEnabled = ref(true);

// 发布对话框状态
const showPublishDialog = ref(false);
const publishNote = ref('');

// 预览对话框状态
const showPreviewDialog = ref(false);
const previewDevice = ref<'mobile' | 'tablet' | 'desktop'>('mobile');
const previewUrl = ref('');

// ==================== 计算属性 ====================

/**
 * 发布对话框确认按钮配置
 */
const publishDialogConfirmBtn = computed(() => ({
  content: '确认发布',
  theme: 'primary',
  loading: props.isPublishing,
}));

/**
 * 发布对话框取消按钮配置
 */
const publishDialogCancelBtn = computed(() => ({
  content: '取消',
  theme: 'default',
}));

// ==================== 方法 ====================

/**
 * 处理保存操作
 */
const handleSave = (): void => {
  emit('save');
};

/**
 * 处理预览操作
 */
const handlePreview = (): void => {
  // 生成预览URL
  previewUrl.value = generatePreviewUrl();
  showPreviewDialog.value = true;
  emit('preview');
};

/**
 * 处理发布操作
 */
const handlePublish = (): void => {
  showPublishDialog.value = true;
};

/**
 * 确认发布
 */
const confirmPublish = (): void => {
  showPublishDialog.value = false;
  emit('publish');

  // 清空发布说明
  publishNote.value = '';
};

/**
 * 取消发布
 */
const cancelPublish = (): void => {
  showPublishDialog.value = false;
  publishNote.value = '';
};

/**
 * 处理自动保存切换
 */
const handleAutoSaveToggle = (enabled: boolean): void => {
  emit('autoSaveToggle', enabled);
};

/**
 * 刷新预览
 */
const refreshPreview = (): void => {
  // 重新生成预览URL
  previewUrl.value = `${generatePreviewUrl()}&t=${Date.now()}`;
  MessagePlugin.success('预览已刷新');
};

/**
 * 生成预览URL
 */
const generatePreviewUrl = (): string => {
  // 这里应该根据当前配置生成预览URL
  // 实际实现时需要调用API获取预览链接
  const baseUrl = 'https://example.com/preview';
  const configId = `temp-${Date.now()}`;
  return `${baseUrl}/${configId}?device=${previewDevice.value}`;
};

/**
 * 获取发布说明
 */
const getPublishNote = (): string => publishNote.value;

// ==================== 暴露给父组件的方法 ====================

defineExpose({
  getPublishNote,
  showPreview: () => {
    handlePreview();
  },
  showPublishDialog: () => {
    handlePublish();
  },
});
</script>

<style lang="scss" scoped>
.card-detail-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 72px;
  padding: 0 24px;
  background-color: #ffffff;
  border-top: 1px solid #e6e6e6;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.04);

  .footer-left {
    display: flex;
    align-items: center;
    gap: 24px;

    .auto-save-section {
      display: flex;
      align-items: center;
      gap: 8px;

      .auto-save-label {
        font-size: 14px;
        color: #666666;
        user-select: none;
      }
    }

    .save-indicator {
      display: flex;
      align-items: center;
      font-size: 14px;

      .unsaved-changes {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #faad14;

        .edit-icon {
          font-size: 16px;
        }
      }

      .all-saved {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #52c41a;

        .check-icon {
          font-size: 16px;
        }
      }
    }
  }

  .footer-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .save-button {
      color: #666666;
      border-color: #d9d9d9;

      &:not(:disabled):hover {
        color: #1890ff;
        border-color: #1890ff;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .preview-button {
      color: #666666;
      border-color: #d9d9d9;

      &:hover {
        color: #1890ff;
        border-color: #1890ff;
      }
    }

    .publish-button {
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }
}

// 发布对话框样式
.publish-dialog-content {
  .publish-warning {
    display: flex;
    gap: 12px;
    margin-bottom: 24px;
    padding: 16px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 6px;

    .warning-icon {
      font-size: 20px;
      color: #52c41a;
      margin-top: 2px;
      flex-shrink: 0;
    }

    .warning-text {
      flex: 1;

      p {
        margin: 0 0 12px 0;
        font-size: 14px;
        color: #333333;
        font-weight: 500;
      }

      ul {
        margin: 0;
        padding-left: 16px;
        font-size: 13px;
        color: #666666;

        li {
          margin-bottom: 4px;
        }
      }
    }
  }

  .publish-note {
    .publish-note-input {
      width: 100%;
    }
  }
}

// 预览对话框样式
:deep(.preview-dialog) {
  .t-dialog__body {
    padding: 0;
  }
}

.preview-dialog-content {
  .preview-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #fafafa;

    .refresh-button {
      color: #666666;

      &:hover {
        color: #1890ff;
      }
    }
  }

  .preview-container {
    padding: 24px;
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;

    .preview-frame {
      background-color: #ffffff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      transition: all 0.3s ease;

      .preview-iframe {
        width: 100%;
        height: 100%;
        border: none;
      }
    }

    &.preview-mobile {
      .preview-frame {
        width: 375px;
        height: 667px;
      }
    }

    &.preview-tablet {
      .preview-frame {
        width: 768px;
        height: 1024px;
      }
    }

    &.preview-desktop {
      .preview-frame {
        width: 1200px;
        height: 800px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card-detail-footer {
    padding: 0 16px;
    height: 64px;

    .footer-left {
      gap: 16px;

      .save-indicator {
        span {
          display: none;
        }
      }
    }

    .footer-right {
      gap: 8px;

      .save-button,
      .preview-button {
        :deep(.t-button__text) {
          display: none;
        }
      }

      .publish-button {
        font-size: 14px;
        padding: 0 12px;
      }
    }
  }

  .preview-dialog-content {
    .preview-container {
      padding: 16px;

      &.preview-tablet .preview-frame,
      &.preview-desktop .preview-frame {
        width: 100%;
        max-width: 600px;
        height: 400px;
      }
    }
  }
}

// 深色主题支持
@media (prefers-color-scheme: dark) {
  .card-detail-footer {
    background-color: #1f1f1f;
    border-top-color: #333333;

    .footer-left {
      .auto-save-section {
        .auto-save-label {
          color: #cccccc;
        }
      }
    }
  }

  .publish-dialog-content {
    .publish-warning {
      background-color: #162312;
      border-color: #274916;

      .warning-text {
        p {
          color: #ffffff;
        }

        ul {
          color: #cccccc;
        }
      }
    }
  }

  .preview-dialog-content {
    .preview-toolbar {
      background-color: #2a2a2a;
      border-bottom-color: #333333;
    }

    .preview-container {
      background-color: #1a1a1a;
    }
  }
}
</style>
