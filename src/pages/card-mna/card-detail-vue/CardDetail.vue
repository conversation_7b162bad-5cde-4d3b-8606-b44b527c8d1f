<template>
  <div class="card-detail-container">
    <!-- 主要内容区域 -->
    <div class="card-detail-content">
      <!-- 左侧配置面板 -->
      <LeftConfigPanel />

      <!-- 右侧预览面板 -->
      <RightPreviewPanel />
    </div>

    <!-- 加载遮罩 -->
    <div v-if="pageState.isLoading" class="loading-overlay">
      <t-loading size="large" text="加载中..." />
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { storeToRefs } from 'pinia';

// 组件导入
import LeftConfigPanel from './components/LeftConfigPanel.vue';
import RightPreviewPanel from './components/RightPreviewPanel.vue';

// 状态管理和类型导入
import { useCardDetailStore } from '@/stores/cardDetail';

// 组合式函数导入
import { useCardDetail } from './composables/useCardDetail';

/**
 * Card-Detail 主组件
 * 商家名片详情配置进度跟踪页面
 */

// 路由相关
const route = useRoute();

// 状态管理
const cardDetailStore = useCardDetailStore();
const { pageState } = storeToRefs(cardDetailStore);

// 组合式函数
const { initializePage } = useCardDetail();

// ==================== 生命周期钩子 ====================

onMounted(async () => {
  // 初始化页面数据
  const processId = route.query.processId as string;
  await initializePage(processId);
});
</script>

<style lang="scss" scoped>
.card-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  .card-detail-content {
    display: flex;
    flex: 1;
    overflow: hidden;
    gap: 24px;
    padding: 24px;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .card-detail-container {
    .card-detail-content {
      flex-direction: column;
      gap: 16px;
      padding: 16px;
    }
  }
}
</style>
