# Card-Detail 页面状态测试指南

## 🎯 测试目的

验证Card-Detail页面在不同业务状态下，按钮状态是否符合预期。

## 🚀 快速测试

### 1. 访问测试页面
```
http://localhost:8083/home/<USER>/card-detail-vue/test
```

### 2. 直接访问Card-Detail页面并切换场景
通过URL参数来测试不同状态：

```bash
# 新手用户 - 所有配置项都未配置
http://localhost:8083/home/<USER>/card-detail-vue?scenario=new-user

# 部分配置 - 基础信息已配置，其他未完成  
http://localhost:8083/home/<USER>/card-detail-vue?scenario=partial-configured

# 审核中 - 基础信息正在审核
http://localhost:8083/home/<USER>/card-detail-vue?scenario=auditing

# 审核驳回 - 基础信息被驳回
http://localhost:8083/home/<USER>/card-detail-vue?scenario=rejected

# 准备发布 - 所有配置完成，可以发布
http://localhost:8083/home/<USER>/card-detail-vue?scenario=ready-to-publish

# 优惠券接入 - 通过优惠券而非品牌会员
http://localhost:8083/home/<USER>/card-detail-vue?scenario=coupon-access

# 待发布状态
http://localhost:8083/home/<USER>/card-detail-vue?scenario=approved

# API错误测试
http://localhost:8083/home/<USER>/card-detail-vue?scenario=api-error

# 全部草稿状态
http://localhost:8083/home/<USER>/card-detail-vue?scenario=all-draft

# 双重接入 - 品牌会员和优惠券都接入
http://localhost:8083/home/<USER>/card-detail-vue?scenario=both-access
```

## 📋 预期结果验证

### 场景1: new-user (新手用户)
**预期状态:**
- 基础信息: `未配置` + `配置` 按钮 (primary主题)
- 名片服务: `未配置` + `配置` 按钮 (primary主题) 
- 品牌会员: `未接入` + `接入` 按钮 (primary主题)
- 发布按钮: **禁用状态**

### 场景2: partial-configured (部分配置)
**预期状态:**
- 基础信息: `已配置` + `查看` 按钮 (default主题)
- 名片服务: `配置中` + `配置` 按钮 (primary主题)
- 品牌会员: `未接入` + `接入` 按钮 (primary主题)
- 发布按钮: **禁用状态**

### 场景3: auditing (审核中)
**预期状态:**
- 基础信息: `审核中` + `查看` 按钮 (default主题)
- 名片服务: `已配置` + `查看` 按钮 (default主题)
- 品牌会员: `已接入` + `查看` 按钮 (default主题)
- 发布按钮: **禁用状态** (审核中不能发布)

### 场景4: rejected (审核驳回)
**预期状态:**
- 基础信息: `审核驳回` + `修改` 按钮 (primary主题)
- 名片服务: `已配置` + `查看` 按钮 (default主题)
- 品牌会员: `已接入` + `查看` 按钮 (default主题)
- 发布按钮: **禁用状态** (有驳回项不能发布)

### 场景5: ready-to-publish (准备发布)
**预期状态:**
- 基础信息: `已配置` + `查看` 按钮 (default主题)
- 名片服务: `已配置` + `查看` 按钮 (default主题)
- 品牌会员: `已接入` + `查看` 按钮 (default主题)
- 发布按钮: **启用状态** ✅

### 场景6: coupon-access (优惠券接入)
**预期状态:**
- 基础信息: `已配置` + `查看` 按钮 (default主题)
- 名片服务: `已配置` + `查看` 按钮 (default主题)
- 品牌会员: `已接入` + `查看` 按钮 (default主题) [通过优惠券接入也显示已接入]
- 发布按钮: **启用状态** ✅

### 场景7: approved (待发布)
**预期状态:**
- 基础信息: `审核通过` + `查看` 按钮 (default主题)
- 名片服务: `已配置` + `查看` 按钮 (default主题)
- 品牌会员: `已接入` + `查看` 按钮 (default主题)
- 发布按钮: **启用状态** ✅

### 场景8: api-error (API错误)
**预期状态:**
- 基础信息: `未配置` + `配置` 按钮 (primary主题) [API错误时显示默认状态]
- 名片服务: `已配置` + `查看` 按钮 (default主题)
- 品牌会员: `已接入` + `查看` 按钮 (default主题)
- 发布按钮: **禁用状态** (有API错误不能发布)

## 🔧 测试API状态

可以通过以下API验证mock数据是否正确：

```bash
# 测试基础信息API
curl "http://localhost:8083/xdc/mchcardinfomgrweb/get-latest-card-info-publish-process?scenario=new-user"

# 测试服务配置API  
curl "http://localhost:8083/xdc/mchcardinfomgrweb/get-latest-service-publish-process?scenario=ready-to-publish"

# 测试品牌会员API
curl "http://localhost:8083/xdc/mchcardinfomgrweb/get-brand-member-access-state?scenario=ready-to-publish"

# 测试优惠券API
curl "http://localhost:8083/xdc/mchcardinfomgrweb/get-shake-coupon-access-state?scenario=coupon-access"
```

## 🎨 状态映射表

| 进程状态 | 数值 | 状态文本 | 按钮文本 | 按钮主题 |
|---------|------|----------|----------|----------|
| INIT | 0 | 未配置 | 配置 | primary |
| DRAFT | 1 | 配置中 | 配置 | primary |
| AUDITING | 2 | 审核中 | 查看 | default |
| AUDIT_PASS | 3 | 已配置 | 查看 | default |
| REJECTED | 4 | 审核驳回 | 修改 | primary |
| APPROVED | 6 | 审核通过 | 查看 | default |

| 接入状态 | 数值 | 状态文本 | 按钮文本 | 按钮主题 |
|---------|------|----------|----------|----------|
| NONE | 0 | 未接入 | 接入 | primary |
| OPEN | 1 | 已接入 | 查看 | default |

## ✅ 核心验证点

1. **状态文本准确性**: 检查状态标签是否正确显示
2. **按钮文本正确性**: 检查按钮显示的文字是否符合业务逻辑
3. **按钮主题一致性**: primary主题用于需要操作的按钮，default主题用于查看类按钮
4. **发布逻辑正确性**: 
   - 只有当基础信息和服务都审核通过，且品牌会员或优惠券至少有一个接入时，发布按钮才启用
   - 任何一个条件不满足都应该禁用发布按钮
5. **错误状态处理**: API错误时应该显示合适的默认状态

## 🐛 问题排查

如果按钮状态不符合预期，请检查：

1. **URL参数**: 确保?scenario=xxx参数正确传递
2. **Mock数据**: 检查对应场景的mock数据是否正确
3. **状态计算**: 检查tutorial.ts中的状态计算逻辑
4. **API响应**: 检查网络面板中API的返回数据
5. **Store状态**: 检查Vue DevTools中的store状态是否正确更新

## 🔄 持续验证

建议在每次修改状态计算逻辑后，都要跑一遍所有场景的测试，确保：
- 现有功能不受影响
- 新的状态逻辑符合业务预期
- 按钮状态变化逻辑正确