import { describe, it, expect } from 'vitest';
import {
  calcCardInfoConfig,
  calcCardServiceConfig,
  calcBrandOrCouponAccessConfig,
  calcPublishStatus,
} from '@/pages/card-mna/common/model/tutorial';

/**
 * 测试状态计算逻辑是否正确
 * 这些是核心的业务逻辑，确保按钮状态符合预期
 */

// 进程状态枚举
enum ProcessState {
  INIT = 0, // 初始状态
  DRAFT = 1, // 草稿
  AUDITING = 2, // 待审核
  AUDIT_PASS = 3, // 审核通过
  REJECTED = 4, // 审核驳回
  CANCELED = 5, // 已作废
  APPROVED = 6, // 待发布
  PUBLISHING = 7, // 发布中
  PUBLISHED = 8, // 已完成
}

// 接入状态枚举
enum AccessState {
  NONE = 0,
  OPEN = 1,
  CLOSE = 2,
}

describe('状态计算逻辑测试', () => {
  describe('基础信息配置状态计算', () => {
    it('初始状态应该显示"配置"', () => {
      const result = calcCardInfoConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.INIT,
            },
          },
        },
        false
      );

      expect(result.tipsText).toBe('未配置');
      expect(result.text).toBe('配置');
      expect(result.buttonStyle).toBe('primary');
    });

    it('草稿状态应该显示"配置"', () => {
      const result = calcCardInfoConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.DRAFT,
            },
          },
        },
        false
      );

      expect(result.tipsText).toBe('配置中');
      expect(result.text).toBe('配置');
      expect(result.buttonStyle).toBe('primary');
    });

    it('审核中状态应该显示"查看"', () => {
      const result = calcCardInfoConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.AUDITING,
            },
          },
        },
        false
      );

      expect(result.tipsText).toBe('审核中');
      expect(result.text).toBe('查看');
      expect(result.buttonStyle).toBe('default');
    });

    it('审核通过状态应该显示"查看"', () => {
      const result = calcCardInfoConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.AUDIT_PASS,
            },
          },
        },
        false
      );

      expect(result.tipsText).toBe('已配置');
      expect(result.text).toBe('查看');
      expect(result.buttonStyle).toBe('default');
    });

    it('审核驳回状态应该显示"修改"', () => {
      const result = calcCardInfoConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.REJECTED,
            },
          },
        },
        false
      );

      expect(result.tipsText).toBe('审核驳回');
      expect(result.text).toBe('修改');
      expect(result.buttonStyle).toBe('primary');
    });

    it('待发布状态应该显示"查看"', () => {
      const result = calcCardInfoConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.APPROVED,
            },
          },
        },
        false
      );

      expect(result.tipsText).toBe('审核通过');
      expect(result.text).toBe('查看');
      expect(result.buttonStyle).toBe('default');
    });

    it('API错误时应该显示"配置"', () => {
      const result = calcCardInfoConfig(
        {
          code: 1, // 错误码
          message: 'API调用失败',
        },
        false
      );

      expect(result.tipsText).toBe('未配置');
      expect(result.text).toBe('配置');
      expect(result.buttonStyle).toBe('primary');
    });
  });

  describe('服务配置状态计算', () => {
    it('服务配置逻辑应该与基础信息配置一致', () => {
      const auditPassResult = calcCardServiceConfig(
        {
          code: 0,
          data: {
            process: {
              state: ProcessState.AUDIT_PASS,
            },
          },
        },
        false
      );

      expect(auditPassResult.tipsText).toBe('已配置');
      expect(auditPassResult.text).toBe('查看');
      expect(auditPassResult.buttonStyle).toBe('default');
    });
  });

  describe('品牌会员或优惠券接入状态计算', () => {
    it('品牌会员已接入时应该显示"查看"', () => {
      const result = calcBrandOrCouponAccessConfig(
        {
          code: 0,
          data: { state: AccessState.OPEN },
        },
        {
          code: 0,
          data: { state: AccessState.NONE },
        }
      );

      expect(result.tipsText).toBe('已接入');
      expect(result.text).toBe('查看');
      expect(result.buttonStyle).toBe('default');
    });

    it('优惠券已接入时应该显示"查看"', () => {
      const result = calcBrandOrCouponAccessConfig(
        {
          code: 0,
          data: { state: AccessState.NONE },
        },
        {
          code: 0,
          data: { state: AccessState.OPEN },
        }
      );

      expect(result.tipsText).toBe('已接入');
      expect(result.text).toBe('查看');
      expect(result.buttonStyle).toBe('default');
    });

    it('两个都未接入时应该显示"接入"', () => {
      const result = calcBrandOrCouponAccessConfig(
        {
          code: 0,
          data: { state: AccessState.NONE },
        },
        {
          code: 0,
          data: { state: AccessState.NONE },
        }
      );

      expect(result.tipsText).toBe('未接入');
      expect(result.text).toBe('接入');
      expect(result.buttonStyle).toBe('primary');
    });

    it('两个都已接入时应该显示"查看"', () => {
      const result = calcBrandOrCouponAccessConfig(
        {
          code: 0,
          data: { state: AccessState.OPEN },
        },
        {
          code: 0,
          data: { state: AccessState.OPEN },
        }
      );

      expect(result.tipsText).toBe('已接入');
      expect(result.text).toBe('查看');
      expect(result.buttonStyle).toBe('default');
    });
  });

  describe('发布按钮状态计算', () => {
    it('所有条件满足时发布按钮应该启用', () => {
      const apisResult = [
        null, // tutorialPublish 在card-detail页面不需要
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { state: AccessState.OPEN },
        },
        {
          code: 0,
          data: { state: AccessState.NONE },
        },
      ];

      const result = calcPublishStatus(apisResult);
      expect(result).toBe(false); // false表示不禁用，即启用
    });

    it('基础信息未完成时发布按钮应该禁用', () => {
      const apisResult = [
        null,
        {
          code: 0,
          data: { process: { state: ProcessState.INIT } }, // 未完成
        },
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { state: AccessState.OPEN },
        },
        {
          code: 0,
          data: { state: AccessState.NONE },
        },
      ];

      const result = calcPublishStatus(apisResult);
      expect(result).toBe(true); // true表示禁用
    });

    it('服务配置未完成时发布按钮应该禁用', () => {
      const apisResult = [
        null,
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { process: { state: ProcessState.DRAFT } }, // 草稿状态
        },
        {
          code: 0,
          data: { state: AccessState.OPEN },
        },
        {
          code: 0,
          data: { state: AccessState.NONE },
        },
      ];

      const result = calcPublishStatus(apisResult);
      expect(result).toBe(true); // true表示禁用
    });

    it('品牌会员和优惠券都未接入时发布按钮应该禁用', () => {
      const apisResult = [
        null,
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { state: AccessState.NONE }, // 未接入
        },
        {
          code: 0,
          data: { state: AccessState.NONE }, // 未接入
        },
      ];

      const result = calcPublishStatus(apisResult);
      expect(result).toBe(true); // true表示禁用
    });

    it('API调用失败时发布按钮应该禁用', () => {
      const apisResult = [
        null,
        {
          code: 1, // 错误码
          message: 'API调用失败',
        },
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { state: AccessState.OPEN },
        },
        {
          code: 0,
          data: { state: AccessState.NONE },
        },
      ];

      const result = calcPublishStatus(apisResult);
      expect(result).toBe(true); // true表示禁用
    });

    it('通过优惠券接入方式时发布按钮应该启用', () => {
      const apisResult = [
        null,
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { process: { state: ProcessState.AUDIT_PASS } },
        },
        {
          code: 0,
          data: { state: AccessState.NONE }, // 品牌会员未接入
        },
        {
          code: 0,
          data: { state: AccessState.OPEN }, // 优惠券已接入
        },
      ];

      const result = calcPublishStatus(apisResult);
      expect(result).toBe(false); // false表示不禁用，即启用
    });
  });
});
