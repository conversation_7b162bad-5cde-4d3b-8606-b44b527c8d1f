/**
 * Card-Detail页面不同状态的Mock数据
 * 用于测试按钮状态和显示逻辑
 */

// 进程状态枚举 (与tutorial.ts保持一致)
export enum ProcessState {
  INIT = 0, // 初始状态
  DRAFT = 1, // 草稿
  AUDITING = 2, // 待审核
  AUDIT_PASS = 3, // 审核通过
  REJECTED = 4, // 审核驳回
  CANCELED = 5, // 已作废
  APPROVED = 6, // 待发布
  PUBLISHING = 7, // 发布中
  PUBLISHED = 8, // 已完成
}

// 接入状态枚举
export enum AccessState {
  NONE = 0,
  OPEN = 1,
  CLOSE = 2,
}

/**
 * 场景1: 新手用户 - 所有配置项都未配置
 * 预期: 所有按钮显示"配置"，发布按钮禁用
 */
export const mockNewUserState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.INIT,
        processId: 'card-init-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.INIT,
        processId: 'service-init-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.NONE,
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE,
    },
  },
};

/**
 * 场景2: 部分配置完成 - 基础信息已配置，服务配置中，品牌未接入
 * 预期: 基础信息显示"查看"，服务显示"配置"，品牌显示"接入"，发布按钮禁用
 */
export const mockPartialConfiguredState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS, // 审核通过 = 已配置
        processId: 'card-configured-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.DRAFT, // 草稿状态 = 配置中
        processId: 'service-draft-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.NONE, // 未接入
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE, // 未接入
    },
  },
};

/**
 * 场景3: 审核中状态 - 基础信息审核中
 * 预期: 基础信息显示"查看"(审核中)，按钮禁用
 */
export const mockAuditingState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDITING, // 审核中
        processId: 'card-auditing-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS,
        processId: 'service-pass-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.OPEN, // 已接入
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE,
    },
  },
};

/**
 * 场景4: 审核驳回状态 - 基础信息被驳回
 * 预期: 基础信息显示"修改"，按钮主题为primary
 */
export const mockRejectedState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.REJECTED, // 审核驳回
        processId: 'card-rejected-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS,
        processId: 'service-pass-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.OPEN,
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE,
    },
  },
};

/**
 * 场景5: 全部配置完成 - 可以发布
 * 预期: 所有配置项显示"查看"，发布按钮启用
 */
export const mockReadyToPublishState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS, // 审核通过
        processId: 'card-ready-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS, // 审核通过
        processId: 'service-ready-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.OPEN, // 品牌会员已接入
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE, // 优惠券可以不接入，只要有一个即可
    },
  },
};

/**
 * 场景6: 优惠券接入方式 - 通过优惠券接入而非品牌会员
 * 预期: 品牌配置项显示"查看"，发布按钮启用
 */
export const mockCouponAccessState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS,
        processId: 'card-ready-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS,
        processId: 'service-ready-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.NONE, // 品牌会员未接入
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.OPEN, // 优惠券已接入
    },
  },
};

/**
 * 场景7: API错误状态 - 模拟API调用失败
 * 预期: 发布按钮禁用，显示错误状态
 */
export const mockApiErrorState = {
  latestCardInfoPublishProcess: {
    code: 1, // 错误码
    message: 'API调用失败',
    data: null,
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS,
        processId: 'service-pass-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.OPEN,
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE,
    },
  },
};

/**
 * 场景8: 待发布状态 - 审核通过但未发布
 * 预期: 基础信息显示"查看"(审核通过)，按钮为default主题
 */
export const mockApprovedState = {
  latestCardInfoPublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.APPROVED, // 待发布
        processId: 'card-approved-id',
      },
    },
  },
  latestServicePublishProcess: {
    code: 0,
    data: {
      process: {
        state: ProcessState.AUDIT_PASS,
        processId: 'service-pass-id',
      },
    },
  },
  brandMemberAccess: {
    code: 0,
    data: {
      state: AccessState.OPEN,
    },
  },
  shakeCouponAccess: {
    code: 0,
    data: {
      state: AccessState.NONE,
    },
  },
};

/**
 * 获取不同状态的期望结果
 */
export const getExpectedResults = () => ({
  newUser: {
    basicInfo: {
      statusText: '未配置',
      buttonText: '配置',
      buttonTheme: 'primary',
      completed: false,
    },
    serviceInfo: {
      statusText: '未配置',
      buttonText: '配置',
      buttonTheme: 'primary',
      completed: false,
    },
    brandInfo: {
      statusText: '未接入',
      buttonText: '接入',
      buttonTheme: 'primary',
      completed: false,
    },
    canPublish: false,
  },
  partialConfigured: {
    basicInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    serviceInfo: {
      statusText: '配置中',
      buttonText: '配置',
      buttonTheme: 'primary',
      completed: false,
    },
    brandInfo: {
      statusText: '未接入',
      buttonText: '接入',
      buttonTheme: 'primary',
      completed: false,
    },
    canPublish: false,
  },
  auditing: {
    basicInfo: {
      statusText: '审核中',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    serviceInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    brandInfo: {
      statusText: '已接入',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    canPublish: false,
  },
  rejected: {
    basicInfo: {
      statusText: '审核驳回',
      buttonText: '修改',
      buttonTheme: 'primary',
      completed: false,
    },
    serviceInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    brandInfo: {
      statusText: '已接入',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    canPublish: false,
  },
  readyToPublish: {
    basicInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    serviceInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    brandInfo: {
      statusText: '已接入',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    canPublish: true,
  },
  couponAccess: {
    basicInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    serviceInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    brandInfo: {
      statusText: '已接入',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    canPublish: true,
  },
  apiError: {
    basicInfo: {
      statusText: '未配置',
      buttonText: '配置',
      buttonTheme: 'primary',
      completed: false,
    },
    serviceInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    brandInfo: {
      statusText: '已接入',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    canPublish: false,
  },
  approved: {
    basicInfo: {
      statusText: '审核通过',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    serviceInfo: {
      statusText: '已配置',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    brandInfo: {
      statusText: '已接入',
      buttonText: '查看',
      buttonTheme: 'default',
      completed: true,
    },
    canPublish: true,
  },
});
