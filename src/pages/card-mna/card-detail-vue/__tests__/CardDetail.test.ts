import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import { createRouter, createWebHistory } from 'vue-router';

// Mock整个store模块
vi.mock('@/stores/cardDetail', () => ({
  useCardDetailStore: () => ({
    pageState: {
      isLoading: false,
      processId: null,
    },
  }),
}));

// Mock组件
vi.mock('../components/LeftConfigPanel.vue', () => ({
  default: {
    name: 'LeftConfigPanel',
    template: '<div data-testid="left-config-panel">Left Config Panel</div>',
  },
}));

vi.mock('../components/RightPreviewPanel.vue', () => ({
  default: {
    name: 'RightPreviewPanel',
    template: '<div data-testid="right-preview-panel">Right Preview Panel</div>',
  },
}));

// Mock composables
vi.mock('../composables/useCardDetail', () => ({
  useCardDetail: () => ({
    initializePage: vi.fn(),
  }),
}));

// Mock vue-router
vi.mock('vue-router', () => ({
  useRoute: () => ({
    query: { processId: 'test-process-id' },
  }),
}));

// 在这里延迟导入组件，确保所有mock都已设置
const CardDetail = await import('../CardDetail.vue');

describe('CardDetail', () => {
  let pinia: any;
  let router: any;

  beforeEach(() => {
    pinia = createPinia();
    setActivePinia(pinia);

    router = createRouter({
      history: createWebHistory(),
      routes: [{ path: '/card-detail', component: CardDetail }],
    });
  });

  it('应该正确渲染Card-Detail页面结构', async () => {
    const wrapper = mount(CardDetail.default, {
      global: {
        plugins: [pinia, router],
      },
    });

    // 检查主容器
    expect(wrapper.find('.card-detail-container').exists()).toBe(true);

    // 检查内容区域
    expect(wrapper.find('.card-detail-content').exists()).toBe(true);

    // 检查左右面板
    expect(wrapper.find('[data-testid="left-config-panel"]').exists()).toBe(true);
    expect(wrapper.find('[data-testid="right-preview-panel"]').exists()).toBe(true);
  });

  it('应该在加载状态下显示加载遮罩', async () => {
    // Mock store with loading state
    vi.doMock('@/stores/cardDetail', () => ({
      useCardDetailStore: () => ({
        pageState: {
          isLoading: true,
          processId: null,
        },
      }),
    }));

    const wrapper = mount(CardDetail.default, {
      global: {
        plugins: [pinia, router],
      },
    });

    expect(wrapper.find('.loading-overlay').exists()).toBe(true);
    expect(wrapper.text()).toContain('加载中...');
  });

  it('应该在非加载状态下隐藏加载遮罩', async () => {
    const wrapper = mount(CardDetail.default, {
      global: {
        plugins: [pinia, router],
      },
    });

    expect(wrapper.find('.loading-overlay').exists()).toBe(false);
  });

  it('应该响应式布局在移动端正确显示', async () => {
    const wrapper = mount(CardDetail.default, {
      global: {
        plugins: [pinia, router],
      },
    });

    // 检查响应式类是否存在
    expect(wrapper.find('.card-detail-content').classes()).toContain(
      'card-detail-content'
    );
  });
});
