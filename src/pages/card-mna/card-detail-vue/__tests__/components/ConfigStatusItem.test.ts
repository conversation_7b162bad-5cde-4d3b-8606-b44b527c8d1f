import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import ConfigStatusItem from '../../components/ConfigStatusItem.vue';
import type { ConfigItem } from '../../types';

// 创建一个全局的TDesign组件mock
const globalComponents = {
  't-button': {
    name: 't-button',
    props: ['theme', 'disabled'],
    template: '<button :class="theme" :disabled="disabled"><slot /></button>',
  },
};

describe('ConfigStatusItem', () => {
  const mockConfigItem: ConfigItem = {
    id: 'basic-info',
    title: '基础信息',
    description: '配置品牌的基础信息，提供电话、在线客服等用户咨询途径',
    completed: false,
    route: '/home/<USER>/card-config',
    statusText: '待配置',
    buttonText: '去配置',
    buttonTheme: 'primary',
  };

  it('应该正确渲染配置项信息', () => {
    const wrapper = mount(ConfigStatusItem, {
      props: {
        item: mockConfigItem,
      },
      global: {
        components: globalComponents,
      },
    });

    // 检查标题
    expect(wrapper.find('.config-title').text()).toBe('基础信息');

    // 检查描述
    expect(wrapper.find('.config-description').text()).toBe(mockConfigItem.description);

    // 检查状态标签
    expect(wrapper.find('.config-status-tag').text()).toBe('待配置');

    // 检查按钮文本
    expect(wrapper.find('button').text()).toBe('去配置');
  });

  it('应该根据完成状态显示正确的样式类', () => {
    const completedItem = { ...mockConfigItem, completed: true };
    const wrapper = mount(ConfigStatusItem, {
      props: {
        item: completedItem,
      },
      global: {
        components: globalComponents,
      },
    });

    expect(wrapper.find('.config-status-tag').classes()).toContain('status-completed');
  });

  it('应该根据未完成状态显示正确的样式类', () => {
    const wrapper = mount(ConfigStatusItem, {
      props: {
        item: mockConfigItem,
      },
      global: {
        components: globalComponents,
      },
    });

    expect(wrapper.find('.config-status-tag').classes()).toContain('status-pending');
  });

  it('应该在点击按钮时触发navigate事件', async () => {
    const wrapper = mount(ConfigStatusItem, {
      props: {
        item: mockConfigItem,
      },
      global: {
        components: globalComponents,
      },
    });

    await wrapper.find('button').trigger('click');

    // 检查是否触发了navigate事件
    expect(wrapper.emitted('navigate')).toBeTruthy();
    expect(wrapper.emitted('navigate')?.[0]).toEqual([mockConfigItem]);
  });

  it('应该使用默认的状态文本和按钮文本', () => {
    const itemWithoutStatusText = {
      ...mockConfigItem,
      statusText: undefined,
      buttonText: undefined,
      completed: true,
    };

    const wrapper = mount(ConfigStatusItem, {
      props: {
        item: itemWithoutStatusText,
      },
      global: {
        components: globalComponents,
      },
    });

    // 对于已完成的项目，应该显示默认状态文本
    expect(wrapper.find('.config-status-tag').text()).toBe('已配置');
    expect(wrapper.find('button').text()).toBe('重新配置');
  });

  it('应该使用默认的按钮主题', () => {
    const itemWithoutTheme = {
      ...mockConfigItem,
      buttonTheme: undefined,
      completed: true,
    };

    const wrapper = mount(ConfigStatusItem, {
      props: {
        item: itemWithoutTheme,
      },
      global: {
        components: globalComponents,
      },
    });

    // 已完成项目的默认主题应该是default
    expect(wrapper.find('button').classes()).toContain('default');
  });
});
