/**
 * Card-Detail 页面入口文件
 * 导出主要组件和类型定义
 */

// 主组件
export { default as CardDetail } from './CardDetail.vue';

// 子组件
export { default as CardDetailHeader } from './components/CardDetailHeader.vue';
export { default as CardDetailFooter } from './components/CardDetailFooter.vue';
export { default as ConfigNavigation } from './components/ConfigNavigation.vue';

// 组合式函数
export { useCardDetail } from './composables/useCardDetail';
export { usePreview } from './composables/usePreview';

// 类型定义
export type {
  ConfigItem,
  PageState,
  PreviewData,
  ApiResponse,
  ConfigStatusResponse,
} from './types/index';
