import { onUnmounted } from 'vue';
import { useCardDetailStore } from '@/stores/cardDetail';

/**
 * Card-Detail页面主逻辑组合式函数
 * 简化的页面初始化和生命周期管理
 */
export function useCardDetail() {
  const cardDetailStore = useCardDetailStore();

  // 自动刷新定时器
  let refreshTimer: NodeJS.Timeout | null = null;

  /**
   * 初始化页面数据
   */
  const initializePage = async (processId?: string): Promise<void> => {
    await cardDetailStore.initializePage(processId);

    // 启动定期刷新（每30秒）
    startAutoRefresh();
  };

  /**
   * 启动自动刷新
   */
  const startAutoRefresh = (): void => {
    // 清除旧的定时器
    if (refreshTimer) {
      clearInterval(refreshTimer);
    }

    // 每30秒自动刷新配置状态
    refreshTimer = setInterval(async () => {
      try {
        await cardDetailStore.refreshConfigStatus();
      } catch (error) {
        console.error('自动刷新失败:', error);
      }
    }, 30000);
  };

  /**
   * 停止自动刷新
   */
  const stopAutoRefresh = (): void => {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
  };

  /**
   * 手动刷新所有数据
   */
  const manualRefresh = async (): Promise<void> => {
    try {
      await Promise.all([
        cardDetailStore.refreshConfigStatus(),
        cardDetailStore.refreshPreviewData(),
      ]);
    } catch (error) {
      console.error('手动刷新失败:', error);
    }
  };

  /**
   * 页面卸载前的清理
   */
  const handleBeforeUnload = (): void => {
    stopAutoRefresh();
  };

  // 组件卸载时自动清理
  onUnmounted(() => {
    handleBeforeUnload();
  });

  return {
    // 初始化方法
    initializePage,

    // 刷新方法
    manualRefresh,
    startAutoRefresh,
    stopAutoRefresh,

    // 生命周期方法
    handleBeforeUnload,
  };
}
