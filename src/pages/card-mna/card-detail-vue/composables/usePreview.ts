import { ref } from 'vue';
import { useCardDetailStore } from '@/stores/cardDetail';
import type { PreviewData } from '../types';

/**
 * 预览相关的组合式函数
 */
export function usePreview() {
  const cardDetailStore = useCardDetailStore();

  /**
   * 生成预览数据
   * @returns 处理后的预览数据
   */
  const generatePreviewData = (): PreviewData | null => {
    return cardDetailStore.previewData;
  };

  /**
   * 验证预览数据的完整性
   * @param data 预览数据
   * @returns 是否有效
   */
  const validatePreviewData = (data: PreviewData | null): boolean => {
    if (!data) return false;
    return !!(data.cardId && data.cardName);
  };

  /**
   * 获取当前设备类型
   */
  const currentDevice = ref<'mobile' | 'tablet' | 'desktop'>('mobile');

  /**
   * 切换设备类型
   * @param device 设备类型
   */
  const changeDevice = (device: 'mobile' | 'tablet' | 'desktop') => {
    currentDevice.value = device;
    // 可以在这里添加额外的逻辑，例如保存用户偏好等
  };

  /**
   * 是否处于全屏模式
   */
  const isFullscreen = ref(false);

  /**
   * 切换全屏模式
   */
  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
  };

  /**
   * 刷新预览
   */
  const refreshPreview = async () => {
    // 这里可以添加刷新预览的逻辑
    // 例如，重新获取配置数据、清除缓存等
    await cardDetailStore.refreshPreviewData();
  };

  return {
    generatePreviewData,
    validatePreviewData,
    currentDevice,
    changeDevice,
    isFullscreen,
    toggleFullscreen,
    refreshPreview,
  };
}
