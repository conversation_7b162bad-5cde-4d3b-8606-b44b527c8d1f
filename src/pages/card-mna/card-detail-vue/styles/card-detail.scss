/**
 * Card-Detail 页面样式文件
 * 包含页面整体样式、组件样式和响应式设计
 */

// ==================== 变量定义 ====================

// 颜色变量
$primary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #ff4d4f;
$text-color: #333333;
$text-color-secondary: #666666;
$text-color-disabled: #999999;
$border-color: #e6e6e6;
$background-color: #ffffff;
$background-color-light: #fafafa;
$background-color-dark: #f5f5f5;

// 尺寸变量
$header-height: 64px;
$footer-height: 72px;
$sidebar-width: 480px;
$border-radius: 6px;
$box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

// 断点变量
$breakpoint-mobile: 768px;
$breakpoint-tablet: 1024px;
$breakpoint-desktop: 1200px;

// ==================== 全局样式 ====================

.card-detail-page {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  line-height: 1.5;
  color: $text-color;
  background-color: $background-color-dark;
}

// ==================== 布局样式 ====================

.card-detail-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;

  .layout-header {
    height: $header-height;
    background-color: $background-color;
    border-bottom: 1px solid $border-color;
    z-index: 100;
  }

  .layout-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    .content-sidebar {
      width: $sidebar-width;
      background-color: $background-color;
      border-right: 1px solid $border-color;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .content-main {
      flex: 1;
      background-color: $background-color-dark;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
  }

  .layout-footer {
    height: $footer-height;
    background-color: $background-color;
    border-top: 1px solid $border-color;
    z-index: 100;
  }
}

// ==================== 组件样式 ====================

// 表单样式
.config-form {
  .form-section {
    margin-bottom: 32px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;

      .section-icon {
        font-size: 18px;
        color: $primary-color;
      }
    }

    .section-content {
      padding-left: 26px;
    }
  }

  .form-item {
    margin-bottom: 24px;

    .form-label {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      font-weight: 500;
      color: $text-color;

      .required-mark {
        color: $error-color;
        margin-left: 4px;
      }

      .label-tip {
        margin-left: 8px;
        color: $text-color-secondary;
        font-size: 12px;
      }
    }

    .form-control {
      width: 100%;

      &.has-error {
        border-color: $error-color;
        box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
      }
    }

    .form-error {
      margin-top: 4px;
      font-size: 12px;
      color: $error-color;
      display: flex;
      align-items: center;
      gap: 4px;

      .error-icon {
        font-size: 14px;
      }
    }

    .form-help {
      margin-top: 4px;
      font-size: 12px;
      color: $text-color-secondary;
    }
  }
}

// 预览样式
.preview-container {
  .preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px;
    background-color: $background-color;
    border-bottom: 1px solid $border-color;

    .preview-title {
      font-size: 16px;
      font-weight: 600;
      color: $text-color;
    }

    .preview-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .preview-content {
    flex: 1;
    padding: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;

    .preview-frame {
      background-color: $background-color;
      border-radius: $border-radius;
      box-shadow: $box-shadow;
      overflow: hidden;
      transition: all 0.3s ease;

      &.device-mobile {
        width: 375px;
        height: 667px;
      }

      &.device-tablet {
        width: 768px;
        height: 1024px;
      }

      &.device-desktop {
        width: 1200px;
        height: 800px;
      }
    }
  }
}

// 状态指示样式
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;

  &.status-success {
    background-color: #f6ffed;
    color: $success-color;
    border: 1px solid #b7eb8f;
  }

  &.status-warning {
    background-color: #fffbe6;
    color: $warning-color;
    border: 1px solid #ffe58f;
  }

  &.status-error {
    background-color: #fff2f0;
    color: $error-color;
    border: 1px solid #ffccc7;
  }

  &.status-info {
    background-color: #f0f8ff;
    color: $primary-color;
    border: 1px solid #91d5ff;
  }

  .status-icon {
    font-size: 14px;
  }
}

// 加载状态样式
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: $text-color-secondary;

  .loading-icon {
    font-size: 32px;
    margin-bottom: 16px;
    animation: spin 1s linear infinite;
  }

  .loading-text {
    font-size: 14px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: $text-color-secondary;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: $text-color;
  }

  .empty-description {
    font-size: 14px;
    text-align: center;
    line-height: 1.6;
  }

  .empty-actions {
    margin-top: 24px;
  }
}

// 图片上传样式
.image-upload {
  .upload-area {
    border: 2px dashed $border-color;
    border-radius: $border-radius;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: $primary-color;
      background-color: #f0f8ff;
    }

    &.dragover {
      border-color: $primary-color;
      background-color: #f0f8ff;
    }

    .upload-icon {
      font-size: 32px;
      color: $text-color-secondary;
      margin-bottom: 12px;
    }

    .upload-text {
      font-size: 14px;
      color: $text-color;
      margin-bottom: 4px;
    }

    .upload-hint {
      font-size: 12px;
      color: $text-color-secondary;
    }
  }

  .upload-progress {
    margin-top: 16px;

    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
      font-size: 12px;
      color: $text-color-secondary;
    }
  }

  .uploaded-image {
    position: relative;
    display: inline-block;
    margin-top: 16px;

    .image-preview {
      width: 120px;
      height: 120px;
      object-fit: cover;
      border-radius: $border-radius;
      border: 1px solid $border-color;
    }

    .image-actions {
      position: absolute;
      top: 8px;
      right: 8px;
      display: flex;
      gap: 4px;

      .action-button {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;

        &:hover {
          background-color: rgba(0, 0, 0, 0.8);
        }
      }
    }
  }
}

// ==================== 响应式设计 ====================

// 平板设备
@media (max-width: $breakpoint-tablet) {
  .card-detail-layout {
    .layout-content {
      .content-sidebar {
        width: 400px;
      }
    }
  }

  .config-form {
    .form-section {
      margin-bottom: 24px;

      .section-content {
        padding-left: 20px;
      }
    }

    .form-item {
      margin-bottom: 20px;
    }
  }

  .preview-container {
    .preview-content {
      padding: 16px;

      .preview-frame {
        &.device-tablet,
        &.device-desktop {
          width: 100%;
          max-width: 600px;
          height: 400px;
        }
      }
    }
  }
}

// 手机设备
@media (max-width: $breakpoint-mobile) {
  .card-detail-layout {
    .layout-header {
      height: 56px;
    }

    .layout-footer {
      height: 64px;
    }

    .layout-content {
      flex-direction: column;

      .content-sidebar {
        width: 100%;
        height: 50%;
        border-right: none;
        border-bottom: 1px solid $border-color;
      }

      .content-main {
        height: 50%;
      }
    }
  }

  .config-form {
    padding: 16px;

    .form-section {
      margin-bottom: 20px;

      .section-title {
        font-size: 14px;
      }

      .section-content {
        padding-left: 0;
      }
    }

    .form-item {
      margin-bottom: 16px;

      .form-label {
        font-size: 13px;
      }
    }
  }

  .preview-container {
    .preview-header {
      padding: 12px 16px;

      .preview-title {
        font-size: 14px;
      }
    }

    .preview-content {
      padding: 12px;

      .preview-frame {
        width: 100%;
        height: 300px;

        &.device-mobile,
        &.device-tablet,
        &.device-desktop {
          width: 100%;
          height: 300px;
        }
      }
    }
  }

  .image-upload {
    .upload-area {
      padding: 16px;

      .upload-icon {
        font-size: 24px;
      }

      .upload-text {
        font-size: 13px;
      }

      .upload-hint {
        font-size: 11px;
      }
    }

    .uploaded-image {
      .image-preview {
        width: 80px;
        height: 80px;
      }
    }
  }
}

// ==================== 深色主题支持 ====================

@media (prefers-color-scheme: dark) {
  .card-detail-page {
    color: #ffffff;
    background-color: #1a1a1a;
  }

  .card-detail-layout {
    .layout-header,
    .layout-footer {
      background-color: #1f1f1f;
      border-color: #333333;
    }

    .layout-content {
      .content-sidebar {
        background-color: #1f1f1f;
        border-color: #333333;
      }

      .content-main {
        background-color: #1a1a1a;
      }
    }
  }

  .config-form {
    .form-section {
      .section-title {
        color: #ffffff;
      }
    }

    .form-item {
      .form-label {
        color: #ffffff;
      }

      .form-help {
        color: #cccccc;
      }
    }
  }

  .preview-container {
    .preview-header {
      background-color: #1f1f1f;
      border-color: #333333;

      .preview-title {
        color: #ffffff;
      }
    }

    .preview-content {
      .preview-frame {
        background-color: #1f1f1f;
      }
    }
  }

  .status-indicator {
    &.status-success {
      background-color: #162312;
      border-color: #274916;
    }

    &.status-warning {
      background-color: #2b2111;
      border-color: #613400;
    }

    &.status-error {
      background-color: #2a1215;
      border-color: #a8071a;
    }

    &.status-info {
      background-color: #111a2c;
      border-color: #003a8c;
    }
  }

  .empty-state {
    .empty-title {
      color: #ffffff;
    }

    .empty-description {
      color: #cccccc;
    }
  }

  .image-upload {
    .upload-area {
      border-color: #333333;
      background-color: #1f1f1f;

      &:hover,
      &.dragover {
        border-color: #1890ff;
        background-color: rgba(24, 144, 255, 0.1);
      }

      .upload-text {
        color: #ffffff;
      }

      .upload-hint {
        color: #cccccc;
      }
    }

    .uploaded-image {
      .image-preview {
        border-color: #333333;
      }
    }
  }
}

// ==================== 打印样式 ====================

@media print {
  .card-detail-layout {
    .layout-header,
    .layout-footer {
      display: none;
    }

    .layout-content {
      .content-sidebar {
        display: none;
      }

      .content-main {
        width: 100%;
      }
    }
  }

  .preview-container {
    .preview-header {
      display: none;
    }

    .preview-content {
      padding: 0;

      .preview-frame {
        width: 100%;
        height: auto;
        box-shadow: none;
        border: 1px solid #000000;
      }
    }
  }
}

// ==================== 动画效果 ====================

// 淡入动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑动动画
.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

// 缩放动画
.scale-enter-active,
.scale-leave-active {
  transition: transform 0.3s ease;
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.8);
}

// ==================== 工具类 ====================

// 文本工具类
.text-primary { color: $primary-color !important; }
.text-success { color: $success-color !important; }
.text-warning { color: $warning-color !important; }
.text-error { color: $error-color !important; }
.text-secondary { color: $text-color-secondary !important; }
.text-disabled { color: $text-color-disabled !important; }

// 背景工具类
.bg-primary { background-color: $primary-color !important; }
.bg-success { background-color: $success-color !important; }
.bg-warning { background-color: $warning-color !important; }
.bg-error { background-color: $error-color !important; }
.bg-light { background-color: $background-color-light !important; }

// 间距工具类
.m-0 { margin: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mt-2 { margin-top: 16px !important; }
.mt-3 { margin-top: 24px !important; }
.mb-1 { margin-bottom: 8px !important; }
.mb-2 { margin-bottom: 16px !important; }
.mb-3 { margin-bottom: 24px !important; }

.p-0 { padding: 0 !important; }
.pt-1 { padding-top: 8px !important; }
.pt-2 { padding-top: 16px !important; }
.pt-3 { padding-top: 24px !important; }
.pb-1 { padding-bottom: 8px !important; }
.pb-2 { padding-bottom: 16px !important; }
.pb-3 { padding-bottom: 24px !important; }

// 显示工具类
.d-none { display: none !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// 对齐工具类
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }

.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }

// 边框工具类
.border { border: 1px solid $border-color !important; }
.border-top { border-top: 1px solid $border-color !important; }
.border-bottom { border-bottom: 1px solid $border-color !important; }
.border-left { border-left: 1px solid $border-color !important; }
.border-right { border-right: 1px solid $border-color !important; }

.rounded { border-radius: $border-radius !important; }
.rounded-full { border-radius: 50% !important; }

// 阴影工具类
.shadow { box-shadow: $box-shadow !important; }
.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important; }
.shadow-lg { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important; }

// 溢出工具类
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-scroll { overflow: scroll !important; }

// 位置工具类
.relative { position: relative !important; }
.absolute { position: absolute !important; }
.fixed { position: fixed !important; }
.sticky { position: sticky !important; }

// 宽高工具类
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.w-auto { width: auto !important; }
.h-auto { height: auto !important; }

// 弹性布局工具类
.flex { display: flex !important; }
.flex-col { flex-direction: column !important; }
.flex-row { flex-direction: row !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-1 { flex: 1 !important; }
.flex-auto { flex: auto !important; }
.flex-none { flex: none !important; }

// 光标工具类
.cursor-pointer { cursor: pointer !important; }
.cursor-not-allowed { cursor: not-allowed !important; }
.cursor-default { cursor: default !important; }

// 用户选择工具类
.select-none { user-select: none !important; }
.select-text { user-select: text !important; }
.select-all { user-select: all !important; }

// 透明度工具类
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

// Z-index工具类
.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }
