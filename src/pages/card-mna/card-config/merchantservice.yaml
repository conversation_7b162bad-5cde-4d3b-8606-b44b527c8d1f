schema:
  interactions:
    merchantService:
      steps:
      - stage: render
        renderType: div
        from:
        - variables: eventData # 所有事件产生的数据都放在eventData中
          as: eventData # 所有事件产生的数据都放在eventData中
        children:
        - watch:
          - variable: eventData # checkbox的点选后传入的是true | false
            when: 1 # 这里的节点为true的时候才渲染这个children
          renderType: div
          children:
          - interaction: "#/interactions/merchantOnlineService"
        - watch:
          - variable: eventData # checkbox的点选后传入的是true | false
            when: 2 # 这里的节点为true的时候才渲
          renderType: div
          children:
          - interaction: "#/interactions/merchantTelService"
    merchantOnlineService:
      steps:
        - stage: render
          renderType: div
          from:
          - state: "#/definitions/cardStatus/state"
            as: cardStatus
          children:
            - renderType: schema-form
              props:
                title: 客服配置
                layout: vertical
                labelAlign: left
                labelWidth: 200px
                disabled: ${cardStatus.status == 3 || cardStatus.status == 2}
              schema:
                type: object
                formItemProps: 
                  labelWidth: 160px
                properties:
                  customerServiceType:
                    definition: '#/concepts/customerService/customerServiceType'
                    title: 客服类型
                    ui:widget: v-select
                    currentDescription: true
                    ui:options:
                      placeholder: 请选择客服类型
                      style:
                        width: 488px
                  customizeWeb.path:
                    watchState: # watchState用于声明表单项有依赖的时候，决定如何进一步显示
                    - state: "#/definitions/customerService/state" # 监控这个state
                      by: customerServiceType  # 根据by字段
                      when: 3  # 值为self时，条件满足渲染当前表单项，不满足时不渲染同时formData中也清除，以下同
                    from: '#/concepts/customerService/customizeWeb/properties/path'
                    title: 网页客服链接
                    ui:options:
                      placeholder: 请输入自建客服会话链接
                      style:
                        width: 488px
                    description: 发布前请扫码确认能否成功跳转客服会话页面
                  customizeMp.appid:
                    watchState:
                      - state: "#/definitions/customerService/state"
                        by: customerServiceType
                        when: 4
                    from: '#/concepts/customerService/customizeMp/properties/appid'
                    title: 小程序AppID
                    ui:widget: v-select
                    empty: v-miniprogram-select-empty
                    handle:
                      from: userProvide
                      path: utils.renderMiniprogramSelect
                    ui:options:
                      placeholder: 请输入小程序AppID
                      style:
                        width: 488px
                    description: 用户发起客服会话的小程序的AppID
                  customizeMp.path:
                    watchState:
                      - state: "#/definitions/customerService/state"
                        by: customerServiceType
                        when: 4
                    from: '#/concepts/merchantService/customizeMp/properties/path'
                    title: 跳转路径
                    ui:options:
                      placeholder: 请输入小程序路径
                      style:
                        width: 488px
                    description: 小程序内客服会话页面的跳转路径，发布前请扫码确认能否成功跳转客服会话页面
                  wecom.path:
                    watchState:
                      - state: "#/definitions/customerService/state"
                        by: customerServiceType
                        when: 2
                    from: '#/concepts/customerService/wecom/properties/path'
                    title: 企业微信客服账号链接
                    ui:options:
                      placeholder: 请输入企业微信客服账号链接
                      style:
                        width: 488px
                    description: 
                    - "可前往企业微信工作台-微信客服-客服账号-接入链接，查询客服账号链接，发布前请扫码确认能否成功跳转客服会话页。" # markdown格式表示链接 
                  miniProgram.appid:
                    watchState:
                      - state: "#/definitions/customerService/state"
                        by: customerServiceType
                        when: 1
                    from: '#/concepts/customerService/miniProgram/properties/appid'
                    title: 小程序AppID
                    ui:widget: v-select
                    empty: v-miniprogram-select-empty
                    handle:
                      from: userProvide
                      path: utils.renderMiniprogramSelect
                    ui:options:
                      placeholder: 请输入小程序AppID
                      style:
                        width: 488px
                    description: "接入小程序官方客服组件的AppID，发布前请扫码确认能否成功跳转客服会话页。"
              # 关联上定义的states
              formData: '#/definitions/customerService/state'
    merchantTelService:
      steps:
        - stage: render
          from:
          - state: "#/definitions/cardStatus/state"
            as: cardStatus
          renderType: div
          props: {}
          children:
            - renderType: schema-form
              props:
                title: 客服配置
                layout: vertical
                labelAlign: left
                disabled: ${cardStatus.status == 3 || cardStatus.status == 2}
              schema:
                type: object
                formItemProps: 
                  labelWidth: 160px
                properties:
                  servicePhone:
                    from: '#/concepts/customerService/servicePhone'
                    title: 客服电话
                    ui:options:
                      placeholder: 请输入官方客服电话
                      style:
                        width: 488px
              formData: '#/definitions/customerService/state'

