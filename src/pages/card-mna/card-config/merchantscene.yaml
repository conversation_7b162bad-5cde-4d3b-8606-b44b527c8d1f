schema:
  interactions:
    merchantSceneMiniprogram:
      steps:
        - stage: render
          from:
            - variables: eventData # 所有事件产生的数据都放在eventData中
              as: eventData # 所有事件产生的数据都放在eventData中
            - state: "#/definitions/cardStatus/state"
              as: cardStatus
          renderType: div
          props:
            class: flag
          children:
            - watch:
              - variable: eventData # checkbox的点选后传入的是true | false
                when: true # 这里的节点为true的时候才渲染这个children
              renderType: schema-form
              props:
                layout: vertical
                labelAlign: left
                disabled: ${cardStatus.status == 3 || cardStatus.status == 2}
              schema:
                type: object
                labelWidth: 160px
                properties:
                  miniProgram.appid:
                    from: '#/concepts/merchantScene/miniProgram/properties/appid'
                    title: 跳转小程序
                    ui:widget: v-select
                    empty: v-miniprogram-select-empty
                    handle:
                      from: userProvide
                      path: utils.renderMiniprogramSelect
                    ui:options:
                      style:
                        width: 100%
                        height: 40px
                      placeholder: 请选择名片头部需展示的小程序
                    description:
                      - "点击名片头部小程序，跳转小程序首页，如需添加其他小程序，请前往[AppID管理](https://pay.weixin.qq.com/xdc/brandweb/index/appid/appid-manage){color=#4848dd;text-decoration: none}"
                  miniProgram.sceneTag:
                    from: '#/concepts/merchantScene/miniProgram/properties/sceneTag'
                    title: 场景标签
                    titleTag: 
                      renderType: v-tag
                      props:
                        content: 选填
                        class: title-tag
                    ui:options:
                      placeholder:  请输入场景标签
                    description: 场景标签展示在小程序下方，如新品上市、限时优惠。如未填写，将默认展示"前往小程序"
                  miniProgram.imageList:
                    from: '#/concepts/merchantScene/miniProgram/properties/imageList'
                    title: 场景展示图片
                    titleTag: 
                      renderType: v-tag
                      props:
                        content: 选填
                        class: title-tag
                    ui:widget: v-upload
                    props:
                      action: /file/upload-image
                      multiple: true
                      accept: image/png, image/jpeg
                      max: 2
                      theme: image
                      requestMethod: configPage.uploadFile
                      # requestMethodResponse: configPage.uploadFileResponse
                    description:
                      - 在小程序右侧展示，请上传不小于40像素*40像素，不大于256像素 * 256像素的图片
              formData: '#/definitions/cardConfigSence/state'
    merchantSceneVideo:
      steps:
        - stage: render
          from:
            - variables: eventData # 所有事件产生的数据都放在eventData中
              as: eventData # 所有事件产生的数据都放在eventData中
            - state: "#/definitions/cardStatus/state"
              as: cardStatus
          renderType: div
          children:
            - watch:
              - variable: eventData # checkbox的点选后传入的是true | false
                when: true # 这里的节点为true的时候才渲染这个children
              renderType: schema-form
              props:
                title: 客服配置
                layout: vertical
                labelAlign: left
                disabled: ${cardStatus.status == 3 || cardStatus.status == 2}
              schema:
                type: object
                labelWidth: 160px
                properties:
                  finder.finderUsername:
                    from: '#/concepts/merchantScene/finder/properties/finderUsername'
                    title: 跳转视频号
                    ui:widget: v-select
                    handle:
                      from: userProvide
                      path: utils.renderFinderSelect
                    empty: v-finder-select-empty
                    ui:options:
                      placeholder: 请选择名片头部需展示的视频号
                    description:
                      - "名片支持跳转视频号首页，如需添加其他视频号，请前往[视频号管理](https://pay.weixin.qq.com/xdc/brandweb/index/finder/finder-manage){color=#4848dd;text-decoration: none}"
                  finder.sceneTag:
                    from: '#/concepts/merchantScene/finder/properties/sceneTag'
                    title: 场景标签
                    titleTag: 
                      renderType: v-tag
                      props:
                        content: 选填
                        class: title-tag
                    ui:options:
                      placeholder: 请输入场景标签
                    description: 如未填写，将默认展示前往视频号
                  finder.imageList:
                    from: '#/concepts/merchantScene/finder/properties/imageList'
                    title: 场景展示图片
                    titleTag: 
                      renderType: v-tag
                      props:
                        content: 选填
                        class: title-tag
                    ui:widget: v-upload
                    props:
                      action: /file/upload-image
                      theme: image
                      accept: image/png, image/jpeg
                      multiple: true
                      max: 1
                      requestMethod: configPage.uploadFile
                    description:
                      - 在视频号右侧展示，请上传不小于40像素*40像素，不大于256像素 * 256像素的图片
              formData: '#/definitions/cardConfigSence/state'
