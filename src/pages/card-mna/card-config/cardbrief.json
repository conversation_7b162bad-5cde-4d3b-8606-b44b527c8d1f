{"schema": {"interactions": {"cardBrief": {"steps": [{"stage": "render", "renderType": "div", "children": [{"renderType": "schema-form", "props": {"title": "名片介绍", "labelAlign": "top", "labelWidth": 0, "style": {"width": "630px"}}, "schema": {"type": "object", "formItemProps": {"labelWidth": 0}, "properties": {"cardBrief": {"from": "#/concepts/cardBrief", "ui:widget": "v-textarea", "ui:options": {"placeholder": "输入名片介绍"}}}}, "formData": "#/definitions/cardBrief/state"}]}]}}}}