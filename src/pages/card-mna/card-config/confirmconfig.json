{"schema": {"interactions": {"confirmConfig": {"from": [{"state": "#/definitions/cardStatus/state", "as": "cardStatus"}], "steps": [{"stage": "prepare", "from": [{"#/definitions/LatestCardInfoPublish/state": ["processId"]}, {"#/definitions/LatestCardInfoPublish/state": {"as": "latestCardInfoPublish"}}, {"#/definitions/formCheckboxStatus/state": {"as": "formCheckboxStatus"}}, {"#/definitions/merchantServiceCategory/state": {"as": "merchantServiceCategory"}}, {"#/definitions/customerService/state": {"as": "customerService"}}, {"#/definitions/cardConfigSence/state": {"as": "cardConfigSence"}}, {"#/definitions/cardBrief/state": {"as": "cardBrief"}}], "handler": {"from": "userProvide", "path": "utils.prepareSaveCardInfo"}}, {"stage": "process", "apis": [[{"#/apis/saveCardConfig": ["processId", "content"]}], [{"#/apis/submitCardConfig": ["processId"], "isSkip": "${cardStatus.isfirst === false}"}]], "loading": "#/global/loading"}, {"stage": "error", "handlers": [{"case": "code", "not": 0, "interaction": "#/interactions/confirmCardInfoError", "slot": "messageContainer"}]}, {"stage": "handle", "handler": {"before": {"from": "userProvide", "path": "utils.handleConfirm"}}, "states": [{"#/definitions/cardStatus/state": {"jump": "$StepData$.jump"}}]}, {"stage": "render", "from": [{"state": "#/definitions/cardStatus/state", "as": "cardStatus"}], "jump": "${cardStatus.jump}"}]}, "confirmCardInfoError": {"steps": [{"stage": "render", "renderType": "v-message", "from": [{"variables": "message", "as": "errMessage"}], "props": {"theme": "error", "duration": 5000, "content": "商家资料添加失败，${errMessage}"}}]}}}}