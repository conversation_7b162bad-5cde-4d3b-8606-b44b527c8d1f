schema:
  interactions:
    doCancelConfig:
      steps: 
      - stage: prepare
        from: 
        - "#/definitions/LatestCardInfoPublish/state":
            - processId
      - stage: process
        api: "#/apis/cancelCardConfig"
        loading: "#/global/loading"
      - stage: error
        handlers:
          - case: code
            not: 0
            interaction: "#/interactions/cancelCardInfoError"
            slot: messageContainer
      - stage: render
        renderType: div
        children:
        - renderType: v-message
          props:
            theme: success
            duration: 5000
            content: 撤销成功
        # 创建一个新单
    cancelCardInfoError:
      steps:
        - stage: render
          renderType: v-message
          from: 
          - variables: message
            as: errMessage
          props:
            theme: error
            duration: 5000
            content: 撤销失败，${errMessage}
