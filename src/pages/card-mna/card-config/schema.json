{"schema": {"globalStyle": {"formtitle": {"background-color": "#fff", "padding": "16px"}, "section": {"margin-bottom": "24px", "background-color": "#fff", "border-radius": "8px", "padding": "24px"}, "section-title": {"font-size": "16px", "font-weight": "bold", "margin-bottom": "16px"}}, "cssClasses": {"appid-text": {"font-size": "14px", "color": "#838383", "margin-top": "2px"}, "form-item-row": {"display": "flex", "align-items": "flex-start", "margin": "24px 0", "font-size": "16px"}, "title-tag": {"border-radius": "4px", "background": "rgba(0, 0, 0, 0.04)", "color": "rgba(0, 0, 0, 0.3)", "margin": "0 4px;"}}, "layout": "v-simplelayout", "onMainFinished": "events.onCardConfigMainFinished", "meta": {"icon": "card", "title": "微信支付 品牌经营"}, "main": {"interactions": [{"path": "#/interactions/renderCardConfigForm", "slot": "top1"}]}, "apis": {"getCardConfig": {"url": "/get-card-info", "method": "GET"}, "getBrandInfo": {"url": "/get-brand-info", "method": "GET"}, "dateCardConfig": {"url": "/save-card-info-publish-process", "method": "PUT"}, "getMiniprogramList": {"url": "/query-brand-miniprogram-list", "method": "GET"}, "getFinderList": {"url": "/query-brand-finder-list", "method": "GET"}, "saveCardConfig": {"url": "/save-card-info-publish-process", "method": "POST"}, "GetLatestCardInfoPublishProcess": {"url": "/get-latest-card-info-publish-process", "method": "GET"}, "createCardInfoPublishProcess": {"url": "/create-card-info-publish-process", "method": "POST"}, "submitCardConfig": {"url": "/submit-card-info-publish-process", "method": "POST"}, "cancelCardConfig": {"url": "/cancel-card-info-publish-process", "method": "POST"}}, "definitions": {"merchantServiceCategory": {"state": {"category": 1}}, "customerService": {"from": "#/concepts/customerService"}, "cardConfigSence": {"from": "#/concepts/merchantScene"}, "cardBrief": {"state": {"cardBrief": ""}}, "brandInfo": {"state": {"brandLogo": "", "brandAlias": "", "brandShowType": "", "brandId": "", "brandName": ""}}, "cardStatus": {"state": {"disabled": false}}, "errors": {"state": {}}, "LatestCardInfoPublish": {"state": {"processId": ""}}, "userAction": {"state": {"showCancelConfirm": false, "saveButtonDisbaled": true, "showNotFirstCancelConfirm": false}}, "processOrigin": {"state": {}}, "cardInfo": {"state": {}}}, "interactions": {"renderCardConfigForm": {"steps": [{"stage": "process", "handler": {"from": "userProvide", "path": "configPage.initPublishProcess"}}, {"stage": "handle", "states": [{"#/definitions/LatestCardInfoPublish/state": {"processId": "$StepData$.processId"}}, {"#/definitions/customerService/state": {"customerServiceType": "$StepData$.customerService.customerServiceType", "customizeWeb": "$StepData$.customerService.customizeWeb", "miniProgram": "$StepData$.customerService.miniProgram", "customizeMp": "$StepData$.customerService.customizeMp", "wecom": "$StepData$.customerService.wecom", "servicePhone": "$StepData$.customerService.servicePhone"}}, {"#/definitions/cardConfigSence/state": {"miniProgram": "$StepData$.sceneConfig.miniProgram", "finder": "$StepData$.sceneConfig.finder"}}, {"#/definitions/formCheckboxStatus/state": {"sceneMiniProgramChecked": "$StepData$.sceneConfig.sceneMiniProgramChecked", "sceneFinderChecked": "$StepData$.sceneConfig.sceneFinderChecked"}}, {"#/definitions/merchantServiceCategory/state": {"category": "$StepData$.customerService.category"}}, {"#/definitions/cardStatus/state": {"status": "$StepData$.status", "isfirst": "$StepData$.isfirst", "publishType": "$StepData$.publishType", "scheduleTimeWithFormat": "$StepData$.scheduleTimeWithFormat"}}, {"#/definitions/processOrigin/state": {"originData": "$StepData$.processOrigin"}}, {"#/definitions/cardInfo/state": {"cardInfo": "$StepData$.cardInfo"}}]}, {"stage": "render", "from": [{"state": "#/definitions/LatestCardInfoPublish/state", "as": "cardInfoPublish"}, {"state": "#/definitions/brandInfo/state", "as": "brandInfo"}, {"state": "#/definitions/customerService/state", "as": "customerService"}, {"state": "#/definitions/cardConfigSence/state", "as": "cardConfigSence"}, {"state": "#/definitions/formCheckboxStatus/state", "as": "formCheckboxStatus"}, {"state": "#/definitions/cardStatus/state", "as": "cardStatus"}, {"state": "#/definitions/userAction/state", "as": "userAction"}, {"state": "#/definitions/processOrigin/state", "as": "processOrigin"}, {"state": "#/definitions/merchantServiceCategory/state", "as": "merchantServiceCategory"}, {"state": "#/definitions/cardStatus/state", "as": "cardStatus"}, {"state": "#/definitions/cardInfo/state", "as": "cardInfo"}], "renderType": "div", "props": {"class": "home-container"}, "children": [{"renderType": "div", "props": {"class": "form-container", "style": {"padding": "32px", "border-radius": "16px", "background-color": null}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justify-content": "space-between"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "flex": 1, "min-width": 0, "flex-direction": "column"}}, "children": [{"renderType": "h3", "props": {"style": {"font-size": "20px", "font-weight": "500"}}, "children": ["基础信息"]}, {"renderType": "div", "props": {"style": {"margin": "24px 0", "width": "860px"}}, "children": [{"renderType": "v-service-status-alert", "props": {"type": "config", "status": "${processOrigin}", "processId": "${cardInfoPublish.processId}", "show": true, "style": {"margin": "24px 0"}}}]}, {"renderType": "div", "children": [{"renderType": "div", "props": {"class": "form-item-row"}, "children": [{"renderType": "div", "props": {"style": {"flex": "0 0 160px", "margin-right": "16px"}}, "children": [{"renderType": "div", "props": {"class": null, "style": {"font-size": "16px", "margin": 0}}, "children": ["品牌信息"]}]}, {"renderType": "div", "props": {"style": {"width": "calc(712px + 272px + 24px)", "align-items": "flex-start"}}, "children": [{"interaction": "#/interactions/brandinfo"}, {"renderType": "div", "props": {"style": {"color": "rgba(0,0,0,.3)", "font-size": "16px", "margin": "8px 0"}}, "children": ["如需修改请前往", {"renderType": "a", "props": {"href": "/xdc/brandweb/index/info/brand-info", "target": "_blank", "style": {"color": "#4848DD"}}, "children": ["品牌信息"]}]}]}]}, {"renderType": "div", "props": {"class": "form-item-row"}, "children": [{"renderType": "div", "props": {"style": {"flex": "0 0 160px", "margin-right": "16px"}}, "children": [{"renderType": "div", "props": {"class": null, "style": {"font-size": "16px", "margin": 0}}, "children": ["商家客服"]}]}, {"renderType": "div", "props": {"style": {"display": "flex", "gap": "24px", "width": "calc(712px)", "align-items": "flex-start"}}, "children": [{"renderType": "div", "props": {"style": {"width": "712px", "display": "flex", "flex-direction": "column", "gap": "32px"}}, "children": [{"renderType": "v-radio-group", "props": {"value": "number:${merchantServiceCategory.category}", "disabled": "${cardStatus.status == 3 || cardStatus.status == 2}", "options": [{"label": "在线客服", "value": 1}, {"label": "客服电话", "value": 2}], "events": {"change": [{"states": {"#/definitions/merchantServiceCategory/state": {"category": "number:$EventData$"}}}, {"interaction": "#/interactions/merchantService", "slot": "serviceform"}]}, "immediate": [{"change": ["number:${merchantServiceCategory.category}"]}]}}, {"renderType": "div", "props": {"class": "auto_height", "style": {"flex": "0 0 auto", "min-width": 0, "background": "rgba(0,0,0, 0.02)", "padding": "24px 24px 0 24px", "border-radius": "16px"}}, "slot": "serviceform"}]}]}]}, {"renderType": "div", "props": {"class": "form-item-row", "style": {"margin": 0}}, "children": [{"renderType": "div", "props": {"style": {"flex": "0 0 160px", "margin-right": "16px"}}, "children": [{"renderType": "div", "props": {"class": null, "style": {"font-size": "16px", "margin": 0}}, "children": [{"renderType": "div", "children": ["场景跳转", {"renderType": "v-tag", "props": {"content": "选填", "class": "title-tag"}}]}]}]}, {"renderType": "div", "props": {"style": {"display": "flex", "gap": "24px", "width": "calc(712px + 272px + 24px)", "align-items": "flex-start"}}, "children": [{"renderType": "div", "props": {"style": {"width": "712px", "display": "flex", "flex-direction": "column", "gap": "32px"}}, "children": [{"renderType": "v-checkbox", "props": {"label": "小程序", "disabled": "${cardStatus.status == 3 || cardStatus.status == 2}", "checked": "boolean:${formCheckboxStatus.sceneMiniProgramChecked}", "events": {"change": [{"states": {"#/definitions/formCheckboxStatus/state": {"sceneMiniProgramChecked": "boolean:$EventData$"}}}, {"interaction": "#/interactions/merchantSceneMiniprogram", "slot": "miniprogramform"}]}, "immediate": [{"change": ["boolean:${formCheckboxStatus.sceneMiniProgramChecked}"]}]}}, {"renderType": "div", "props": {"class": "auto_height", "style": {"flex": "0 0 auto", "min-width": 0, "background": "rgba(0,0,0, 0.02)", "padding": "24px 24px 0 24px", "border-radius": "16px"}}, "slot": "miniprogramform"}, {"renderType": "v-checkbox", "props": {"label": "视频号", "disabled": "${cardStatus.status == 3 || cardStatus.status == 2}", "checked": "boolean:${formCheckboxStatus.sceneFinderChecked}", "events": {"change": [{"states": {"#/definitions/formCheckboxStatus/state": {"sceneFinderChecked": "boolean:$EventData$"}}}, {"interaction": "#/interactions/merchantSceneVideo", "slot": "videoform"}]}, "immediate": [{"change": ["boolean:${formCheckboxStatus.sceneFinderChecked}"]}]}}, {"renderType": "div", "props": {"class": "auto_height", "style": {"flex": "0 0 auto", "min-width": 0, "background": "rgba(0,0,0, 0.02)", "padding": "24px 24px 0 24px", "border-radius": "16px"}}, "slot": "videoform"}]}]}]}, {"renderType": "div", "props": {"class": "form-item-row", "style": {"display": "none"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "0 0 160px", "margin-right": "16px"}}, "children": [{"renderType": "div", "props": {"class": null, "style": {"font-size": "16px", "margin": 0}}, "children": ["名片简介"]}]}, {"renderType": "div", "props": {"style": {"display": "flex", "gap": "24px", "width": "calc(712px + 272px + 24px)", "align-items": "flex-start"}}, "children": [{"renderType": "div", "props": {"style": {"width": "712px", "display": "flex", "flex-direction": "column", "gap": "32px"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "gap": "24px", "width": "calc(712px + 272px + 24px)", "align-items": "flex-start"}}, "children": [{"interaction": "#/interactions/cardBrief"}]}]}]}]}]}]}, {"renderType": "div", "props": {"style": {"flex": "0 0 240px", "background": "rgba(0,0,0, 0.02)", "height": "520px", "width": "240px", "border-radius": "16px"}}, "children": [{"renderType": "div", "props": {"style": {"margin": 0, "font-size": "16px", "color": "rgba(0,0,0,0.6)"}}, "children": [{"renderType": "v-businesscard", "reactive": {"#/definitions/customerService/state": {"keys": ["servicePhone", "miniProgramAppID"]}, "#/definitions/cardConfigSence/state": {"keys": ["miniProgram"]}}, "props": {"data": {"servicePhone": "${servicePhone}", "miniProgramAppID": "${miniProgramAppID}", "miniProgram": "${miniProgram}", "showQRCode": "pop", "cardInfo": "${cardInfo}", "page": "card-config"}}}]}]}]}]}, {"renderType": "div", "props": {"class": "action-buttons", "style": {"marginTop": "8px", "padding": "16px 32px"}}, "condition": {"literal": "${cardStatus.status !== 8}"}, "children": [{"renderType": "div", "slot": "confirm<PERSON><PERSON><PERSON>"}, {"renderType": "v-button", "condition": {"literal": "${cardStatus.status == 0 || ${cardStatus.status == 1 || cardStatus.status == 4}"}, "reactive": {"#/definitions/userAction/state": {"keys": ["save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "props": {"theme": "primary", "content": "确认配置", "disabled": "${userAction.saveButtonDisbaled}", "events": {"click": [{"interaction": "#/interactions/confirmConfig", "slot": "dialogContainer"}]}}}, {"renderType": "v-button", "condition": {"literal": "${cardStatus.status == 0 || ${cardStatus.status == 1 || ${cardStatus.status == 4}"}, "reactive": {"#/definitions/userAction/state": {"keys": ["save<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "props": {"theme": "default", "size": "large", "content": "保存", "disabled": "${userAction.saveButtonDisbaled}", "events": {"click": [{"validate": "#/definitions/customerService/state"}, {"validate": "#/definitions/cardConfigSence/state"}, {"handle": {"from": "userProvide", "path": "configPage.execValidate"}}, {"interaction": "#/interactions/saveMerchantConfig", "slot": "messageContainer"}]}}}, {"renderType": "v-popconfirm", "reactive": {"#/definitions/userAction/state": {"keys": ["showNotFirstCancelConfirm"]}}, "props": {"theme": "danger", "content": "撤销配置后，本次修改内容将不再保存", "visible": "${userAction.showNotFirstCancelConfirm}", "overlayClassName": "custom-popconfirm-container", "events": {"confirm": [{"interaction": "#/interactions/doCancelConfig", "slot": "messageContainer"}, {"state": {"#/definitions/userAction/state": {"showNotFirstCancelConfirm": false}}}, {"action": "refresh"}], "cancel": [{"state": {"#/definitions/userAction/state": {"showNotFirstCancelConfirm": false}}}]}}}, {"renderType": "v-button", "condition": {"literal": "${ cardStatus.status == 1 || cardStatus.status == 2 || cardStatus.status == 3 || cardStatus.status == 4 || cardStatus.status == 6}"}, "props": {"theme": "default", "content": "撤销配置", "events": {"click": [{"state": {"#/definitions/userAction/state": {"showNotFirstCancelConfirm": true}}}]}}}, {"renderType": "v-button", "props": {"theme": "default", "content": "返回", "events": {"click": [{"jump": "${cardStatus.isfirst === true ? '/home/<USER>/card-detail': '/home/<USER>/card-view'}"}]}}}]}]}]}}}}