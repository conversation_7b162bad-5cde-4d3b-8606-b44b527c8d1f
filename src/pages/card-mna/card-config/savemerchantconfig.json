{"schema": {"interactions": {"saveMerchantConfig": {"steps": [{"stage": "prepare", "from": [{"#/definitions/LatestCardInfoPublish/state": {"as": "latestCardInfoPublish"}}, {"#/definitions/formCheckboxStatus/state": {"as": "formCheckboxStatus"}}, {"#/definitions/merchantServiceCategory/state": {"as": "merchantServiceCategory"}}, {"#/definitions/customerService/state": {"as": "customerService"}}, {"#/definitions/cardConfigSence/state": {"as": "cardConfigSence"}}, {"#/definitions/cardBrief/state": {"as": "cardBrief"}}], "handler": {"from": "userProvide", "path": "utils.prepareSaveCardInfo"}}, {"stage": "process", "api": "#/apis/saveCardConfig", "loading": "#/global/loading"}, {"stage": "error", "handlers": [{"case": "code", "when": 268574582, "message": "参数错误，请检查表单内容", "interaction": "#/interactions/saveCardInfoError", "slot": "messageContainer"}, {"case": "code", "when": 268574717, "message": "选择小程序AppID", "interaction": "#/interactions/saveCardInfoError", "slot": "messageContainer"}, {"case": "code", "when": 268574718, "message": "请填写在线客服信息", "interaction": "#/interactions/saveCardInfoError", "slot": "messageContainer"}, {"case": "code", "when": 268574719, "message": "请填写客服电话", "interaction": "#/interactions/saveCardInfoError", "slot": "messageContainer"}, {"case": "code", "when": 268574723, "message": "请填写场景标签", "interaction": "#/interactions/saveCardInfoError", "slot": "messageContainer"}, {"case": "code", "not": 0, "interaction": "#/interactions/saveCardInfoError", "slot": "messageContainer"}]}, {"stage": "render", "renderType": "div", "children": [{"renderType": "v-message", "props": {"theme": "success", "duration": 5000, "content": "保存成功"}}]}]}, "saveCardInfoError": {"steps": [{"stage": "render", "renderType": "v-message", "from": [{"variables": "message", "as": "errMessage"}], "props": {"theme": "error", "duration": 5000, "content": "商家资料添加失败，请稍后再试"}}]}}}}