{"schema": {"interactions": {"brandinfo": {"steps": [{"stage": "process", "api": "#/apis/getBrandInfo", "loading": "#/global/loading"}, {"stage": "handle", "states": [{"#/definitions/brandInfo/state": "$StepData$.data"}]}, {"stage": "render", "renderType": "div", "from": [{"state": "#/definitions/brandInfo/state", "as": "brandInfo"}], "children": [{"renderType": "div", "props": {"style": {"width": "712px", "display": "flex", "gap": "8px"}}, "children": [{"renderType": "div", "children": [{"renderType": "img", "props": {"class": "brand-logo", "src": "${brandInfo.brandLogo}"}}]}, {"renderType": "div", "children": ["${brandInfo.brandName}"]}]}]}]}}}}