<template>
  <!-- 主要内容区域 - 添加测试所需的类名 -->
  <div class="card-home card-home-page">
    <!-- 使用面包屑导航替代H1标题 -->
    <div class="breadcrumb-container">
      <component :is="breadcrumb" v-if="breadcrumb"></component>
    </div>
    <!-- 主要内容区域 - 对应DSL中的BusinessCardSection -->
    <div class="cardSection">
      <!-- 标题和按钮区域 -->
      <div class="header-section">
        <div class="title-area">
          <h3 class="section-title">商家名片</h3>
          <p class="section-description">
            助力商家服务与经营，帮助商家实现和用户的深度连接
          </p>
        </div>
        <div class="action-area">
          <t-button
            theme="primary"
            :disabled="!canStartConfig || undefined"
            :loading="loading"
            data-testid="start-config-button"
            @click="handleStartConfig"
          >
            开始配置
          </t-button>
        </div>
      </div>

      <!-- 功能卡片容器 -->
      <div class="card-container">
        <!-- 交易连接名片 -->
        <div class="feature-card">
          <div class="card-images">
            <div class="image-item">
              <img class="card-image" :src="imageAssets.img1" alt="交易连接名片" />
            </div>
            <div class="image-item">
              <img class="card-image" :src="imageAssets.img2" alt="交易连接名片" />
            </div>
          </div>
          <div class="card-content">
            <p class="card-title">交易连接名片</p>
            <p class="card-description">
              添加交易场景，用户支付成功后，点击微信支付分公众号用户下发的交付凭证，将跳转商家名片
            </p>
          </div>
        </div>

        <!-- 名片连接商家的服务与优惠 -->
        <div class="feature-card">
          <div class="card-images">
            <div class="image-item">
              <img class="card-image" :src="imageAssets.img3" alt="商品服务1" />
            </div>
            <div class="image-item">
              <img class="card-image" :src="imageAssets.img4" alt="商品服务2" />
            </div>
          </div>
          <div class="card-content">
            <p class="card-title">名片连接商家的服务与优惠</p>
            <p class="card-description">
              名片连接商家的小程序、视频号和客服，提供自定义服务配置，展示用户名商家的优惠券和会员权益
            </p>
          </div>
        </div>

        <!-- 用户标记喜欢，稳定找回商家 -->
        <div class="feature-card">
          <div class="card-images">
            <div class="image-item">
              <img class="card-image" :src="imageAssets.img5" alt="宣传推广1" />
            </div>
            <div class="image-item">
              <img class="card-image" :src="imageAssets.img6" alt="宣传推广2" />
            </div>
          </div>
          <div class="card-content">
            <p class="card-title">用户标记喜欢，稳定找回商家</p>
            <p class="card-description">
              用户标记了喜欢商家，商家名片将出现在「微信支付公众号-我的商家-喜欢的商家」列表，便于用户快速找回喜欢商家
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="isInitializing" class="loading-overlay">
      <t-loading size="large" text="正在加载..." />
    </div>

    <!-- 错误提示 -->
    <t-message v-if="error" theme="error" :content="error" :duration="5000" />
  </div>
</template>

<script setup lang="ts">
import { useCardHome } from './composables/useCardHome';
import { useBreadcrumb } from '@/router/useBreadcrumb';

const { dynamicBreadcrumb } = useBreadcrumb();
const breadcrumb = dynamicBreadcrumb;

// 使用组合式函数
const { isInitializing, loading, error, canStartConfig, handleStartConfig } =
  useCardHome();

// 图片资源映射 - 对应DSL中的 ${asserts.img1-6}
const imageAssets = {
  img1: '/src/assets/images/cardmna/01.png', // 交易连接名片1
  img2: '/src/assets/images/cardmna/02.png', // 交易连接名片2
  img3: '/src/assets/images/cardmna/03.png', // 商品服务1
  img4: '/src/assets/images/cardmna/04.png', // 商品服务2
  img5: '/src/assets/images/cardmna/05.png', // 宣传推广1
  img6: '/src/assets/images/cardmna/06.png', // 宣传推广2
};
</script>

<style scoped lang="scss">
// 页面根容器
.card-home {
  padding: 24px;
  // background-color: #f5f5f5;
  min-height: 100vh;
}

// 面包屑容器
.breadcrumb-container {
  margin-bottom: 16px;

  :deep(.breadcrumb) {
    padding: 0;
    margin-bottom: 16px;
  }
}

// 页面头部
.page-header {
  margin-bottom: 24px;
  text-align: center;

  h1 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #000;
  }

  .page-description {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

// 复用DSL中定义的全局样式
.cardSection {
  margin-top: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
}

// 标题和按钮区域 - 对应DSL中的header部分
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .title-area {
    .section-title {
      font-size: 20px;
      font-weight: 500;
      margin: 0 0 8px 0;
      color: #000;
    }

    .section-description {
      color: #666;
      font-size: 16px;
      opacity: 0.3;
      margin: 0;
    }
  }

  .action-area {
    // 按钮样式由TDesign提供
  }
}

// 卡片容器 - 对应DSL中的card-container
.card-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  max-width: 1200px;
  margin: 0 auto;
}

// 功能卡片 - 对应DSL中的feature-card
.feature-card {
  width: 32%;
  border: 1px solid #e7e7e7;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  // 图片区域 - 对应DSL中的两张并排图片
  .card-images {
    display: flex;
    flex-wrap: nowrap;
    gap: 16px;
    width: 100%;
    align-items: stretch;

    .image-item {
      width: calc(50% - 8px);
      overflow: hidden;

      .card-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 4px;
        background: #f5f5f5;
      }
    }
  }

  // 卡片内容区域
  .card-content {
    margin-top: 16px;
    text-align: left;
    width: 100%;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      margin: 0 0 14px 0;
      color: #000;
    }

    .card-description {
      margin: 0;
      font-weight: 400;
      text-align: left;
      width: 100%;
      color: rgba(0, 0, 0, 0.5);
      line-height: 1.5;
      font-size: 14px;
    }
  }
}

// 加载状态覆盖层
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// 响应式布局
@media (max-width: 1200px) {
  .card-container {
    justify-content: flex-start;
  }

  .feature-card {
    width: calc(50% - 8px);
  }
}

@media (max-width: 768px) {
  .header-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .card-container {
    flex-direction: column;
  }

  .feature-card {
    width: 100%;
  }
}
</style>
