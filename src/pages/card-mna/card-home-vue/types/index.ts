/**
 * Card-Home页面类型定义
 * 基于业务规则文档中的状态结构
 */

// 卡片状态接口
export interface CardState {
  useable: boolean; // 是否可用，控制"开始配置"按钮的启用状态
}

// 教程状态接口
export interface TutorialState {
  processId: string; // 新手引导流程ID
}

// 卡片信息接口
export interface CardInfo {
  isConfigured: boolean; // 是否已配置
  cardType: string; // 卡片类型
  lastUpdated: string; // 最后更新时间
}

// API响应类型
export interface CardInfoResponse {
  isConfigured?: boolean;
  cardType?: string;
  lastUpdated?: string;
}

export interface TutorialProcessResponse {
  processId: string;
}

// 功能卡片类型
export interface FeatureCard {
  id: string;
  title: string;
  description: string;
  icon: string;
  route: string;
  image?: string;
}

// 路由检查结果类型
export interface RouterCheckResult {
  code: number;
  message: string;
  path: string;
}

// 进程状态枚举
export enum ProcessState {
  INIT = 0, // 初始状态
  DRAFT = 1, // 草稿
  AUDITING = 2, // 待审核
  AUDIT_PASS = 3, // 审核通过
  REJECTED = 4, // 审核驳回
  CANCELED = 5, // 已作废
  APPROVED = 6, // 待发布
  PUBLISHING = 7, // 发布中
  PUBLISHED = 8, // 已完成
}

// 用户状态分类
export enum UserStatus {
  NEW_USER = 1000, // 新用户 - 显示card-home介绍页面
  CONFIGURING = 2000, // 配置中用户 - 重定向到card-detail继续配置
  COMPLETED = 3000, // 已完成用户 - 重定向到dashboard仪表盘
}
