/**
 * CardHome 页面组合式函数
 * 封装页面主要业务逻辑
 */

import { computed } from 'vue';
import { useRouter } from 'vue-router';
import { useCardHomeStore } from '@/stores/cardHome';

export function useCardHome() {
  const store = useCardHomeStore();
  const router = useRouter();

  // 计算属性 - 使用正确的store属性名
  const isInitializing = computed(() => store.loading);
  const loading = computed(() => store.loading);
  const error = computed(() => store.error);
  const hasError = computed(() => !!store.error);
  const errorMessage = computed(() => store.error);
  const cardState = computed(() => store.cardState);
  const tutorialState = computed(() => store.tutorialState);
  const canStartConfig = computed(() => !store.loading && !store.error);

  // 方法 - 使用正确的store方法名
  const initializePage = async () => {
    try {
      await store.initializePageData();
    } catch (error) {
      console.error('初始化页面数据失败:', error);
    }
  };

  const checkCardStatus = async () => {
    await store.checkCardStatus();
  };

  const createTutorialProcess = async () => {
    await store.createTutorialPublishProcess();
  };

  const handleStartConfig = async () => {
    try {
      const success = await store.createTutorialPublishProcess();
      if (success) {
        // 跳转到详情页
        router.push('/home/<USER>/card-detail');
      }
    } catch (error) {
      console.error('开始配置失败:', error);
      // 错误时不跳转路由
    }
  };

  const handleFeatureClick = (path: string) => {
    if (path) {
      router.push(path);
    }
  };

  const resetState = () => {
    store.resetState();
  };

  return {
    // 状态
    isInitializing,
    loading,
    error,
    hasError,
    errorMessage,
    cardState,
    tutorialState,
    canStartConfig,

    // 方法
    initializePage,
    checkCardStatus,
    createTutorialProcess,
    handleStartConfig,
    handleFeatureClick,
    resetState,

    // 实例
    store,
    router,
  };
}
