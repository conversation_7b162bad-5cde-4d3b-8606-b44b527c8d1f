/**
 * 路由检查逻辑
 * 基于业务规则文档中的路由守卫逻辑
 */

import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { cardHomeApi } from '@/services/cardHome';
import type { RouterCheckResult } from '../types';
import { UserStatus } from '../types';

export function useRouterCheck() {
  const router = useRouter();

  // 状态
  const isChecking = ref(false);
  const checkResult = ref<RouterCheckResult | null>(null);

  /**
   * 执行路由检查
   * 对应业务规则中的智能路由重定向规则
   */
  const performRouterCheck = async (): Promise<RouterCheckResult> => {
    try {
      isChecking.value = true;

      const result = await cardHomeApi.checkRouterStatus();
      checkResult.value = result;

      return result;
    } catch (error) {
      console.error('路由检查失败:', error);

      // 默认返回新用户状态
      const defaultResult: RouterCheckResult = {
        code: UserStatus.NEW_USER,
        message: '路由检查失败，默认显示首页',
        path: '/home/<USER>/card-home',
      };

      checkResult.value = defaultResult;
      return defaultResult;
    } finally {
      isChecking.value = false;
    }
  };

  /**
   * 根据检查结果执行重定向
   */
  const executeRedirect = async (result: RouterCheckResult): Promise<void> => {
    try {
      // 如果当前路径与目标路径不同，则执行重定向
      if (router.currentRoute.value.path !== result.path) {
        await router.push(result.path);
      }
    } catch (error) {
      console.error('执行重定向失败:', error);
    }
  };

  /**
   * 检查并重定向
   * 组合检查和重定向逻辑
   */
  const checkAndRedirect = async (): Promise<void> => {
    const result = await performRouterCheck();

    // 根据业务规则，只有非新用户状态才需要重定向
    if (result.code !== UserStatus.NEW_USER) {
      await executeRedirect(result);
    }
  };

  /**
   * 获取用户状态描述
   */
  const getUserStatusDescription = (code: number): string => {
    switch (code) {
      case UserStatus.NEW_USER:
        return '新用户 - 显示功能介绍';
      case UserStatus.CONFIGURING:
        return '配置中 - 继续完成配置';
      case UserStatus.COMPLETED:
        return '已完成 - 查看数据仪表盘';
      default:
        return '未知状态';
    }
  };

  return {
    // 状态
    isChecking,
    checkResult,

    // 方法
    performRouterCheck,
    executeRedirect,
    checkAndRedirect,
    getUserStatusDescription,
  };
}
