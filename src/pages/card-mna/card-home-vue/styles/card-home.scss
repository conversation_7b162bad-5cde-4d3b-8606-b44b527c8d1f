/**
 * Card-Home页面样式
 * 基于业务规则文档中的页面布局规则
 */

// 全局样式变量
:root {
  --card-home-primary-color: #0052d9;
  --card-home-text-primary: #000000;
  --card-home-text-secondary: #333333;
  --card-home-text-tertiary: #666666;
  --card-home-bg-primary: #ffffff;
  --card-home-bg-secondary: #f5f5f5;
  --card-home-border-color: #e7e7e7;
  --card-home-border-radius: 8px;
  --card-home-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --card-home-transition: all 0.3s ease;
}

// 基础布局样式 - 基于业务规则中的样式类定义
.formtitle {
  background: var(--card-home-bg-primary);
  padding: 16px;
  border-radius: var(--card-home-border-radius);
  margin-bottom: 16px;
  box-shadow: var(--card-home-shadow);
}

.cardSection {
  background: var(--card-home-bg-primary);
  border-radius: var(--card-home-border-radius);
  padding: 24px;
  margin-bottom: 16px;
  box-shadow: var(--card-home-shadow);
}

.card-container {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
}

// 功能卡片样式 - 基于业务规则中的feature-card样式
.feature-card {
  width: 32%;
  min-height: 200px;
  background: var(--card-home-bg-primary);
  border: 1px solid var(--card-home-border-color);
  border-radius: var(--card-home-border-radius);
  padding: 24px;
  cursor: pointer;
  transition: var(--card-home-transition);
  
  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: var(--card-home-primary-color);
  }
}

// 响应式布局 - 基于业务规则中的响应式布局
@media (max-width: 1200px) {
  .feature-card {
    width: 48%;
    margin-bottom: 16px;
  }
  
  .card-container {
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .feature-card {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .card-container {
    flex-direction: column;
  }
  
  .cardSection {
    padding: 16px;
  }
}

// 动画效果 - 基于业务规则中的过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translateY(20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

// 工具类
.text-primary {
  color: var(--card-home-text-primary);
}

.text-secondary {
  color: var(--card-home-text-secondary);
}

.text-tertiary {
  color: var(--card-home-text-tertiary);
}

.bg-primary {
  background-color: var(--card-home-bg-primary);
}

.bg-secondary {
  background-color: var(--card-home-bg-secondary);
}

.border-radius {
  border-radius: var(--card-home-border-radius);
}

.shadow {
  box-shadow: var(--card-home-shadow);
}

.transition {
  transition: var(--card-home-transition);
}