/**
 * CardHome 组件测试
 * 基于acceptance.md中的验收标准
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import { setActivePinia, createPinia } from 'pinia';

import CardHome from '../CardHome.vue';
import { useCardHomeStore } from '@/stores/cardHome';

// Mock API服务 - 必须在导入组件之前
vi.mock('@/services/cardHome', () => ({
  cardHomeApi: {
    createTutorialPublishProcess: vi.fn(),
    getCardInfo: vi.fn(),
    checkRouterStatus: vi.fn().mockResolvedValue({
      code: 1000, // NEW_USER
      message: '新用户',
      path: '/home/<USER>/card-home',
    }),
  },
}));

// Mock Vue Router
const mockPush = vi.fn();
const mockRoute = {
  path: '/home/<USER>/card-home',
  params: {},
  query: {},
  hash: '',
  fullPath: '/home/<USER>/card-home',
  matched: [],
  meta: {},
  redirectedFrom: undefined,
};

vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush,
    currentRoute: {
      value: mockRoute,
    },
  }),
  useRoute: () => mockRoute,
}));

describe('CardHome 组件', () => {
  let wrapper: ReturnType<typeof mount>;
  let store: ReturnType<typeof useCardHomeStore>;

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useCardHomeStore();
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Feature: 商户卡片首页展示', () => {
    describe('Scenario: 页面初始化渲染', () => {
      it('Given 用户访问首页, When 页面加载完成, Then 应该显示正确的页面结构', () => {
        // Given & When
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // Then
        expect(wrapper.exists()).toBe(true);
        expect(wrapper.find('.card-home-page').exists()).toBe(true);
      });

      it('Given 页面加载, When 渲染完成, Then 应该显示页面标题', () => {
        // Given & When
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // Then
        const title = wrapper.find('h3.section-title');
        expect(title.exists()).toBe(true);
        expect(title.text()).toContain('商家名片');
      });
    });

    describe('Scenario: 功能卡片展示', () => {
      it('Given 页面渲染, When 显示功能区域, Then 应该展示所有功能卡片', () => {
        // Given & When
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // Then
        const featureCards = wrapper.findAll('.feature-card');
        expect(featureCards.length).toBeGreaterThan(0);
      });

      it('Given 功能卡片存在, When 检查卡片内容, Then 应该包含正确的功能信息', () => {
        // Given & When
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // Then
        const featureCards = wrapper.findAll('.feature-card');
        featureCards.forEach((card) => {
          expect(card.find('.card-title').exists()).toBe(true);
          expect(card.find('.card-description').exists()).toBe(true);
        });
      });
    });

    describe('Scenario: 开始配置按钮交互', () => {
      it('Given 页面加载完成, When 查找开始配置按钮, Then 按钮应该存在', () => {
        // Given & When
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // Then
        const startButton = wrapper.find('[data-testid="start-config-button"]');
        expect(startButton.exists()).toBe(true);
      });

      it('Given 开始配置按钮存在, When 按钮可用状态, Then 应该能够点击', async () => {
        // Given
        store.cardState.useable = false; // 按钮应该可用

        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // When
        const startButton = wrapper.find('[data-testid="start-config-button"]');

        // Then
        expect(startButton.exists()).toBe(true);
        expect(startButton.attributes('disabled')).toBeUndefined();
      });
    });
  });

  describe('Feature: 状态管理集成', () => {
    describe('Scenario: Store状态同步', () => {
      it('Given 组件挂载, When Store状态变化, Then 组件应该响应状态变化', async () => {
        // Given
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // When
        store.cardState.useable = true;
        await wrapper.vm.$nextTick();

        // Then
        expect(store.canStartConfig).toBe(false);
      });

      it('Given 组件存在, When 调用Store方法, Then 应该正确执行', async () => {
        // Given
        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        // When
        store.resetState();

        // Then
        expect(store.cardState.useable).toBe(false);
        expect(store.tutorialState.processId).toBe('');
        expect(store.error).toBe(null);
      });
    });
  });

  describe('Feature: 错误处理', () => {
    describe('Scenario: 错误状态显示', () => {
      it('Given Store中有错误, When 组件渲染, Then 应该显示错误信息', async () => {
        // Given
        store.error = '测试错误信息';

        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        await wrapper.vm.$nextTick();

        // Then
        expect(store.error).toBe('测试错误信息');
      });
    });
  });

  describe('Feature: 加载状态', () => {
    describe('Scenario: 加载状态显示', () => {
      it('Given Store处于加载状态, When 组件渲染, Then 应该显示加载状态', async () => {
        // Given
        store.loading = true;

        wrapper = mount(CardHome, {
          global: {
            plugins: [createPinia()],
          },
        });

        await wrapper.vm.$nextTick();

        // Then
        expect(store.loading).toBe(true);
      });
    });
  });
});
