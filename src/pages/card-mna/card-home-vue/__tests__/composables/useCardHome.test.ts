/**
 * useCardHome 组合式函数测试
 * 测试业务逻辑和组合式函数功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { setActivePinia, createPinia } from 'pinia';

import { useCardHome } from '../../composables/useCardHome';
import { cardHomeApi } from '@/services/cardHome';

// Mock API服务 - 必须在导入之前
vi.mock('@/services/cardHome', () => ({
  cardHomeApi: {
    createTutorialPublishProcess: vi.fn(),
    getCardInfo: vi.fn(),
    checkRouterStatus: vi.fn(),
  },
}));

// Mock Vue Router
const mockPush = vi.fn();
vi.mock('vue-router', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

describe('useCardHome 组合式函数', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
    vi.clearAllMocks();
  });

  describe('Feature: 组合式函数初始化', () => {
    describe('Scenario: 函数返回值验证', () => {
      it('Given 调用useCardHome, When 函数执行, Then 应该返回正确的属性和方法', () => {
        // Given & When
        const { store, router, handleStartConfig, handleFeatureClick, initializePage } = useCardHome();

        // Then
        expect(store).toBeDefined();
        expect(router).toBeDefined();
        expect(handleStartConfig).toBeTypeOf('function');
        expect(handleFeatureClick).toBeTypeOf('function');
        expect(initializePage).toBeTypeOf('function');
      });
    });
  });

  describe('Feature: 开始配置功能', () => {
    describe('Scenario: 成功创建新手引导', () => {
      it('Given 用户点击开始配置, When 创建新手引导成功, Then 应该跳转到详情页', async () => {
        // Given
        vi.mocked(cardHomeApi.createTutorialPublishProcess).mockResolvedValue({
          processId: 'test-process-123',
        });

        const { handleStartConfig } = useCardHome();

        // When
        await handleStartConfig();

        // Then
        expect(cardHomeApi.createTutorialPublishProcess).toHaveBeenCalledTimes(1);
        expect(mockPush).toHaveBeenCalledWith('/home/<USER>/card-detail');
      });

      it('Given 用户点击开始配置, When 创建新手引导失败, Then 应该显示错误信息', async () => {
        // Given
        vi.mocked(cardHomeApi.createTutorialPublishProcess).mockRejectedValue(new Error('创建失败'));

        const { handleStartConfig, store } = useCardHome();

        // When
        await handleStartConfig();

        // Then
        expect(store.error).toBe('创建失败');
        expect(mockPush).not.toHaveBeenCalled();
      });
    });
  });

  describe('Feature: 功能卡片点击', () => {
    describe('Scenario: 卡片点击导航', () => {
      it('Given 用户点击功能卡片, When 传入有效路径, Then 应该正确导航', () => {
        // Given
        const { handleFeatureClick } = useCardHome();
        const testPath = '/test/path';

        // When
        handleFeatureClick(testPath);

        // Then
        expect(mockPush).toHaveBeenCalledWith(testPath);
      });

      it('Given 用户点击功能卡片, When 传入空路径, Then 不应该导航', () => {
        // Given
        const { handleFeatureClick } = useCardHome();

        // When
        handleFeatureClick('');

        // Then
        expect(mockPush).not.toHaveBeenCalled();
      });
    });
  });

  describe('Feature: 页面初始化', () => {
    describe('Scenario: 初始化页面数据', () => {
      it('Given 页面加载, When 调用初始化方法, Then 应该检查卡片状态', async () => {
        // Given
        vi.mocked(cardHomeApi.getCardInfo).mockResolvedValue({
          isConfigured: false,
          cardType: '',
          lastUpdated: '',
        });

        const { initializePage, store } = useCardHome();

        // When
        await initializePage();

        // Then
        expect(cardHomeApi.getCardInfo).toHaveBeenCalledTimes(1);
        expect(store.loading).toBe(false);
      });

      it('Given 页面初始化, When API调用失败, Then 应该处理错误', async () => {
        // Given
        vi.mocked(cardHomeApi.getCardInfo).mockRejectedValue(new Error('网络错误'));

        const { initializePage, store } = useCardHome();

        // When
        await initializePage();

        // Then
        expect(store.error).toBe('网络错误');
        expect(store.loading).toBe(false);
      });
    });
  });

  describe('Feature: 状态管理集成', () => {
    describe('Scenario: Store状态访问', () => {
      it('Given 组合式函数初始化, When 访问store, Then 应该返回正确的store实例', () => {
        // Given & When
        const { store } = useCardHome();

        // Then
        expect(store.cardState).toBeDefined();
        expect(store.tutorialState).toBeDefined();
        expect(store.cardInfo).toBeDefined();
        expect(store.canStartConfig).toBeDefined();
        expect(store.hasProcessId).toBeDefined();
      });

      it('Given store存在, When 调用store方法, Then 应该正确执行', () => {
        // Given
        const { store } = useCardHome();

        // When
        store.resetState();

        // Then
        expect(store.cardState.useable).toBe(false);
        expect(store.tutorialState.processId).toBe('');
        expect(store.error).toBe(null);
      });
    });
  });

  describe('Feature: 路由管理', () => {
    describe('Scenario: 路由实例访问', () => {
      it('Given 组合式函数初始化, When 访问router, Then 应该返回正确的router实例', () => {
        // Given & When
        const { router } = useCardHome();

        // Then
        expect(router).toBeDefined();
        expect(router.push).toBeTypeOf('function');
      });
    });
  });
});
