<template>
  <div
    class="feature-card"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 卡片图片区域 -->
    <div class="feature-card__image">
      <img :src="card.image" :alt="card.title" class="feature-card__img" />
    </div>

    <!-- 卡片内容区域 -->
    <div class="feature-card__content">
      <div class="feature-card__header">
        <t-icon :name="card.icon" class="feature-card__icon" />
        <h3 class="feature-card__title">{{ card.title }}</h3>
      </div>

      <p class="feature-card__description">
        {{ card.description }}
      </p>
    </div>

    <!-- 悬停效果指示器 -->
    <div class="feature-card__hover-indicator">
      <t-icon name="chevron-right" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import type { FeatureCard } from '../types';

// Props
interface Props {
  card: FeatureCard;
}

const props = defineProps<Props>();

// Emits
type Emits = (e: 'click', card: FeatureCard) => void;

const emit = defineEmits<Emits>();

// 状态
const isHovered = ref(false);

// 方法
const handleClick = (): void => {
  emit('click', props.card);
};

const handleMouseEnter = (): void => {
  isHovered.value = true;
};

const handleMouseLeave = (): void => {
  isHovered.value = false;
};
</script>

<style scoped lang="scss">
.feature-card {
  position: relative;
  width: 32%;
  min-height: 200px;
  background: white;
  border: 1px solid #e7e7e7;
  border-radius: 8px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
    border-color: #0052d9;

    .feature-card__hover-indicator {
      opacity: 1;
      transform: translateX(0);
    }
  }

  &__image {
    width: 100%;
    height: 120px;
    margin-bottom: 16px;
    border-radius: 4px;
    overflow: hidden;
    background: #f5f5f5;
  }

  &__img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  &__icon {
    font-size: 20px;
    color: #0052d9;
    margin-right: 8px;
  }

  &__title {
    font-size: 16px;
    font-weight: 600;
    color: #000000;
    margin: 0;
  }

  &__description {
    font-size: 14px;
    color: #666666;
    line-height: 1.5;
    margin: 0;
    flex: 1;
  }

  &__hover-indicator {
    position: absolute;
    top: 50%;
    right: 16px;
    transform: translateY(-50%) translateX(10px);
    opacity: 0;
    transition: all 0.3s ease;
    color: #0052d9;
    font-size: 16px;
  }
}

// 响应式布局
@media (max-width: 1200px) {
  .feature-card {
    width: 48%;
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .feature-card {
    width: 100%;
    margin-bottom: 16px;
  }
}
</style>
