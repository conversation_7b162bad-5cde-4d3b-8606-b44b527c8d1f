<template>
  <t-button
    :theme="theme"
    :size="size"
    :loading="loading"
    :disabled="disabled"
    :class="buttonClass"
    @click="handleClick"
  >
    <template v-if="icon" #icon>
      <t-icon :name="icon" />
    </template>

    <slot>{{ text }}</slot>
  </t-button>
</template>

<script setup lang="ts">
import { computed } from 'vue';
// TDesign组件已全局注册，直接使用t-button和t-icon即可

// Props
interface Props {
  text?: string;
  theme?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  disabled?: boolean;
  icon?: string;
  block?: boolean;
  variant?: 'base' | 'outline' | 'dashed' | 'text';
}

const props = withDefaults(defineProps<Props>(), {
  text: '',
  theme: 'primary',
  size: 'medium',
  loading: false,
  disabled: false,
  block: false,
  variant: 'base',
});

// Emits
type Emits = (e: 'click', event: MouseEvent) => void;

const emit = defineEmits<Emits>();

// 计算属性
const buttonClass = computed(() => [
  'action-button',
  {
    'action-button--block': props.block,
    'action-button--loading': props.loading,
    'action-button--disabled': props.disabled,
  },
]);

// 方法
const handleClick = (event: MouseEvent): void => {
  if (!props.disabled && !props.loading) {
    emit('click', event);
  }
};
</script>

<style scoped lang="scss">
.action-button {
  transition: all 0.3s ease;

  &--block {
    width: 100%;
  }

  &--loading {
    pointer-events: none;
  }

  &--disabled {
    cursor: not-allowed;
  }

  &:not(.action-button--disabled):not(.action-button--loading):hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 82, 217, 0.2);
  }
}
</style>
