schema:
  globalStyle:
    formtitle:
      background-color: '#fff'
      padding: 16px
    cardSection:
      margin-top: 20px
      background-color: '#fff'
      border-radius: 8px
      padding: 24px
    featureCard:
      display: flex
      flex-direction: column
      align-items: center
      text-align: center
      margin-top: 20px
  cssClasses:
    card-container:
      display: flex
      justify-content: space-between
      flex-wrap: wrap
    feature-card:
      width: 32%
      border: '1px solid #E7E7E7'
      border-radius: 8px
      padding: 20px
      cursor: pointer
      transition: all 0.3s
    feature-card-hover:
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1)
    card-image:
      width: 100%
      height: 100%
    card-title:
      font-size: 16px
      font-weight: '500'
      margin-top: 16px
      color: '#000'
  meta:
    icon: shop
    title: 微信支付 品牌经营
  layout: v-simplelayout
  main:
    interactions:
      - path: '#/interactions/CardOverviewHeader'
        slot: top1
      - path: '#/interactions/BusinessCardSection'
        slot: top2
  apis:
    getCardInfo:
      url: /api/merchant/card/info
      method: GET
    createCard:
      url: /api/merchant/card/create
      method: POST
    
  definitions:
    cardInfo:
      state:
        isConfigured: false
        cardType: ''
        lastUpdated: ''
    cardState:
      state:
        useable: false
    tutorialState:
      state:
        processId: ''
  interactions:
    # 初始化交互
    CardOverviewHeader:
      steps:
        - stage: render
          renderType: div
          props:
            class: formtitle
          children: []
    # 跳转
    # JumpToCardDetail:
    #   steps:
    #     - stage: render
    #       renderType: div
    #       jump: /home/<USER>/card-detail
    # 初始化交互
    BusinessCardSection:
      steps:
        # - stage: process
        #   handler:
        #     from: userProvide
        #     path: home.init
        # - stage: error
        #   handlers:
        #     - case: code
        #       when: 2000
        #       interaction: "#/interactions/JumpToCardDetail"
        #       slot: errorinfo
        #api: '#/apis/GetTutorialPublishProcess'
        - stage: render
          from:
            - state: '#/definitions/cardState/state'
              as: cardState
          renderType: div
          props:
            class: cardSection
          children:
            - renderType: div
              props:
                style:
                  display: flex
                  justify-content: space-between
                  align-items: center
                  margin-bottom: 16px
              children:
                - renderType: div
                  children:
                    - renderType: h3
                      props:
                        style:
                          font-size: 20px
                          font-weight: '500'
                      children:
                        - 商家名片
                    - renderType: p
                      props:
                        style:
                          color: '#666'
                          font-size: 16px
                          opacity: '.3'
                          margin-bottom: 16px
                      children:
                        - 助力商家服务与经营，帮助商家实现和用户的深度连接
                - renderType: v-button
                  props:
                    theme: primary
                    content: 开始配置 # 这个按钮点了后要建单
                    disabled: boolean:${cardState.useable}
                    events:
                      click:
                      - interaction: '#/interactions/CreateNewTutorial'
                      - jump: /home/<USER>/card-detail
            - renderType: div
              props:
                class: card-container
              children:
                - renderType: div
                  props:
                    class: feature-card
                  events:
                    click:
                      jump: /card-mna/transaction-card
                  children:
                    - renderType: div
                      props:
                        style:
                          display: flex
                          flex-wrap: nowrap
                          gap: 16px
                          width: 100%
                          align-items: stretch
                      children:
                        - renderType: div
                          props:
                            style:
                              width: calc(50% - 8px)
                          children:
                            - renderType: img
                              props:
                                class: card-image
                                src: ${asserts.img1}
                                alt: 交易连接名片
                                style:
                                  width: 100%
                                  height: auto
                        - renderType: div
                          props:
                            style:
                              width: calc(50% - 8px)
                          children:
                            - renderType: img
                              props:
                                class: card-image
                                src: ${asserts.img2}
                                alt: 商品服务
                                style:
                                  width: 100%
                                  height: auto
                    - renderType: div
                      props:
                        style:
                          margin-top: 16px
                          text-align: left
                          width: 100%
                      children:
                        - renderType: p
                          props:
                            class: card-title
                          children:
                            - 交易连接名片
                    - renderType: p
                      props:
                        style:
                          margin-top: 14px
                          font-weight: 400
                          text-align: left
                          width: 100%
                          color: rgba(0, 0, 0, 0.5)
                      children:
                        - 用户支付成功后，微信支付公众号向用户下发支付成功的交易凭证，用户点击凭证跳转商家名片
                - renderType: div
                  props:
                    class: feature-card
                  events:
                    click:
                      jump: /card-mna/service-card
                  children:
                    - renderType: div
                      props:
                        style:
                          display: flex
                          flex-wrap: nowrap
                          gap: 16px
                          width: 100%
                      children:
                        - renderType: div
                          props:
                            style:
                              width: calc(50% - 8px)
                          children:
                            - renderType: img
                              props:
                                class: card-image
                                src: ${asserts.img3}
                                alt: 商品服务1
                                style:
                                  width: 100%
                                  height: auto
                        - renderType: div
                          props:
                            style:
                              width: calc(50% - 8px)
                          children:
                            - renderType: img
                              props:
                                class: card-image
                                src: ${asserts.img4}
                                alt: 商品服务2
                                style:
                                  width: 100%
                                  height: auto
                    - renderType: div
                      props:
                        style:
                          margin-top: 16px
                          text-align: left
                          width: 100%
                      children:
                        - renderType: p
                          props:
                            class: card-title
                          children:
                            - 名片连接商家的服务与优惠
                    - renderType: p
                      props:
                        style:
                          margin-top: 14px
                          font-weight: 400
                          text-align: left
                          width: 100%
                          color: rgba(0, 0, 0, 0.5)
                      children:
                        - 名片连接商家的小程序、视频号和客服，提供自定义服务配置，展示用户在商家的优惠券和会员权益
                - renderType: div
                  props:
                    class: feature-card
                  events:
                    click:
                      jump: /card-mna/promotion-card
                  children:
                    - renderType: div
                      props:
                        style:
                          display: flex
                          flex-wrap: nowrap
                          gap: 16px
                          width: 100%
                      children:
                        - renderType: div
                          props:
                            style:
                              width: calc(50% - 8px)
                          children:
                            - renderType: img
                              props:
                                class: card-image
                                src: ${asserts.img5}
                                alt: 宣传推广1
                                style:
                                  width: 100%
                                  height: auto
                        - renderType: div
                          props:
                            style:
                              width: calc(50% - 8px)
                          children:
                            - renderType: img
                              props:
                                class: card-image
                                src: ${asserts.img6}
                                alt: 宣传推广2
                                style:
                                  width: 100%
                                  height: auto
                    - renderType: div
                      props:
                        style:
                          margin-top: 16px
                          text-align: left
                          width: 100%
                      children:
                        - renderType: p
                          props:
                            class: card-title
                          children:
                            - 用户标记喜欢，稳定找回商家
                    - renderType: p
                      props:
                        style:
                          margin-top: 14px
                          font-weight: 400
                          text-align: left
                          width: 100%
                          color: rgba(0, 0, 0, 0.5)
                      children:
                        - 用户标记了喜欢商家，商家名片将出现在「微信支付公众号-摇优惠-喜欢的商家」列表，便于用户发现更多优惠
    CreateNewTutorial:
      steps:
        - stage: process
          api: '#/apis/CreateTutorialPublishProcess'
        - stage: handle
          states:
            - '#/definitions/tutorialState/state':
                processId: '$StepData$.data.processId'
