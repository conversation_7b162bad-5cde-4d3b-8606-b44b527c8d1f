{"steps": [{"stage": "render", "from": [{"state": "#/definitions/pageStatus/state", "as": "pageStatus"}, {"state": "#/definitions/configItems/state", "as": "configData"}], "renderType": "div", "props": {"style": {"paddingLeft": "8px"}}, "children": [{"renderType": "div", "props": {"class": "card-detail-item"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["基础信息"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "text": {"from": "userProvide", "text": "已配置", "path": "tutorial.renderStatusTag"}}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["配置品牌的基础信息，提供电话、在线客服等用户咨询途径，支持连接品牌在微信内的多个场景，如小程序、视频号"]}]}, {"renderType": "v-button", "props": {"theme": "default", "content": "查看", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/card-view"}]}}}]}]}, {"renderType": "div", "props": {"class": "card-detail-item"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["名片服务"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "text": {"from": "userProvide", "text": "已配置", "path": "tutorial.renderStatusTag"}}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["向用户提供自定义服务，如官方商城、新品速递、订单查询、售后服务等，提升用户服务质量"]}]}, {"renderType": "v-button", "props": {"theme": "default", "content": "查看", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/service-config/preview"}]}}}]}]}, {"renderType": "div", "props": {"class": "card-detail-item"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["品牌会员"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "children": [{"renderType": "span", "text": {"from": "userProvide", "text": "${pageStatus.brandAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["打通品牌的会员体系，用户便捷开通、查询会员权益，支持用户购买付费会员"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.brandAccessStatusButtonStyle}", "content": "${pageStatus.brandAccessStatusText}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "https://pay.weixin.qq.com/doc/brand/4015562403", "inapp": false, "target": "_blank"}]}}}]}]}, {"renderType": "div", "props": {"class": "card-detail-item", "style": {"marginBottom": "0"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["品牌优惠"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "children": [{"renderType": "span", "text": {"from": "userProvide", "text": "${pageStatus.couponAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["基于用户在微信支付的消费偏好，向用户提供最短路径发现和使用优惠的能力"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.couponAccessStatusButtonStyle}", "content": "${pageStatus.couponAccessStatusText}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "https://pay.weixin.qq.com/doc/v3/merchant/4013839283", "inapp": false, "target": "_blank"}]}}}]}]}]}]}