schema:
  globalStyle:
    formtitle:
      background-color: '#fff'
      padding: 16px
    cardSection:
      margin-top: 20px
      background-color: '#fff'
      border-radius: 8px
      padding: 24px
    dashboardHeader:
      margin-bottom: 24px
    dashboardStats:
      margin-bottom: 32px
    cardListSection:
      background-color: '#fff'
      border-radius: 8px
      padding: 24px
  cssClasses:
    dashboard-container:
      max-width: 1200px
      background-color: '#fff'
      padding: 24px
      margin: 12px
      width: 1224px
      border-radius: 16px
    flex-between:
      display: flex
      justify-content: space-between
      align-items: center
    stats-container:
      display: flex
      gap: 24px
      margin-top: 24px
    stat-card:
      flex: 1
      padding: 24px
      background-color: '#fafafa'
      border-radius: 8px
      border: '1px solid #E7E7E7'
    stat-title:
      font-size: 14px
      color: '#666'
      margin-bottom: 8px
    stat-value:
      font-size: 24px
      font-weight: 500
      color: '#000'
    card-grid:
      display: grid
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr))
      gap: 24px
      margin-top: 24px
    card-item:
      border: '1px solid #E7E7E7'
      border-radius: 8px
      padding: 16px
      background-color: '#fff'
      transition: all 0.3s ease
    card-status:
      display: inline-block
      padding: '4px 8px'
      border-radius: 4px
      font-size: 12px
      margin-bottom: 12px
    card-active:
      background-color: '#E6F7FF'
      color: '#1890FF'
    card-inactive:
      background-color: '#F2F3F5'
      color: '#909399'
    card-title:
      font-size: 16px
      font-weight: 500
      color: '#000'
      margin-bottom: 12px
    card-footer:
      display: flex
      justify-content: space-between
      align-items: center
      margin-top: 16px
      padding-top: 16px
      border-top: '1px solid #f0f0f0'
    card-detail-item:
      background-color: rgba(0, 0, 0, 0.02)
      padding: 24px
      margin-bottom: 24px
      border-radius: 8px
  layout: v-simplelayout
  meta:
    icon: dashboard
    title: 微信支付 品牌经营
  main:
    interactions:
    - "#/interactions/initapis"
    # - path: "#/interactions/dashboard"
    #   slot: top1
    - path: "#/interactions/cardList"
      slot: top2
  apis:
    getCardStats:
      url: /api/card/stats
      method: GET
    getCardList:
      url: /api/card/list
      method: GET
    getDashboard:
      url: /api/dashboard
      method: GET
    activateCard:
      url: /api/card/activate
      method: POST
    deactivateCard:
      url: /api/card/deactivate
      method: POST
  definitions:
    cardStats:
      state:
        totalCards: 0
        activeCards: 0
        viewCount: 0
        conversionRate: 0
    cardList:
      state:
        cards: []
        loading: false
        total: 0
        query:
          page: 1
          pageSize: 10
          status: ''
  interactions:
    dashboard:
      steps:
      - stage: render
        renderType: div
        props:
          class: dashboard-container
        children:
          - interaction: "#/interactions/dashboardHeader"
          - interaction: "#/interactions/dashboardStats"
            id: dashboardStats
    dashboardHeader:
      steps:
        - stage: render
          renderType: div
          props:
            class: dashboardHeader
          children:
            - renderType: div
              props:
                class: flex-between
              children:
                - renderType: h2
                  props:
                    style:
                      font-size: 20px
                      font-weight: 500
                      margin-bottom: 8px
                  children:
                    - 名片数据
                - renderType: div
                  children:
                    - renderType: v-date-picker
                      props:
                        events:
                          change:
                          - interaction: "#/interactions/dashboardStats"
                            id: dashboardStats

    dashboardStats:
      steps:
        - stage: prepare
          from:
          - variables:
            - eventData
            as:
            - date
        - stage: process
          # api: "#/apis/getCardStats"
          # loading: "#/global/loading"
        # - stage: error
        #   handlers:
        #     - case: code
        #       not: 0
        #       message: 获取仪表盘统计数据失败
        #       error: "#/global/error"
        - stage: handle
          states:
            - "#/definitions/cardStats/state":
                totalCards: "$StepData$.data.stats.totalCards"
                activeCards: "$StepData$.data.stats.activeCards"
                viewCount: "$StepData$.data.stats.viewCount"
                conversionRate: "$StepData$.data.stats.conversionRate"
        - stage: render
          from:
            - state: "#/definitions/cardStats/state"
              as: cardStats
          renderType: div
          props:
            class: dashboardStats
          children:
            - renderType: div
              condition:
                literal: 0
              props:
                class: stats-container
              children:
                - interaction: "#/interactions/statCard"
                  props:
                    title: 名片总数
                    value: "${cardStats.totalCards}"
                    icon: card
                - interaction: "#/interactions/statCard"
                  props:
                    title: 已激活名片
                    value: "${cardStats.activeCards}"
                    icon: check-circle
                - interaction: "#/interactions/statCard"
                  props:
                    title: 展示次数
                    value: "${cardStats.viewCount}"
                    icon: view
                - interaction: "#/interactions/statCard"
                  props:
                    title: 转化率
                    value: "${cardStats.conversionRate}%"
                    icon: chart
    statCard:
      steps:
        - stage: render
          from:
            - variables: title
              as: title
            - variables: value
              as: value
            - variables: icon
              as: icon
          renderType: div
          props:
            class: stat-card
          children:
            - renderType: div
              props:
                class: stat-title
              children:
                - "${title}"
            - renderType: div
              props:
                class: stat-value
              children:
                - "${value}"

    cardList:
      steps:
      - stage: render
        renderType: div
        props:
          class: dashboard-card-container
          style:
            display: flex
            flex-direction: row
            border: 1px solid rgba(0, 0, 0, 0.04)
            backgroundColor: '#fff'
            width: 1224px
            minWidth: 960px
            border-radius: 16px
        children:
          - renderType : div
            props:
              class: flex-left
              style:
                flex: 1
                overflow: auto
                padding: 32px
                padding-right: 0
            children:
              - renderType: h3
                props:
                  style:
                    margin-bottom: 24px
                    font-size: 20px
                    font-weight: 500
                    color: '#000'
                children:
                  - "名片功能"
              - interaction: "#/interactions/cardDetail"
          - renderType: div
            props:
              class: flex-right
              style:
                padding: 32px
                padding-light: 0
            children:
              - interaction: "#/interactions/rightPreview"
    rightPreview:
      steps:
      - stage: render
        # 右侧名片预览区
        renderType: v-businesscard
        props:
          class: 'right-panel'
          from:
            - state: "#/definitions/cardDetail/state"
              as: cardDetail  
          style:
            height: '100%'
            background-color: '#f7f8fa'
            border-left: '1px solid #E7E7E7'
            width: '320px'
          cardDetail: '${cardDetail}'
          data:
            # topInfo:
            #   avatar: 'https://dummyimage.com/80x80/222/fff&text=image'
            #   name: 爱马哥123image爱马哥image爱马哥image
            #   tagsAvatars:
            #     - 'https://dummyimage.com/18x18/eee/aaa&text=U'
            #     - 'https://dummyimage.com/18x18/eee/aaa&text=U'
            #     - 'https://dummyimage.com/80x80/222/fff&text=image'
            #   tags:
            #     - 等1000+人喜欢
            # cards:
            #   - title: 小程序
            #     desc: 新品上市
            #     images:
            #       - 'https://img.js.design/assets/img/65f2e1e2b9b6b7b7b2e8b7b7.png'
            #       - 'https://img.js.design/assets/img/65f2e1e2b9b6b7b7b2e8b7b8.png'
            #   - title: 视频号
            #     desc: 新发布作品
            #     images:
            #       - 'https://img.js.design/assets/img/65f2e1e2b9b6b7b7b2e8b7b9.png'
            # otherTab: 优惠1
            # activeTab: service
            showQRCode: pop
            serviceList: []
            page: dashboard
            # moreServices:
            #   - name: 关于image
            #   - name: 新品专辑
            # likeList:
            #   - avatarText: W
            #     title: 爱马哥爱马哥爱马哥爱马哥爱马哥爱马哥爱马哥
            #     tags:
            #       - 会员专场
            #       - 小程序下单
            #       - 小程序下单
            #     desc: 仅有内容区域的卡片形式。卡片内容区域可以是文字、图片、表单、表格等形式信息内容。可使用大中小不同的卡片尺寸，按业务需求进行呈现。
            #   - avatarText: W
            #     title: 无印良品
            #     tags:
            #       - 会员专场
            #       - 小程序下单
            #     desc: 无印良品的描述内容。

    cardDetail: !include ./cardStatus.yaml
    initapis: !include initapis.yaml
