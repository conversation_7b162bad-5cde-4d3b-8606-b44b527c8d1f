{"schema": {"globalStyle": {"formtitle": {"background-color": "#fff", "padding": "16px"}, "cardSection": {"margin-top": "20px", "background-color": "#fff", "border-radius": "8px", "padding": "24px"}, "dashboardHeader": {"margin-bottom": "24px"}, "dashboardStats": {"margin-bottom": "32px"}, "cardListSection": {"background-color": "#fff", "border-radius": "8px", "padding": "24px"}}, "cssClasses": {"dashboard-container": {"max-width": "1200px", "background-color": "#fff", "padding": "24px", "margin": "12px", "width": "1224px", "border-radius": "16px"}, "flex-between": {"display": "flex", "justify-content": "space-between", "align-items": "center"}, "stats-container": {"display": "flex", "gap": "24px", "margin-top": "24px"}, "stat-card": {"flex": 1, "padding": "24px", "background-color": "#fafafa", "border-radius": "8px", "border": "1px solid #E7E7E7"}, "stat-title": {"font-size": "14px", "color": "#666", "margin-bottom": "8px"}, "stat-value": {"font-size": "24px", "font-weight": 500, "color": "#000"}, "card-grid": {"display": "grid", "grid-template-columns": "repeat(auto-fill, minmax(280px, 1fr))", "gap": "24px", "margin-top": "24px"}, "card-item": {"border": "1px solid #E7E7E7", "border-radius": "8px", "padding": "16px", "background-color": "#fff", "transition": "all 0.3s ease"}, "card-status": {"display": "inline-block", "padding": "4px 8px", "border-radius": "4px", "font-size": "12px", "margin-bottom": "12px"}, "card-active": {"background-color": "#E6F7FF", "color": "#1890FF"}, "card-inactive": {"background-color": "#F2F3F5", "color": "#909399"}, "card-title": {"font-size": "16px", "font-weight": 500, "color": "#000", "margin-bottom": "12px"}, "card-footer": {"display": "flex", "justify-content": "space-between", "align-items": "center", "margin-top": "16px", "padding-top": "16px", "border-top": "1px solid #f0f0f0"}, "card-detail-item": {"background-color": "rgba(0, 0, 0, 0.02)", "padding": "24px", "margin-bottom": "24px", "border-radius": "8px"}}, "layout": "v-simplelayout", "meta": {"icon": "dashboard", "title": "微信支付 品牌经营"}, "main": {"interactions": ["#/interactions/initapis", {"path": "#/interactions/cardList", "slot": "top2"}]}, "apis": {"getCardStats": {"url": "/api/card/stats", "method": "GET"}, "getCardList": {"url": "/api/card/list", "method": "GET"}, "getDashboard": {"url": "/api/dashboard", "method": "GET"}, "activateCard": {"url": "/api/card/activate", "method": "POST"}, "deactivateCard": {"url": "/api/card/deactivate", "method": "POST"}}, "definitions": {"cardStats": {"state": {"totalCards": 0, "activeCards": 0, "viewCount": 0, "conversionRate": 0}}, "cardList": {"state": {"cards": [], "loading": false, "total": 0, "query": {"page": 1, "pageSize": 10, "status": ""}}}}, "interactions": {"dashboard": {"steps": [{"stage": "render", "renderType": "div", "props": {"class": "dashboard-container"}, "children": [{"interaction": "#/interactions/dashboardHeader"}, {"interaction": "#/interactions/dashboardStats", "id": "dashboardStats"}]}]}, "dashboardHeader": {"steps": [{"stage": "render", "renderType": "div", "props": {"class": "dashboardHeader"}, "children": [{"renderType": "div", "props": {"class": "flex-between"}, "children": [{"renderType": "h2", "props": {"style": {"font-size": "20px", "font-weight": 500, "margin-bottom": "8px"}}, "children": ["名片数据"]}, {"renderType": "div", "children": [{"renderType": "v-date-picker", "props": {"events": {"change": [{"interaction": "#/interactions/dashboardStats", "id": "dashboardStats"}]}}}]}]}]}]}, "dashboardStats": {"steps": [{"stage": "prepare", "from": [{"variables": ["eventData"], "as": ["date"]}]}, {"stage": "process"}, {"stage": "handle", "states": [{"#/definitions/cardStats/state": {"totalCards": "$StepData$.data.stats.totalCards", "activeCards": "$StepData$.data.stats.activeCards", "viewCount": "$StepData$.data.stats.viewCount", "conversionRate": "$StepData$.data.stats.conversionRate"}}]}, {"stage": "render", "from": [{"state": "#/definitions/cardStats/state", "as": "cardStats"}], "renderType": "div", "props": {"class": "dashboardStats"}, "children": [{"renderType": "div", "condition": {"literal": 0}, "props": {"class": "stats-container"}, "children": [{"interaction": "#/interactions/statCard", "props": {"title": "名片总数", "value": "${cardStats.totalCards}", "icon": "card"}}, {"interaction": "#/interactions/statCard", "props": {"title": "已激活名片", "value": "${cardStats.activeCards}", "icon": "check-circle"}}, {"interaction": "#/interactions/statCard", "props": {"title": "展示次数", "value": "${cardStats.viewCount}", "icon": "view"}}, {"interaction": "#/interactions/statCard", "props": {"title": "转化率", "value": "${cardStats.conversionRate}%", "icon": "chart"}}]}]}]}, "statCard": {"steps": [{"stage": "render", "from": [{"variables": "title", "as": "title"}, {"variables": "value", "as": "value"}, {"variables": "icon", "as": "icon"}], "renderType": "div", "props": {"class": "stat-card"}, "children": [{"renderType": "div", "props": {"class": "stat-title"}, "children": ["${title}"]}, {"renderType": "div", "props": {"class": "stat-value"}, "children": ["${value}"]}]}]}, "cardList": {"steps": [{"stage": "render", "renderType": "div", "props": {"class": "dashboard-card-container", "style": {"display": "flex", "flex-direction": "row", "border": "1px solid rgba(0, 0, 0, 0.04)", "backgroundColor": "#fff", "width": "1224px", "minWidth": "960px", "border-radius": "16px"}}, "children": [{"renderType": "div", "props": {"class": "flex-left", "style": {"flex": 1, "overflow": "auto", "padding": "32px", "padding-right": 0}}, "children": [{"renderType": "h3", "props": {"style": {"margin-bottom": "24px", "font-size": "20px", "font-weight": 500, "color": "#000"}}, "children": ["名片功能"]}, {"interaction": "#/interactions/cardDetail"}]}, {"renderType": "div", "props": {"class": "flex-right", "style": {"padding": "32px", "padding-light": 0}}, "children": [{"interaction": "#/interactions/rightPreview"}]}]}]}, "rightPreview": {"steps": [{"stage": "render", "renderType": "v-businesscard", "props": {"class": "right-panel", "from": [{"state": "#/definitions/cardDetail/state", "as": "cardDetail"}], "style": {"height": "100%", "background-color": "#f7f8fa", "border-left": "1px solid #E7E7E7", "width": "320px"}, "cardDetail": "${cardDetail}", "data": {"showQRCode": "pop", "serviceList": [], "page": "dashboard"}}}]}, "cardDetail": {"steps": [{"stage": "render", "from": [{"state": "#/definitions/pageStatus/state", "as": "pageStatus"}, {"state": "#/definitions/configItems/state", "as": "configData"}], "renderType": "div", "props": {"style": {"paddingLeft": "8px"}}, "children": [{"renderType": "div", "props": {"class": "card-detail-item"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["基础信息"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "text": {"from": "userProvide", "text": "已配置", "path": "tutorial.renderStatusTag"}}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["配置品牌的基础信息，提供电话、在线客服等用户咨询途径，支持连接品牌在微信内的多个场景，如小程序、视频号"]}]}, {"renderType": "v-button", "props": {"theme": "default", "content": "查看", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/card-view"}]}}}]}]}, {"renderType": "div", "props": {"class": "card-detail-item"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["名片服务"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "text": {"from": "userProvide", "text": "已配置", "path": "tutorial.renderStatusTag"}}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["向用户提供自定义服务，如官方商城、新品速递、订单查询、售后服务等，提升用户服务质量"]}]}, {"renderType": "v-button", "props": {"theme": "default", "content": "查看", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "/home/<USER>/service-config/preview"}]}}}]}]}, {"renderType": "div", "props": {"class": "card-detail-item"}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["品牌会员"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "children": [{"renderType": "span", "text": {"from": "userProvide", "text": "${pageStatus.brandAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["打通品牌的会员体系，用户便捷开通、查询会员权益，支持用户购买付费会员"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.brandAccessStatusButtonStyle}", "content": "${pageStatus.brandAccessStatusText}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "https://pay.weixin.qq.com/doc/brand/4015562403", "inapp": false, "target": "_blank"}]}}}]}]}, {"renderType": "div", "props": {"class": "card-detail-item", "style": {"marginBottom": "0"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "justifyContent": "space-between", "alignItems": "center"}}, "children": [{"renderType": "div", "props": {"style": {"flex": "1"}}, "children": [{"renderType": "div", "props": {"style": {"display": "flex", "alignItems": "center", "marginBottom": "8px"}}, "children": [{"renderType": "span", "props": {"style": {"fontSize": "16px", "fontWeight": "500"}}, "children": ["品牌优惠"]}, {"renderType": "span", "props": {"style": {"marginLeft": "8px"}}, "children": [{"renderType": "span", "text": {"from": "userProvide", "text": "${pageStatus.couponAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"style": {"margin": "0", "color": "rgba(0, 0, 0, 0.5)", "lineHeight": "22px", "fontSize": "14px"}}, "children": ["基于用户在微信支付的消费偏好，向用户提供最短路径发现和使用优惠的能力"]}]}, {"renderType": "v-button", "props": {"theme": "${pageStatus.couponAccessStatusButtonStyle}", "content": "${pageStatus.couponAccessStatusText}", "style": {"marginLeft": "16px"}, "events": {"click": [{"jump": "https://pay.weixin.qq.com/doc/v3/merchant/4013839283", "inapp": false, "target": "_blank"}]}}}]}]}]}]}, "initapis": {"steps": [{"stage": "process", "loading": "#/global/loading", "apis": [["#/apis/GetLatestCardInfoPublishProcess", "#/apis/GetLatestServicePublishProcess", "#/apis/GetBrandMemberAccessState", "#/apis/GetShakeCouponAccessState"]]}, {"stage": "handle", "handler": {"before": {"from": "userProvide", "path": "dashboard.initPageState"}}, "states": [{"#/definitions/pageStatus/state": "$StepData$"}]}]}}}}