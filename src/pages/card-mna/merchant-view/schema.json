{"schema": {"globalStyle": {"formtitle": {"background-color": "#fff", "padding": "16px"}, "section": {"margin-bottom": "24px", "background-color": "#fff", "border-radius": "8px", "padding": "24px"}, "section-title": {"font-size": "16px", "font-weight": "bold", "margin-bottom": "16px"}}, "cssClasses": {"merchant-card": {"background-color": "#fff", "border-radius": "8px", "padding": "24px", "margin-bottom": "24px"}, "merchant-header": {"display": "flex", "align-items": "center", "margin-bottom": "32px"}, "merchant-avatar": {"width": "80px", "height": "80px", "border-radius": "50%", "margin-right": "16px", "background-color": "#f5f5f5"}, "merchant-info": {"display": "flex", "flex-direction": "column"}, "merchant-name": {"font-size": "20px", "font-weight": "500", "margin-bottom": "8px"}, "info-section": {"margin-top": "24px", "border-top": "1px solid #f0f0f0", "padding-top": "24px"}, "info-title": {"font-size": "16px", "font-weight": "500", "margin-bottom": "16px"}, "info-row": {"display": "flex", "margin-bottom": "16px"}, "info-label": {"width": "120px", "color": "rgba(0,0,0,0.6)"}, "info-value": {"flex": "1"}}, "layout": "v-simplelayout", "meta": {"icon": "shop", "title": "i18n:商家信息查看"}, "main": {"interactions": ["#/interactions/getMerchantInfo", {"path": "#/interactions/renderMerchantInfo", "slot": "top1"}]}, "apis": {"getMerchantInfo": {"url": "/api/merchant/info", "method": "GET"}}, "definitions": {"merchantInfo": {"state": {"merchantName": "奈雪的茶", "merchantLogo": "", "customerServiceType": 1, "customerServicePath": "/E1QDEFPQUJWB/s11gfxefx0j6/index?storeId=0a9295c8-8ce7-4b92-9d77-7ecdd186f346&serviceType=1", "servicePhone": "", "sceneType": 1, "sceneTag": "", "sceneId": "", "videoAccount": "格zZGegeir", "sceneImages": [], "description": "爱马哥，提供美味可口的品质饮品和轻食"}}, "errors": {"state": {}}}, "interactions": {"getMerchantInfo": {"steps": [{"stage": "process", "api": "#/apis/getMerchantInfo", "condition": {"op": "eq", "path": "#/definitions/merchantInfo/state.merchantName", "value": ""}}, {"stage": "handle", "states": [{"#/definitions/merchantInfo/state": "$StepData$.data"}]}]}, "renderMerchantInfo": {"steps": [{"stage": "render", "from": [{"state": "#/definitions/merchantInfo/state", "as": "merchantInfo"}], "renderType": "div", "props": {"class": "merchant-view-container"}, "children": [{"renderType": "div", "props": {"class": "merchant-card"}, "children": [{"renderType": "div", "props": {"class": "merchant-header"}, "children": [{"renderType": "img", "props": {"class": "merchant-avatar", "src": "${merchantInfo.merchantLogo || \"/default-avatar.png\"}", "alt": "${merchantInfo.merchantName}"}}, {"renderType": "div", "props": {"class": "merchant-info"}, "children": [{"renderType": "h2", "props": {"class": "merchant-name"}, "children": ["${merchantInfo.merchantName}"]}]}]}]}, {"renderType": "div", "props": {"class": "merchant-card"}, "children": [{"renderType": "h3", "props": {"class": "info-title"}, "children": ["商家客服"]}, {"renderType": "div", "props": {"class": "info-row"}, "children": [{"renderType": "span", "props": {"class": "info-label"}, "children": ["客服类型"]}, {"renderType": "span", "props": {"class": "info-value"}, "children": ["自建客服"]}]}, {"renderType": "div", "props": {"class": "info-row"}, "children": [{"renderType": "span", "props": {"class": "info-label"}, "children": ["自建客服Path"]}, {"renderType": "span", "props": {"class": "info-value"}, "children": ["${merchantInfo.customerServicePath}"]}]}]}, {"renderType": "div", "props": {"class": "merchant-card"}, "children": [{"renderType": "h3", "props": {"class": "info-title"}, "children": ["场景跳转"]}, {"renderType": "div", "props": {"class": "info-row"}, "children": [{"renderType": "span", "props": {"class": "info-label"}, "children": ["视频号"]}, {"renderType": "span", "props": {"class": "info-value"}, "children": ["${merchantInfo.videoAccount}"]}]}, {"renderType": "div", "props": {"class": "info-row"}, "children": [{"renderType": "span", "props": {"class": "info-label"}, "children": ["场景标签"]}, {"renderType": "span", "props": {"class": "info-value"}, "children": ["${merchantInfo.sceneTag || \"-\"}"]}]}, {"renderType": "div", "props": {"class": "info-row"}, "children": [{"renderType": "span", "props": {"class": "info-label"}, "children": ["场景展示图片"]}, {"renderType": "span", "props": {"class": "info-value"}, "children": ["${merchantInfo.sceneImages && merchantInfo.sceneImages.length ? \"-\" : \"-\"}"]}]}]}, {"renderType": "div", "props": {"class": "merchant-card"}, "children": [{"renderType": "h3", "props": {"class": "info-title"}, "children": ["名片简介"]}, {"renderType": "div", "props": {"class": "info-row"}, "children": [{"renderType": "p", "props": {"style": {"margin": "0"}}, "children": ["${merchantInfo.description}"]}]}]}]}]}}}}