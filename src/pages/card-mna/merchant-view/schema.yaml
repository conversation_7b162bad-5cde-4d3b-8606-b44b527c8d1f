schema:
  globalStyle:
    formtitle:
      background-color: '#fff'
      padding: '16px'
    section:
      margin-bottom: '24px'
      background-color: '#fff'
      border-radius: '8px'
      padding: '24px'
    section-title:
      font-size: '16px'
      font-weight: 'bold'
      margin-bottom: '16px'
  cssClasses:
    merchant-card:
      background-color: '#fff'
      border-radius: '8px'
      padding: '24px'
      margin-bottom: '24px'
      box-shadow: '0 2px 8px rgba(0,0,0,0.05)'
    merchant-header:
      display: 'flex'
      align-items: 'center'
      margin-bottom: '32px'
    merchant-avatar:
      width: '80px'
      height: '80px'
      border-radius: '50%'
      margin-right: '16px'
      background-color: '#f5f5f5'
      object-fit: 'cover'
    merchant-info:
      display: 'flex'
      flex-direction: 'column'
    merchant-name:
      font-size: '20px'
      font-weight: '500'
      margin-bottom: '8px'
    info-section:
      margin-top: '24px'
      border-top: '1px solid #f0f0f0'
      padding-top: '24px'
    info-title:
      font-size: '16px'
      font-weight: '500'
      margin-bottom: '16px'
      padding-bottom: '8px'
      border-bottom: '1px solid #f0f0f0'
    info-row:
      display: 'flex'
      margin-bottom: '16px'
    info-label:
      width: '120px'
      color: 'rgba(0,0,0,0.6)'
    info-value:
      flex: '1'
    action-buttons:
      display: 'flex'
      justify-content: 'flex-start'
      margin-top: '24px'
    image-gallery:
      display: 'flex'
      flex-wrap: 'wrap'
      gap: '8px'
    gallery-image:
      width: '80px'
      height: '80px'
      border-radius: '4px'
      object-fit: 'cover'
  layout: v-cardmnalayout
  meta:
    icon: shop
    title: 微信支付 品牌经营
  main:
    interactions:
      - '#/interactions/getMerchantInfo'
      - path: '#/interactions/renderMerchantInfo'
        slot: 'top1'
      - path: '#/interactions/renderActionButtons'
        slot: 'top2'
  apis:
    getMerchantInfo:
      url: '/api/merchant/info'
      method: 'GET'
    updateMerchantStatus:
      url: '/api/merchant/status'
      method: 'PUT'
  definitions:
    merchantInfo:
      state:
        merchantName: '奈雪的茶'
        merchantLogo: ''
        customerServiceType: 1
        customerServicePath: '/E1QDEFPQUJWB/s11gfxefx0j6/index?storeId=0a9295c8-8ce7-4b92-9d77-7ecdd186f346&serviceType=1'
        servicePhone: ''
        sceneType: 1
        sceneTag: ''
        sceneId: ''
        videoAccount: '格zZGegeir'
        sceneImages: []
        description: '爱马哥，提供美味可口的品质饮品和轻食'
        status: 'active'
        createTime: '2023-07-01T12:00:00Z'
        updateTime: '2023-08-15T09:30:00Z'
    statusOperation:
      state:
        loading: false
        currentStatus: 'active'
        confirmVisible: false
    errors:
      state: {}
  interactions:
    getMerchantInfo:
      steps:
        - stage: prepare
          from:
            - path: 'merchant-view/:merchantId'
              as: merchantId
        - stage: process
          api: '#/apis/getMerchantInfo'
          loading: '#/global/loading'
          condition:
            op: 'exists'
            path: 'merchantId'
        - stage: error
          handlers:
            - case: code
              not: 0
              message: "获取商家信息失败"
              error: "#/global/error"
        - stage: handle
          states:
            - '#/definitions/merchantInfo/state': '$StepData$.data'
            - '#/definitions/statusOperation/state.currentStatus': '$StepData$.data.status'
    
    toggleMerchantStatus:
      steps:
        - stage: prepare
          from:
            - state: '#/definitions/merchantInfo/state'
              as: merchantInfo
            - state: '#/definitions/statusOperation/state'
              as: statusOperation
        - stage: render
          renderType: v-dialog
          props:
            header: '确认操作'
            confirmBtn: 确认
            cancelBtn: 取消
            onConfirm:
              - states:
                  '#/definitions/statusOperation/state.loading': true
              - interaction: '#/interactions/updateMerchantStatus'
                slot: 'dialogContainer'
          children:
            - renderType: p
              children:
                - '确定要${statusOperation.currentStatus === "active" ? "停用" : "启用"}该商家吗？'
    
    updateMerchantStatus:
      steps:
        - stage: prepare
          from:
            - state: '#/definitions/merchantInfo/state'
              as: merchantInfo
            - state: '#/definitions/statusOperation/state'
              as: statusOperation
          handler:
            from: userProvide
            path: utils.prepareStatusUpdate
        - stage: process
          api: '#/apis/updateMerchantStatus'
          loading: '#/global/loading'
        - stage: error
          handlers:
            - case: code
              not: 0
              message: "更新商家状态失败"
              error: "#/global/error"
        - stage: handle
          states:
            - '#/definitions/merchantInfo/state.status': '$StepData$.data.status'
            - '#/definitions/statusOperation/state':
                loading: false
                confirmVisible: false
                currentStatus: '$StepData$.data.status'
        - stage: render
          renderType: v-dialog
          props:
            header: '操作成功'
            confirmBtn: 确定
            cancelBtn: null
            onConfirm:
              - close: true
          children:
            - renderType: p
              children:
                - '商家状态已成功更新'
    
    renderMerchantInfo:
      steps:
        - stage: render
          from:
            - state: '#/definitions/merchantInfo/state'
              as: merchantInfo
          renderType: div
          props:
            class: 'merchant-view-container'
          children:
            # 商家头像和名称区域
            - renderType: div
              props:
                class: 'merchant-card'
              children:
                - renderType: div
                  props:
                    class: 'merchant-header'
                  children:
                    - renderType: img
                      props:
                        class: 'merchant-avatar'
                        src: '${merchantInfo.merchantLogo || "/default-avatar.png"}'
                        alt: '${merchantInfo.merchantName}'
                    - renderType: div
                      props:
                        class: 'merchant-info'
                      children:
                        - renderType: h2
                          props:
                            class: 'merchant-name'
                          children:
                            - '${merchantInfo.merchantName}'
                        - renderType: div
                          props:
                            style:
                              display: 'flex'
                              gap: '16px'
                              margin-top: '8px'
                          children:
                            - renderType: v-tag
                              props:
                                theme: ${merchantInfo.status === 'active' ? 'success' : 'warning'}
                                variant: 'light'
                              children:
                                - '${merchantInfo.status === "active" ? "已启用" : merchantInfo.status === "inactive" ? "已停用" : "审核中"}'
                            - renderType: v-tag
                              props:
                                theme: 'primary'
                                variant: 'light'
                              children:
                                - '创建时间: ${merchantInfo.createTime}'
            
            # 商家基本信息区域
            - renderType: div
              props:
                class: 'merchant-card'
              children:
                - renderType: h3
                  props:
                    class: 'info-title'
                  children:
                    - '基本信息'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '商家ID'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.merchantId || "-"}'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '更新时间'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.updateTime || "-"}'
            
            # 商家客服信息区域
            - renderType: div
              props:
                class: 'merchant-card'
              children:
                - renderType: h3
                  props:
                    class: 'info-title'
                  children:
                    - '商家客服'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '客服类型'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.customerServiceType === 1 ? "小程序客服" : merchantInfo.customerServiceType === 2 ? "企业微信客服" : "自建客服"}'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '客服路径'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.customerServicePath || "-"}'
                - renderType: div
                  props:
                    class: 'info-row'
                  when:
                    - exists: merchantInfo.servicePhone
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '客服电话'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.servicePhone}'
            
            # 场景跳转信息区域
            - renderType: div
              props:
                class: 'merchant-card'
              children:
                - renderType: h3
                  props:
                    class: 'info-title'
                  children:
                    - '场景跳转'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '场景类型'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.sceneType === 1 ? "小程序" : "视频号"}'
                - renderType: div
                  props:
                    class: 'info-row'
                  when:
                    - exists: merchantInfo.videoAccount
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '视频号'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.videoAccount}'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '场景标签'
                    - renderType: span
                      props:
                        class: 'info-value'
                      children:
                        - '${merchantInfo.sceneTag || "-"}'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: span
                      props:
                        class: 'info-label'
                      children:
                        - '场景展示图片'
                    - renderType: div
                      when:
                        - op: gt
                          path: merchantInfo.sceneImages.length
                          value: 0
                      props:
                        class: 'image-gallery'
                      children:
                        - renderType: v-image-list
                          props:
                            images: '${merchantInfo.sceneImages}'
                            class: 'gallery-image'
            
            # 名片简介区域
            - renderType: div
              props:
                class: 'merchant-card'
              children:
                - renderType: h3
                  props:
                    class: 'info-title'
                  children:
                    - '名片简介'
                - renderType: div
                  props:
                    class: 'info-row'
                  children:
                    - renderType: p
                      props:
                        style:
                          margin: '0'
                          line-height: '1.6'
                      children:
                        - '${merchantInfo.description || "暂无简介"}'
    
    renderActionButtons:
      steps:
        - stage: render
          from:
            - state: '#/definitions/merchantInfo/state'
              as: merchantInfo
          renderType: div
          props:
            class: 'action-buttons'
            style:
              margin-top: '32px'
              padding: '16px 24px'
              background-color: '#fff'
              border-radius: '8px'
              box-shadow: '0 2px 8px rgba(0,0,0,0.05)'
          children:
            - renderType: v-button
              props:
                theme: primary
                content: 编辑商家信息
                events:
                  click:
                    - jump: '/home/<USER>/card-config/${merchantInfo.merchantId || ""}'
            - renderType: v-button
              props:
                theme: ${merchantInfo.status === 'active' ? 'warning' : 'success'}
                content: ${merchantInfo.status === 'active' ? '停用商家' : '启用商家'}
                style:
                  margin-left: '16px'
                events:
                  click:
                    - interaction: '#/interactions/toggleMerchantStatus'
                      slot: 'dialogContainer'
            - renderType: v-button
              props:
                theme: default
                content: 返回列表
                style:
                  margin-left: '16px'
                events:
                  click:
                    - jump: '/home/<USER>/card-detail'