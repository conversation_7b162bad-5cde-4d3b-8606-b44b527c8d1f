<template>
  <div>
    <div><slot name="dialogContainer"></slot></div>
    <div class="main-container">
      <div class="left-container">
        <slot name="left"></slot>
      </div>
      <div class="right-container">
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeftRightLayout',
};
</script>

<style scoped>
.main-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 600px;
}

.left-container {
  flex: 1;
  background-color: #ffffff;
  overflow: auto;
  padding: 32px;
  min-height: 640px;
}

.right-container {
  width: 320px;
  background-color: #f7f8fa;
  border-left: 1px solid #e7e7e7;
}
</style>
