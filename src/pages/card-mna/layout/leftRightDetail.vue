<template>
  <div>
    <div><slot name="dialogContainer"></slot></div>
    <div class="main-container">
      <div class="left-container">
        <slot name="left"></slot>
      </div>
      <div class="right-container">
        <slot name="right"></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LeftRightLayout',
};
</script>

<style scoped>
.main-container {
  display: flex;
  width: 100%;
  height: 100%;
  min-height: 600px;
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.04);
  background: #fff;
  width: 1224px;
  min-width: 960px;
}

.left-container {
  flex: 1;
  overflow: auto;
  padding: 32px;
  min-height: 640px;
}

.right-container {
  padding-top: 32px;
  padding-right: 32px;
  padding-bottom: 32px;
  padding-left: 0;
}
</style>
