<template>
  <div><slot name="dialogContainer"></slot></div>
  <div class="main">
    <slot name="top1"></slot>
    <slot name="top2"></slot>
    <slot name="top3"></slot>
    <slot name="top4"></slot>
  </div>
  <div
    style="
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
    "
  >
    <slot name="messageContainer"></slot>
  </div>
</template>
<!-- 
这里是纯布局文件
这里不能有<script>
-->
<style>
.global-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}
/* .main {
  width: calc(100vw - 288px - 2rem);
} */
.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.text-primary {
  color: #0052d9;
}
.text-secondary {
  color: #666;
}
.text-danger {
  color: E34D59;
}
.auto_height {
  display: none;
}
.auto_height:has(form) {
  display: block;
}
.form-item-error {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 12px;
}

.form-container {
  background-color: #fff;
  padding: 32px 0;
}
.form-item-description {
  margin-top: 12px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.3);
}

.form-item-row {
  display: flex;
  align-items: flex-start;
  margin: 32px 0;
}
.form-label {
  width: 120px;
  color: #333;
  font-size: 14px;
  text-align: left;
  line-height: 32px;
}
.form-content {
  flex: 1;
}
.form-note {
  font-size: 12px;
  color: #999;
  margin-top: 8px;
}
.upload-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
}
.action-buttons {
  background-color: #fff;
  /* margin-top: 24px; */
  width: 1224px;
  border-radius: 16px;
  padding: 0 16px 16px;
  display: flex;
  justify-content: flex-start;
}

.action-buttons button {
  margin-left: 8px;
}
</style>
