{"schema": {"globalStyle": {"formtitle": {"background-color": "#fff", "padding": "16px"}, "cardSection": {"margin-top": "20px", "background-color": "#fff", "border-radius": "8px", "padding": "24px"}, "configSection": {"background-color": "#fff", "border-radius": "8px", "padding": "24px", "margin-bottom": "24px"}}, "cssClasses": {"home-container": {"display": "flex", "flex-direction": "column", "width": "calc(100% - 2rem)", "padding": "32px", "gap": "24px", "background-color": "#fff", "border-radius": "16px", "border": "1px solid rgba(0, 0, 0, 0.04)", "overflow-y": "auto"}, "flex-between": {"display": "flex", "justify-content": "space-between", "align-items": "center", "gap": "24px"}, "config-card": {"flex": 1, "border-radius": "8px", "padding": "24px", "background-color": "rgba(0, 0, 0, 0.02)"}, "card-title": {"font-size": "16px", "font-weight": 500, "color": "#000", "margin-bottom": "12px"}, "card-description": {"font-size": "14px", "color": "rgba(0, 0, 0, 0.5)", "margin-bottom": "12px", "overflow": "hidden"}, "img-container": {"display": "flex", "flex-direction": "row", "justify-content": "center", "align-items": "center", "gap": "16px", "border-radius": "8px", "padding": "20px", "background-color": "rgba(0, 0, 0, 0.03)", "width": "100%"}, "status-tag": {"display": "inline-block", "font-size": "16px", "margin-left": "14px", "font-weight": 400}, "status-unconfigured": {"color": "rgba(0, 0, 0, 0.3);"}, "status-configured": {"color": "rgba(7, 193, 96, 1)"}, "button-group": {"display": "flex", "justify-content": "center", "gap": "12px", "margin-top": "24px"}}, "apis": {"GetBrandMemberAccessState": {"url": "/get-brand-member-access-state", "method": "get"}, "GetShakeCouponAccessState": {"url": "/get-shake-coupon-access-state", "method": "get"}}, "meta": {"icon": "shop", "title": "微信支付 品牌经营"}, "layout": "v-simplelayout", "main": {"interactions": [{"path": "#/interactions/home", "slot": "top1"}]}, "definitions": {"vipAndCouponConfig": {"state": {"canBrandAccess": false, "brandAccessStatusTipsText": "", "canCouponAccess": false, "couponAccessStatusTipsText": ""}}}, "interactions": {"home": {"steps": [{"stage": "render", "renderType": "div", "props": {"class": "home-container"}, "children": [{"interaction": "#/interactions/pageHeader"}, {"interaction": "#/interactions/config<PERSON><PERSON>r"}]}]}, "pageHeader": {"steps": [{"stage": "render", "renderType": "div", "props": {"class": "formtitle"}, "children": [{"renderType": "h2", "props": {"style": {"font-size": "20px", "font-weight": 500, "margin-bottom": "8px"}}, "children": ["品牌会员或品牌优惠"]}, {"renderType": "p", "props": {"style": {"color": "rgba(0, 0, 0, 0.5)", "font-size": "14px"}}, "children": ["配置商家名片，品牌会员和品牌优惠至少需要完成一项接入"]}]}]}, "configContainer": {"steps": [{"stage": "process", "loading": "#/global/loading", "apis": [["#/apis/GetBrandMemberAccessState", "#/apis/GetShakeCouponAccessState"]]}, {"stage": "handle", "handler": {"before": {"from": "userProvide", "path": "brandVip.initPageState"}}, "states": [{"#/definitions/vipAndCouponConfig/state": "$StepData$"}]}, {"stage": "render", "from": [{"state": "#/definitions/vipAndCouponConfig/state", "as": "vipAndCouponConfig"}], "renderType": "div", "props": null, "children": [{"renderType": "div", "props": {"class": "flex-between"}, "children": [{"renderType": "div", "props": {"class": "config-card"}, "children": [{"renderType": "div", "props": {"class": "flex-between"}, "children": [{"renderType": "h3", "props": {"class": "card-title"}, "children": ["品牌会员", {"renderType": "span", "props": {"class": "status-tag status-unconfigured"}, "condition": {"literal": "vipAndCouponConfig.canCouponAccess"}, "text": {"from": "userProvide", "text": "${vipAndCouponConfig.couponAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}, {"renderType": "span", "props": {"class": "status-tag status-configured"}, "condition": {"literal": "vipAndCouponConfig.canCouponAccess === false"}, "text": {"from": "userProvide", "text": "${vipAndCouponConfig.couponAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"class": "card-description"}, "children": ["打通品牌的会员体系，用户便捷开通、查询会员权益，支持用户购买付费会员"]}, {"renderType": "div", "props": {"class": "img-container"}, "children": [{"renderType": "div", "props": {"style": {"width": "calc(100% / 3 - 8px)"}}, "children": [{"renderType": "img", "props": {"src": "${asserts.img01}", "alt": "品牌会员", "style": {"height": "auto"}}}]}, {"renderType": "div", "props": {"style": {"width": "calc(100% / 3 - 8px)"}}, "children": [{"renderType": "img", "props": {"src": "${asserts.img02}", "alt": "品牌会员", "style": {"height": "auto"}}}]}, {"renderType": "div", "props": {"style": {"width": "calc(100% / 3 - 8px)"}}, "children": [{"renderType": "img", "props": {"src": "${asserts.img03}", "alt": "品牌会员", "style": {"height": "auto"}}}]}]}, {"renderType": "div", "props": {"class": "button-group"}, "children": [{"renderType": "v-button", "props": {"theme": "primary", "content": "接入指引", "events": {"click": [{"jump": "https://pay.weixin.qq.com/doc/brand/4015562403", "inapp": false, "target": "_blank"}]}}}]}]}, {"renderType": "div", "props": {"class": "config-card"}, "children": [{"renderType": "div", "props": {"class": "flex-between"}, "children": [{"renderType": "h3", "props": {"class": "card-title"}, "children": ["品牌优惠", {"renderType": "span", "condition": {"literal": "vipAndCouponConfig.canBrandAccess"}, "props": {"class": "status-tag status-unconfigured"}, "text": {"from": "userProvide", "text": "${vipAndCouponConfig.brandAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}, {"renderType": "span", "condition": {"literal": "vipAndCouponConfig.canBrandAccess === false"}, "props": {"class": "status-tag status-configured"}, "text": {"from": "userProvide", "text": "${vipAndCouponConfig.brandAccessStatusTipsText}", "path": "tutorial.renderStatusTag"}}]}]}, {"renderType": "p", "props": {"class": "card-description"}, "children": ["基于用户在微信支付的消费偏好，向用户提供最短路径发现和使用优惠的能力"]}, {"renderType": "div", "props": {"class": "img-container"}, "children": [{"renderType": "div", "props": {"style": {"width": "calc(100% / 3 - 8px)"}}, "children": [{"renderType": "img", "props": {"src": "${asserts.img04}", "alt": "品牌优惠", "style": {"height": "auto"}}}]}, {"renderType": "div", "props": {"style": {"width": "calc(100% / 3 - 8px)"}}, "children": [{"renderType": "img", "props": {"src": "${asserts.img05}", "alt": "品牌优惠", "style": {"height": "auto"}}}]}, {"renderType": "div", "props": {"style": {"width": "calc(100% / 3 - 8px)"}}, "children": [{"renderType": "img", "props": {"src": "${asserts.img06}", "alt": "品牌优惠", "style": {"height": "auto"}}}]}]}, {"renderType": "div", "props": {"class": "button-group"}, "children": [{"renderType": "v-button", "props": {"theme": "primary", "content": "接入指引", "events": {"click": [{"jump": "https://pay.weixin.qq.com/doc/v3/merchant/4013839283", "inapp": false, "target": "_blank"}]}}}]}]}]}]}]}}}}