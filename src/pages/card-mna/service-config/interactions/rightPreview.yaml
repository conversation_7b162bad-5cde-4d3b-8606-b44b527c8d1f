steps:
  - stage: render
    # 右侧名片预览区
    renderType: v-businesscard
    from:
      - state: '#/definitions/cardDetail/state'
        as: cardDetail
      - state: '#/definitions/cardServiceData/state'
        as: cardServiceData
    reactive:
      '#/definitions/cardServiceData/state':
        keys:
          - data
          - serviceList
    props:
      class: 'right-panel'
      style:
        height: '100%'
        background-color: '#f7f8fa'
        border-left: '1px solid #E7E7E7'
        width: '320px'
      data: ${cardServiceData}
