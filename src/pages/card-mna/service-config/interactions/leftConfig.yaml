steps:
  - stage: render
    renderType: div
    from:
      - state: '#/definitions/pageStatus/state'
        as: pageStatus
    props:
      id: 'left-config'
      style:
        display: 'flex'
        flex-direction: 'column'
        height: '100%'
    children:
      - renderType: v-cardservice
        from:
          - state: '#/definitions/cardServiceData/state'
            as: cardServiceData
        props:
          style:
            padding: '20px'
            backgroundColor: '#FFFFFF'
            borderRadius: '8px'
          serviceData: '${cardServiceData}'
          events:
            update:serviceData:
              - state:
                  '#/definitions/cardServiceData/state':
                    serviceList: '$EventData$.serviceList'
                    data: '$EventData$.data'
              - interaction: '#/interactions/rightPreview'
                slot: right
