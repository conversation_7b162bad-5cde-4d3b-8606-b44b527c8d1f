schema:
  globalStyle:
    formtitle:
      # background-color: '#fff'
      padding: 16px
    section:
      margin-bottom: 24px
      background-color: '#fff'
      border-radius: 8px
      padding: 24px
    section-title:
      font-size: 16px
      font-weight: bold
      margin-bottom: 16px
  cssClasses:
    home-container:
      max-width: 1200px
      width: 1224px
      height: 780px
  layout: v-simplelayout
  meta:
    icon: card
    title: 微信支付 品牌经营
  main:
    interactions:
      - path: '#/interactions/renderCardConfigForm'
        slot: top1
  apis:
  definitions:
    errors:
      state: {}
  interactions:
    renderCardConfigForm:
      steps:
        - stage: render
          from:
          renderType: div
          props:
            class: home-container
          children:
            - renderType: v-businesscard
              props:
                data:
                  showQRCode: right
                  page: card-publish
