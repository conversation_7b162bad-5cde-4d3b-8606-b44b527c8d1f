# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此代码仓库中工作时提供指导。

## 项目概述

这是一个用于微信支付商户卡管理的 Vue 3 + TypeScript 应用 (mmpaymchcardinfomgrhtml)。它采用了独特的混合架构，结合了 DSL 驱动的页面和原生 Vue 组件，基于腾讯 XPage 框架构建，具有全面的测试和文档标准。

## 开发命令

### 核心命令
```bash
# 带热重载的开发服务器
npm run dev

# 生产构建
npm run build:prod

# 测试构建
npm run build:test

# 代码格式化
npm run lint

# 测试
npm test                    # 运行所有测试
npm run test:run            # 运行一次测试
npm run test:coverage       # 带覆盖率的测试
npm run test:ui             # 带UI的测试
npm run test:watch          # 监控模式测试
```

### 特定测试执行
```bash
# 运行特定测试文件
npm run test:run -- src/services/__tests__/cardHome.test.ts
npm run test:run -- src/stores/__tests__/cardHome.test.ts
```

## 架构概览

### 核心技术
- **框架**: Vue 3 with Composition API
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **UI 库**: TDesign Vue Next
- **构建工具**: Vite with custom YAML processing
- **测试**: Vitest with jsdom
- **Mock**: vite-plugin-mock (always enabled)

### 腾讯特定技术
- **@tencent/dsl-page**: 用于模式驱动页面开发的DSL框架
- **@tencent/xpage-web-sdk**: 内部SDK (request, lodash-es, dayjs 工具集)
- **@tencent/xpage-mch-components**: 商户特定组件
- **@tencent/rod-vue-components**: 额外的组件库

### 项目结构
```
src/
├── main.ts                    # 应用程序入口点
├── app.vue                    # 根 Vue 组件
├── assets/                    # 静态资产 (字体、图像、样式)
├── components/                # 可复用组件
│   ├── BusinessCard/         # 商务卡功能
│   ├── CardService/          # 卡服务管理
│   ├── layouts/              # 布局组件
│   └── ...                   # 其他共享组件
├── helpers/                   # 工具和常量
├── models/                    # 业务逻辑层 (OOP)
│   ├── app-model.ts          # 全局应用模型
│   ├── common-models/        # 共享模型
│   └── page-models/          # 页面特定模型
├── pages/                     # 页面组件
│   ├── card-link/            # 卡关联功能
│   ├── card-mna/             # 卡管理主要区域
│   │   ├── card-home-vue/    # 卡首页 (Vue 3)
│   │   ├── card-detail-vue/  # 卡详情页 (Vue 3)
│   │   └── ...               # 其他卡管理页面
│   └── ...
├── router/                    # Vue Router 配置
├── services/                  # API 服务和数据获取
├── stores/                    # Pinia 状态管理存储
└── utils/                     # 工具函数
```

### 核心架构模式

#### 混合 DSL-Vue 架构
项目采用独特的**双重方法**进行页面开发：

**DSL 驱动页面**: 大多数页面使用模式驱动开发，其中：
- YAML 文件定义页面结构、交互和数据流
- 自定义 Vite 插件 (`vite-plugin-yaml-to-json.js`) 将 YAML 转换为 JSON 并跟踪依赖关系
- 来自 `@tencent/dsl-page` 的 `DynamicPage` 组件基于模式渲染页面
- 在 YAML 配置中定义业务逻辑，包含 API 映射

**原生 Vue 组件**: 性能关键页面 (如 `card-home-vue`) 使用传统 Vue：
- 更好的开发体验和调试
- 更复杂的交互和自定义逻辑
- 从 DSL 到原生组件的渐进迁移路径

#### Model-View-Store 架构
- **Models** (`src/models/`): 遵循 OOP 原则的业务逻辑
- **Views** (`src/pages/`): 混合 DSL 模式和 Vue 组件
- **Stores** (`src/stores/`): 用于状态管理的 Pinia 存储

#### 模式驱动开发
- 每个 DSL 页面都有 `schema.yaml` 和自动生成的 `schema.json` 文件
- YAML 文件支持 `!include` 指令以实现模块化
- 实时 YAML 到 JSON 转换，支持热模块替换
- 多文件配置的复杂模式合并逻辑

### XPage 框架集成
- 使用 `@tencent/xpage-web-sdk` 提供内置工具 (request, dayjs, lodash-es)
- 在 `main.ts` 中自定义组件注册系统
- TDesign 组件以 `v-` 前缀约定注册
- 路由器动态导入并合并 JSON 模式
- SVG 组件通过 `unplugin-svg-vue-component` 自动注册

### Mock 开发
- 开发环境使用 `vite-plugin-mock` 进行 API 模拟 (始终启用)
- TypeScript Mock 文件位于 `mock/` 目录
- Mock 服务器提供无缝开发体验
- 在 mock 和真实 API 之间轻松切换

## 测试策略

### 测试结构
```
src/
├── services/__tests__/       # 服务层测试
├── stores/__tests__/         # Store 测试
└── pages/card-mna/**/__tests__/ # 页面组件测试
```

### 测试最佳实践
- 遵循 TDD 方法：在实现之前编写测试
- 使用 Vitest 和 Vue Test Utils 进行组件测试
- 测试文件与实现代码共同位置 (`__tests__/` 目录)
- 通过 `npm run test:coverage` 获取覆盖率报告
- 基于 `specs/` 中的 GHERKIN 规范的 BDD 风格测试

## 开发工作流

### 文档驱动开发 (DDD)
该项目遵循全面的文档驱动方法，并采用优化的工作流：

#### 文档结构
所有项目文档位于 `specs` 目录中：

**根级文档**：
- `specs/project.md` - 项目概述和背景，模块内容
- `specs/roadmap.md` - 项目规划路线图和当前进度
- `specs/test.md` - 项目测试规范

**模块文档** (`specs/modules/{feature}/`)：
- `specs/modules/{feature}/requirements.md` - EARS 语法需求规范 (由 `requirements-analyst-cn` Agent 生成)
- `specs/modules/{feature}/acceptance.md` - Gherkin 语法验收测试用例 (由 `acceptance-cn` Agent 生成，用于 TDD 测试用例生成)
- `specs/modules/{feature}/design.md` - 模块设计文档 (由 `desgin-cn` Agent 生成，包含 Mermaid 图表)
- `specs/modules/{feature}/todo.md` - 实现任务清单 (由 `implement-cn` Agent 生成)

#### 基于专用Agent的优化开发工作流

本项目采用专用Agent系统来处理不同类型的任务，确保每个阶段都有专门的专家处理：

**1. 需求分析阶段** - 使用 `requirements-analyst-cn` Agent
- 阅读 `specs/project.md` 和 `specs/roadmap.md` 来理解项目概述和当前进度
- 根据用户需求确定哪些模块需要修改
- **优化**: 重点关注在 `specs/roadmap.md` 中标记为 🚧 **进行中** 的模块
- 使用专用Agent执行：
  ```bash
  # 启动需求收集专家
  /requirements-analyst "为{功能名称}创建需求文档"
  ```
- Agent将：
  - 创建/更新 `specs/modules/{feature}/requirements.md` 文件
  - 使用EARS语法格式化需求规范
  - 生成用户故事和验收标准
  - 与用户迭代完善需求直到获得明确批准

**2. 验收测试用例阶段** - 使用 `acceptance-cn` Agent
- 在需求获得批准后，基于需求创建验收测试用例
- 使用专用Agent执行：
  ```bash
  # 启动验收测试用例生成专家
  /acceptance "基于需求为{功能名称}创建验收测试用例"
  ```
- Agent将：
  - 创建/更新 `specs/modules/{feature}/acceptance.md` 文件
  - 基于requirements.md中的EARS格式需求创建Gherkin格式测试用例
  - 确保测试覆盖所有需求场景（正常流程、异常流程、边界条件等）
  - 包含BDD风格的测试场景，支持TDD开发
  - 与用户迭代完善测试用例直到获得明确批准

**3. 设计阶段** - 使用 `desgin-cn` Agent  
- 在需求获得批准后，启动设计文档创建（可与验收测试用例阶段并行）
- 使用专用Agent执行：
  ```bash
  # 启动设计文档专家
  /desgin "基于需求创建{功能名称}的设计文档"
  ```
- Agent将：
  - 创建/更新 `specs/modules/{feature}/design.md` 文件
  - 进行必要的技术研究和架构调研
  - 包含完整的设计文档结构（概述、架构、组件接口、数据模型、错误处理、测试策略）
  - 使用Mermaid图表进行可视化设计
  - 考虑验收测试用例中的测试要求
  - 与用户迭代完善设计直到获得明确批准

**4. 实现计划阶段** - 使用 `implement-cn` Agent
- 在设计和验收测试用例都获得批准后，创建可执行的实现计划
- 使用专用Agent执行：
  ```bash
  # 启动实现计划专家
  /implement "为{功能名称}创建详细的实现任务清单"
  ```
- Agent将：
  - 创建/更新 `specs/modules/{feature}/todo.md` 文件
  - 将设计转换为具体的编码任务
  - 创建编号复选框格式的任务清单
  - 确保每个任务都是可操作的编码任务
  - 建立任务间的依赖关系和执行顺序
  - 考虑验收测试用例的实现要求
  - 与用户迭代完善任务清单直到获得明确批准

**5. 代码实现阶段** - 使用通用Agent或手动执行
- 按照 `todo.md` 中的任务清单逐项实现
- **优化**: 直接检查当前进行中模块的 `specs/modules/{module-name}/todo.md` 文件以识别 `[ ]` 未完成任务
- 遵循设计文档进行编码
- 保持代码风格的一致性和可读性
- **优化**: 使用 TDD 方法，首先找到相关测试文件 (如 `src/services/__tests__/{module-name}.test.ts` 或 `src/stores/__tests__/{module-name}.test.ts`)，运行测试以确定当前功能状态

**6. 验收测试阶段**
- 根据 `acceptance.md` 中的 Gherkin 案例执行验收测试
- 确保所有测试通过
- 执行代码审查以确保质量符合要求
- **优化**: 使用命令 `npm run test:run -- {test-file-path}` 运行特定测试，而非所有测试

**7. 文档更新阶段**
- 完成编码后，更新 `todo.md` 标记已完成的任务
- 更新 `roadmap.md` 以反映最新的项目进度
- 确保所有文档与当前实现保持一致

#### 基于专用Agent的工作流图表

```mermaid
flowchart TD
    Start[用户需求] --> A[需求评估]
    A --> B[阅读 project.md 和 roadmap.md]
    B --> B1[查找标记为 🚧 进行中的模块]
    B1 --> C[确定相关模块]
    
    C --> C1[启动 requirements-analyst-cn Agent]
    C1 --> D[创建/更新 requirements.md]
    D --> D1[使用 EARS 语法生成需求]
    D1 --> D2[与用户迭代完善需求]
    D2 --> D3{需求获得批准?}
    
    D3 -->|否| D1
    D3 -->|是| E[启动 acceptance-cn Agent]
    
    E --> E1[创建/更新 acceptance.md]
    E1 --> E2[基于需求生成Gherkin测试用例]
    E2 --> E3[覆盖正常流程/异常流程/边界条件]
    E3 --> E4[与用户迭代完善测试用例]
    E4 --> E5{验收测试用例获得批准?}
    
    E5 -->|否| E2
    E5 -->|是| F[启动 desgin-cn Agent]
    
    F --> F1[创建/更新 design.md]
    F1 --> F2[进行技术研究和架构设计]
    F2 --> F3[使用 Mermaid 创建设计图表]
    F3 --> F4[考虑验收测试要求]
    F4 --> F5[与用户迭代完善设计]
    F5 --> F6{设计获得批准?}
    
    F6 -->|否| F2
    F6 -->|是| G[启动 implement-cn Agent]
    
    G --> G1[创建/更新 todo.md]
    G1 --> G2[将设计转换为编码任务清单]
    G2 --> G3[创建编号复选框任务格式]
    G3 --> G4[考虑验收测试实现要求]
    G4 --> G5[与用户迭代完善任务清单]
    G5 --> G6{任务清单获得批准?}
    
    G6 -->|否| G2
    G6 -->|是| H[开始代码实现阶段]
    
    H --> H1[检查 todo.md 中的 [ ] 未完成任务]
    H1 --> H2{有未完成任务?}
    
    H2 -->|是| I[执行下一个任务]
    H2 -->|否| L[验收测试]
    
    I --> I1[查找相关测试文件]
    I1 --> I2[运行特定测试确定状态]
    I2 --> I3[按照 TDD 方式实现代码]
    I3 --> I4[标记任务为完成 ✓]
    I4 --> H1
    
    L --> L1[根据 acceptance.md 执行测试]
    L1 --> L2{所有测试通过?}
    L2 -->|否| I
    L2 -->|是| M[文档更新]
    
    M --> M1[更新 roadmap.md 项目进度]
    M1 --> M2[确保文档与实现一致]
    M2 --> N[完成]
```

### Agent使用指南

#### 可用的专用Agent

本项目配置了以下专用Agent，每个都专门处理特定类型的任务：

1. **requirements-analyst-cn** - 需求收集生成专家
   - **用途**: 创建和完善功能需求文档
   - **输出**: `specs/modules/{feature}/requirements.md`
   - **格式**: EARS语法需求规范和用户故事

2. **acceptance-cn** - 验收测试用例生成专家
   - **用途**: 基于需求创建验收测试用例
   - **输出**: `specs/modules/{feature}/acceptance.md`
   - **格式**: Gherkin语法的BDD测试用例，支持TDD开发

3. **desgin-cn** - 设计文档创建专家
   - **用途**: 基于需求创建技术设计文档
   - **输出**: `specs/modules/{feature}/design.md`
   - **格式**: 包含架构、组件、数据模型和Mermaid图表

4. **implement-cn** - 实现计划专家
   - **用途**: 将设计转换为可执行的编码任务清单
   - **输出**: `specs/modules/{feature}/todo.md`
   - **格式**: 编号复选框任务清单

#### Agent调用方式

```bash
# 需求阶段
/requirements-analyst "为用户登录功能创建需求文档"

# 验收测试用例阶段
/acceptance "基于需求为用户登录功能创建验收测试用例"

# 设计阶段
/desgin "基于需求为用户登录功能创建设计文档"

# 计划阶段  
/implement "为用户登录功能创建详细的实现任务清单"
```

#### Agent工作特点

**requirements-analyst-cn**特点：
- 自动创建`specs/modules/{feature}/requirements.md`
- 使用EARS语法格式化需求
- 必须获得用户明确批准才能进入下一阶段
- 支持迭代完善需求直到准确

**acceptance-cn**特点：
- 基于requirements.md创建验收测试用例
- 使用Gherkin语法格式化BDD测试场景
- 覆盖正常流程、异常流程、边界条件等测试场景
- 确保测试用例支持TDD开发方式
- 必须获得用户明确批准才能进入下一阶段

**desgin-cn**特点：
- 进行必要的技术研究和调研
- 创建完整的设计文档结构
- 使用Mermaid图表进行可视化
- 确保设计覆盖所有需求点
- 考虑验收测试用例的技术实现要求

**implement-cn**特点：
- 将设计转换为具体编码任务
- 创建有序的任务依赖关系
- 确保每个任务都是可操作的
- 专注于编码实现而非业务流程
- 考虑验收测试用例的实现要求

#### 优化工作流

为了提高效率并避免每次都读取所有文件，请遵循以下优先级顺序：

**1. 路线图分析**
- 阅读 `specs/roadmap.md` 查找标记为 🚧 **进行中** 的模块

**2. Agent驱动的文档创建**
- 根据模块状态使用专用Agent按顺序创建文档：
  - 如果缺少需求文档：使用 `/requirements-analyst` 
  - 如果缺少验收测试文档：使用 `/acceptance`
  - 如果缺少设计文档：使用 `/desgin`
  - 如果缺少任务计划：使用 `/implement`
- 每个阶段必须获得明确批准才能进入下一阶段

**3. 任务分析**
- 阅读对应模块的 `specs/modules/{module-name}/todo.md` 文件
- 识别标记为 `[ ]` 的任务（未完成）

**4. TDD 开发**
- 查找相关测试文件：
  - `src/services/__tests__/{module-name}.test.ts`
  - `src/stores/__tests__/{module-name}.test.ts`
  - `src/pages/card-mna/{module-name}-vue/__tests__/**/*.test.ts`
- 运行特定测试：`npm run test:run -- {test-file-path}`

**5. 代码实现**
- 根据测试结果实现或修复功能
- 验证实现是否通过测试

**6. 文档更新**
- 更新任务状态和相关文档

#### Agent最佳实践

1. **顺序执行**: 严格按照 需求 → 验收测试 → 设计 → 计划 的顺序使用Agent
2. **明确批准**: 每个阶段必须获得用户明确同意后才继续
3. **迭代完善**: 支持多轮迭代直到文档质量符合预期
4. **上下文维护**: 每个Agent都能访问前序阶段的文档作为上下文
5. **测试驱动**: 验收测试用例应在设计之前创建，确保设计满足测试要求
6. **文档质量优先**: 宁可多轮迭代也要确保文档的准确性和完整性
7. **任务明确性**: 在调用Agent时提供明确的功能描述和期望输出

#### EARS 需求格式

EARS (简易需求语法方法) 遵循以下模式：

- **普遍性**: `系统应当 <action>`
- **事件驱动**: `当 <trigger> 时，系统应当 <action>`
- **状态驱动**: `当系统处于 <state> 状态时，系统应当 <action>`
- **可选性**: `在 <condition> 条件下，系统应当 <action>`
- **不期望的**: `如果 <unwanted condition> 那么系统不应当 <action>`
- **复杂性**: `当 <trigger> 且系统处于 <state> 状态时，系统应当 <action>`

#### Gherkin 测试格式

Gherkin 测试用例遵循以下格式：

```gherkin
Feature: 功能名称

  Scenario: 场景描述
    Given 前置条件
    When 执行操作
    Then 预期结果
```

### 当前项目状态

**模块状态** (来自 roadmap.md)：
- **Card-Home**: ✅ 完成 (100% 测试覆盖率，TDD 实现)
- **Card-Detail**: 🚧 进行中 (30% 完成，架构已建立)
- **Dashboard, Card-Config 等**: 📋 已规划

### 最佳实践

#### 开发流程最佳实践
- 在深入细节之前始终理解全局
- 确保代码和设计文档之间的一致性
- 关注代码的可维护性和可扩展性
- 遵循现有的代码风格和架构模式
- 在开发过程中考虑边缘情况和错误处理
- 在尊重现有设计决策的同时主动建议改进

#### Agent使用最佳实践
- **严格按阶段使用Agent**: 遵循 需求收集 → 设计创建 → 计划制定 的顺序
- **确保明确批准**: 每个Agent完成工作后必须获得用户明确同意才能进入下一阶段
- **利用迭代完善**: 充分利用Agent的迭代能力，确保每个文档质量达标
- **保持上下文连贯**: 确保每个Agent都能访问前序阶段的文档作为参考
- **文档质量优先**: 宁可多轮迭代也要确保文档的准确性和完整性
- **任务明确性**: 在调用Agent时提供明确的功能描述和期望输出

### 代码风格和约定
- 文件名：小写加连字符 (kebab-case)
- 路径别名：`@/` 映射到 `src/`
- 组件命名：组件使用 PascalCase，文件使用 kebab-case
- 启用 TypeScript 严格模式
- 配置了 ESLint 和 Prettier，带有预提交钩子

## 构建和部署

### 构建配置
- 开发环境：`vite --host` (端口 8080)
- 生产环境：`vite build --mode prod`
- 输出目录：`build/`
- 启用源映射用于调试
- 生产环境禁用代码压缩以便调试
- 自定义 YAML 到 JSON 插件，带依赖追踪

### 环境配置
- 基础路径由 `NODE_ENV` 和 `CDN_URL` 决定
- 开发环境中 Mock 服务器始终启用 (`enable: true`)
- 实时 YAML 到 JSON 转换，支持热重载
- 通过 `rollup-plugin-visualizer` 进行包分析

## 特殊考虑

### 微信支付集成
- 业务领域：支付商户管理
- 与微信支付 API 集成
- 卡片管理和配置功能

### 性能优化
- 通过 `rollup-plugin-visualizer` 进行包分析
- 路由和组件的懒加载
- 带 CDN 支持的资源优化

### 开发体验
- 代码和 YAML 模式的热模块替换
- 严格模式下的全面 TypeScript 支持
- 通过 Husky 和 lint-staged 的 ESLint 预提交钩子
- SVG 组件自动注册为 Vue 组件
- 实时模式验证和 YAML 到 JSON 转换

## DSL 与 Vue 组件的使用

### 何时使用 DSL 页面
- 具有标准布局的简单 CRUD 页面
- 具有最少自定义交互的页面
- 快速原型开发和配置驱动的功能
- 当业务逻辑可以用 YAML 表达时

### 何时使用原生 Vue 组件
- 复杂的用户交互或自定义动画
- 需要优化的性能关键页面
- 需要广泛 TypeScript 集成的功能
- 需要高级测试场景的页面

### 迁移策略
- DSL 页面可以逐步迁移到 Vue 组件
- 共享业务逻辑应保留在 `src/models/` 中
- `src/services/` 中的 API 服务适用于两种方法
- 测试策略不同：模式验证 vs 组件测试