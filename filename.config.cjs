module.exports = {
  // 文件名命名规范: 'kebab-case' | 'PascalCase' | 'camelCase'
  filenameCase: 'kebab-case',

  // 目录名命名规范: 'kebab-case' | 'PascalCase' | 'camelCase'
  directoryCase: 'kebab-case',

  // 需要检查的文件扩展名
  checkExtensions: ['.vue', '.ts', '.js', '.tsx', '.jsx'],

  // 排除检查的目录
  excludeDirs: [
    'node_modules',
    '.git',
    '.husky',
    'dist',
    'build',
    '.vscode',
    '.in-context'
  ],

  // 排除检查的文件（这些文件通常有特定的命名要求）
  excludeFiles: [
    'main.ts',
    'app.vue',
    'index.ts',
    'index.js',
    'vite.config.js',
    'tailwind.config.js',
    'postcss.config.js',
    'babel.config.js',
    '.eslintrc.cjs',
    '.prettierrc'
  ]
};