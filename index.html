<!doctype html>
<html lang="" data-id="3062041692779304" data-mode="">
  <head>
    <!--XMONITOR-PLACEHOLDER-START-->
    <!-- htmlmin:ignore -->
    <!--#include virtual="/sinclude/xdc/core/payapp-header.shtml" -->
    <!-- htmlmin:ignore -->
    <!--XMONITOR-PLACEHOLDER-END-->
    <% if (isDev) { %>
    <!-- 以下两个文件用于开发版使用，注入 mock 后的布局数据，线上版本需要替换为 sinclude -->
    <link
      rel="stylesheet"
      crossorigin
      href="https://xres.wxp.woa.com/resource/xres/build/td/wxpay/mch_open_app/brand-layout-component/latest/assets/style.css"
    />
    <script
      type="module"
      crossorigin
      src="https://xres.wxp.woa.com/resource/xres/build/td/wxpay/mch_open_app/brand-layout-component/latest/assets/brand-layout-dev.js"
    ></script>
    <% } else { %>
    <!-- htmlmin:ignore -->
    <!--#include virtual="/sinclude/jsi/brand/brand-layout.html" -->
    <!-- htmlmin:ignore -->
    <% } %>
    <meta charset="UTF-8" />
    <link rel="icon" href="https://wx.gtimg.com/core/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>微信支付 品牌经营</title>
    <style>
      html,
      body,
      #app-root {
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
      }

      #app-root {
        /* 有 16 的 padding 40-16 */
        margin-top: 24px;
        min-height: calc(100vh - 520px);
      }

      /* 减少品牌布局的边距 */
      #brand-site {
        padding: 0 !important;
        margin: 0 !important;
      }

      /* 调整主容器的边距 */
      .main {
        padding-left: 0 !important;
        margin-left: 0 !important;
      }
      .side-footer {
        width: 1200px !important;
        padding-right: 8px !important;
        margin-top: 8px !important;
      }
    </style>
  </head>

  <body>
    <div id="brand-site">
      <div id="app-root"></div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
