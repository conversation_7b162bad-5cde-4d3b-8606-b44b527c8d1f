import fs from 'fs';

checkBlankFile('./build');

function checkBlankFile(pathStr) {
  const pa = fs.readdirSync(pathStr);
  console.log(pa);
  pa.forEach(function (ele, index) {
    const info = fs.statSync(`${pathStr}/${ele}`);
    if (info.isDirectory()) {
      const dir = fs.readdirSync(`${pathStr}/${ele}`); // 读目录
      if (!dir.length) {
        fs.rmdirSync(dir);
      }
      checkBlankFile(`${pathStr}/${ele}`);
    } else {
      const data = fs.readFileSync(`${pathStr}/${ele}`);
      if (!data.length) {
        // 删除空文件
        fs.unlinkSync(`${pathStr}/${ele}`);
        console.log(`--------- 删除文件${pathStr}/${ele}成功 ---------`);
      }
    }
  });
}
