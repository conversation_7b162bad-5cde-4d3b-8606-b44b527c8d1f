{"name": "mmpaymchcardinfomgrhtml", "description": "", "version": "0.0.0", "type": "module", "private": true, "scripts": {"start": "vite --host", "dev": "NODE_ENV=development vite --host", "serve": "vite", "build:prod": "vite build --mode prod", "build:test": "BUILD_TEST=true vite build", "build": "vite build", "lint": "prettier --write 'src/**/*.{vue,ts}'", "prepare": "husky", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "test:watch": "vitest --watch"}, "author": "", "keywords": ["template", "javascript", "typescript", "vue 3.x", "vite", "tailwind", "css"], "dependencies": {"@tencent/dsl-page": "^1.0.2-alpha.95", "@tencent/rod-vue-components": "^0.0.14", "@tencent/xpage-mch-components": "^0.0.1-alpha.64", "@tencent/xpage-web-sdk": "^0.0.6", "core-js": "^3.6.5", "pinia": "^3.0.3", "qrcode": "^1.5.4", "tdesign-icons-vue-next": "^0.3.5", "tdesign-vue-next": "^1.11.5", "vue": "^3.5.3", "vue-router": "^4.3.2"}, "devDependencies": {"@tencent/eslint-config-tencent": "^1.0.4", "@types/node": "^20.12.12", "@typescript-eslint/eslint-plugin": "^7.10.0", "@typescript-eslint/parser": "^7.10.0", "@vitejs/plugin-vue": "^5.2.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.4.27", "@vue/eslint-config-typescript": "^13.0.0", "@vue/runtime-dom": "^3.2.29", "@vue/test-utils": "^2.4.6", "autoprefixer": "^10.4.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.25.0", "husky": "^9.0.11", "js-yaml": "^4.1.0", "jsdom": "^27.0.0-beta.0", "less": "^4.2.0", "lint-staged": "^15.0.0", "postcss": "^8.4.12", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "rollup-plugin-visualizer": "^5.6.0", "sass-embedded": "^1.89.2", "tailwindcss": "^3.4.0", "typescript": "^5.4.5", "typescript-eslint": "^7.8.0", "unplugin-svg-vue-component": "^0.1.1", "vite": "^5.2.0", "vite-plugin-babel": "^1.1.3", "vite-plugin-eslint": "^1.8.1", "vite-plugin-externals": "^0.6.2", "vite-plugin-html": "^3.2.0", "vite-plugin-mock": "^3.0.2", "vitest": "^3.2.4", "vue-eslint-parser": "^9.4.2", "vue-tsc": "^2.0.19"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "*"}, "engines": {"node": ">=18.18.0"}, "lint-staged": {"**/**.{js,vue,ts}": ["eslint --fix --ignore-path .gitignore"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "mmpaymoduleattr": {"xuc_sys_id": "3", "xuc_domain_id": "146", "xmonitor_id": "3062041692779304", "platform": "xpage", "module_type": "html", "program_lang": "ts", "framework": "vue3", "biz_oriented": "paymerchant", "is_oversea": false, "scaffold_name": "xpage初始化模板-pay", "scaffold_link": "***************:xdev/xpage-pro-tools.git", "module_id": 1504}}