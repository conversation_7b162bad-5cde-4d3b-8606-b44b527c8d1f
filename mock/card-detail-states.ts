import { MockMethod } from 'vite-plugin-mock';

// 直接定义baseUrl，避免导入src文件造成依赖问题
const baseUrl = '/xdc/mchcardinfomgrweb';

// 进程状态枚举
enum ProcessState {
  INIT = 0, // 初始状态
  DRAFT = 1, // 草稿
  AUDITING = 2, // 待审核
  AUDIT_PASS = 3, // 审核通过
  REJECTED = 4, // 审核驳回
  CANCELED = 5, // 已作废
  APPROVED = 6, // 待发布
  PUBLISHING = 7, // 发布中
  PUBLISHED = 8, // 已完成
}

// 接入状态枚举
enum AccessState {
  NONE = 0,
  OPEN = 1,
  CLOSE = 2,
}

/**
 * 不同测试场景的状态配置
 * 通过URL参数 ?scenario=xxx 来切换不同的mock状态
 */
const scenarios = {
  // 场景1: 新手用户 - 所有配置都未开始
  'new-user': {
    cardInfoState: ProcessState.INIT,
    serviceState: ProcessState.INIT,
    brandMemberState: AccessState.NONE,
    shakeCouponState: AccessState.NONE,
    description: '新手用户，所有配置项都未配置',
  },

  // 场景2: 部分配置 - 基础信息已配置，其他未完成
  'partial-configured': {
    cardInfoState: ProcessState.AUDIT_PASS,
    serviceState: ProcessState.DRAFT,
    brandMemberState: AccessState.NONE,
    shakeCouponState: AccessState.NONE,
    description: '基础信息已配置，服务配置中，品牌未接入',
  },

  // 场景3: 审核中 - 基础信息正在审核
  auditing: {
    cardInfoState: ProcessState.AUDITING,
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.OPEN,
    shakeCouponState: AccessState.NONE,
    description: '基础信息审核中',
  },

  // 场景4: 审核驳回 - 基础信息被驳回
  rejected: {
    cardInfoState: ProcessState.REJECTED,
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.OPEN,
    shakeCouponState: AccessState.NONE,
    description: '基础信息审核驳回',
  },

  // 场景5: 准备发布 - 所有配置完成，可以发布
  'ready-to-publish': {
    cardInfoState: ProcessState.AUDIT_PASS,
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.OPEN,
    shakeCouponState: AccessState.NONE,
    description: '所有配置完成，可以发布',
  },

  // 场景6: 优惠券接入方式 - 通过优惠券而非品牌会员
  'coupon-access': {
    cardInfoState: ProcessState.AUDIT_PASS,
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.NONE,
    shakeCouponState: AccessState.OPEN,
    description: '通过优惠券接入方式',
  },

  // 场景7: 待发布状态
  approved: {
    cardInfoState: ProcessState.APPROVED,
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.OPEN,
    shakeCouponState: AccessState.NONE,
    description: '审核通过，待发布',
  },

  // 场景8: API错误测试
  'api-error': {
    cardInfoState: 'ERROR', // 特殊标记，表示API调用失败
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.OPEN,
    shakeCouponState: AccessState.NONE,
    description: 'API调用失败',
  },

  // 场景9: 全部草稿状态
  'all-draft': {
    cardInfoState: ProcessState.DRAFT,
    serviceState: ProcessState.DRAFT,
    brandMemberState: AccessState.NONE,
    shakeCouponState: AccessState.NONE,
    description: '所有配置都是草稿状态',
  },

  // 场景10: 全部审核通过，品牌会员和优惠券都接入
  'both-access': {
    cardInfoState: ProcessState.AUDIT_PASS,
    serviceState: ProcessState.AUDIT_PASS,
    brandMemberState: AccessState.OPEN,
    shakeCouponState: AccessState.OPEN,
    description: '品牌会员和优惠券都已接入',
  },
};

// 获取当前场景配置
const getCurrentScenario = (url: string) => {
  const urlObj = new URL(url, 'http://localhost');
  const scenario = urlObj.searchParams.get('scenario') || 'new-user';
  return scenarios[scenario as keyof typeof scenarios] || scenarios['new-user'];
};

export default [
  // 获取基础信息发布进程
  {
    url: `${baseUrl}/get-latest-card-info-publish-process`,
    method: 'get',
    response: ({ url }: { url: string }) => {
      const scenario = getCurrentScenario(url);

      // 模拟API错误
      if (scenario.cardInfoState === 'ERROR') {
        return {
          code: 1,
          message: 'API调用失败',
          data: null,
        };
      }

      return {
        code: 0,
        message: 'success',
        data: {
          process: {
            processId: `card-info-${Date.now()}`,
            brandId: '100000007005',
            processType: 1,
            primaryProcessId: '',
            businessCode: '100000007005_1',
            idempotentId: '100000007005_1',
            content: {
              cardBrief: '测试商家名片',
              customerServiceList: [],
              sceneList: [],
            },
            state: scenario.cardInfoState,
            stateVersion: '1',
            creator: '500127677',
            modifier: '500127677',
            property: '0',
            submitAuditTime:
              scenario.cardInfoState >= ProcessState.AUDITING
                ? String(Date.now() / 1000)
                : '0',
            auditPassTime:
              scenario.cardInfoState === ProcessState.AUDIT_PASS
                ? String(Date.now() / 1000)
                : '0',
            auditRejectTime:
              scenario.cardInfoState === ProcessState.REJECTED
                ? String(Date.now() / 1000)
                : '0',
            cancelTime: '0',
            submitPublishTime: '0',
            startPublishTime: '0',
            finishTime: '0',
            publishType: 1,
            scheduleTime: '0',
            createTime: String(Date.now() / 1000),
            modifyTime: String(Date.now() / 1000),
          },
        },
      };
    },
  },

  // 获取服务发布进程
  {
    url: `${baseUrl}/get-latest-service-publish-process`,
    method: 'get',
    response: ({ url }: { url: string }) => {
      const scenario = getCurrentScenario(url);

      return {
        code: 0,
        message: 'success',
        data: {
          process: {
            processId: `service-${Date.now()}`,
            brandId: '100000001000',
            processType: 3,
            primaryProcessId: '',
            businessCode: '100000001000_3',
            idempotentId: '100000001000_3',
            content: {
              serviceList: [
                {
                  serviceId: '4931bb4373824fb7bfc13156b7bfaba4',
                  serviceName: '测试服务',
                  serviceClassifyName: '',
                  jumpInfo: {
                    jumpType: 4,
                    web: {
                      path: 'https://www.example.com',
                    },
                  },
                },
              ],
            },
            state: scenario.serviceState,
            stateVersion: '10',
            creator: '500128876',
            modifier: 'system',
            property: '0',
            submitAuditTime:
              scenario.serviceState >= ProcessState.AUDITING
                ? String(Date.now() / 1000)
                : '0',
            auditPassTime:
              scenario.serviceState === ProcessState.AUDIT_PASS
                ? String(Date.now() / 1000)
                : '0',
            auditRejectTime:
              scenario.serviceState === ProcessState.REJECTED
                ? String(Date.now() / 1000)
                : '0',
            cancelTime: '0',
            submitPublishTime: '0',
            startPublishTime: '0',
            finishTime: '0',
            publishType: 0,
            scheduleTime: String(Date.now() / 1000),
            createTime: String(Date.now() / 1000),
            modifyTime: String(Date.now() / 1000),
          },
        },
      };
    },
  },

  // 获取品牌会员接入状态
  {
    url: `${baseUrl}/get-brand-member-access-state`,
    method: 'get',
    response: ({ url }: { url: string }) => {
      const scenario = getCurrentScenario(url);

      return {
        code: 0,
        message: 'success',
        data: {
          state: scenario.brandMemberState,
        },
      };
    },
  },

  // 获取优惠券接入状态
  {
    url: `${baseUrl}/get-shake-coupon-access-state`,
    method: 'get',
    response: ({ url }: { url: string }) => {
      const scenario = getCurrentScenario(url);

      return {
        code: 0,
        message: 'success',
        data: {
          state: scenario.shakeCouponState,
        },
      };
    },
  },

  // 场景切换API - 用于开发调试
  {
    url: `${baseUrl}/switch-scenario`,
    method: 'post',
    response: ({ body }: { body: { scenario: string } }) => {
      const { scenario } = body;
      const config = scenarios[scenario as keyof typeof scenarios];

      if (!config) {
        return {
          code: 1,
          message: `未知场景: ${scenario}`,
          data: null,
        };
      }

      return {
        code: 0,
        message: 'success',
        data: {
          scenario,
          description: config.description,
          config,
        },
      };
    },
  },

  // 获取所有可用场景
  {
    url: `${baseUrl}/get-scenarios`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: Object.entries(scenarios).map(([key, value]) => ({
          scenario: key,
          description: value.description,
          config: value,
        })),
      };
    },
  },
] as MockMethod[];
