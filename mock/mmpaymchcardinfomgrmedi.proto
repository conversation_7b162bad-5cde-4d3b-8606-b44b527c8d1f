syntax = "proto2";

import "mmcontrib/xwi/core/xwicore.proto";
import "comm2/tlvpickle/skbuiltintype.proto";
import "mmcontrib/xwi/is_svrkit/shared/xwisvrkit.proto";
import "mmcontrib/xwi/ext_wxpay_log/xwi_wxpay_log.proto";
import "mmpaygitgateway/mmpay_comm/service_export_gen/comm/proto/mmpaylog_mask.proto";
import "mmcontrib/xwi/stub_datetime/xwi_stub_datetime.proto";
import "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_apply/comm/proto/mmpaymchmgraddition.proto";
import "phxgateway/proto/evcclient.proto";
import "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardcomm.proto";
import "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardjumpinfo.proto";
import "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardmgrcomm.proto";
import "mmcontrib/xwi/stub_msg_validator/base_msg_validator.proto";
package mmpaymchcardinfomgrmedi;

option (.xwi.file_infrastructure) = "svrkit";
option (.xwi.file_camel_case_name) = "MMPayMchCardInfoMgrMedi";
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_GetPublishProcess"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_CreatePublishProcess"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_GetLatestPublishProcess"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_SavePublishProcess"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_SubmitAudit"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_CancelPublishProcess"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_SaveAuditRecord"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_RejectAudit"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_ResetAuditRecord"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_PassAudit"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_stub_defs) = {
  name: "MMPayMchCardPublishProcessAo_SubmitPublish"
  type: "svrkit_generic_client"
  cpp_includes: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao/summer_client/client/mmpaymchcardpublishprocessaoclient.h"
  bazel_dependencies: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/domain/mmpaymchcardpublishprocessao:mmpaymchcardpublishprocessaoclient"
};
option (.xwi.file_external_packages) = "mmcontrib/xwi/is_svrkit/meta.json";
option (.xwi.file_external_packages) = "mmcontrib/xwi/ext_wxpay_log/xwi_wxpay_log.json";
option (.xwi.file_external_packages) = "mmcontrib/xwi/stub_datetime/xwi_stub_datetime.json";
option (.xwi.file_external_packages) = "mmcontrib/xwi/is_svrkit/stub_generic/svrkit_generic_client.json";
option (.xwi.file_external_packages) = "mmcontrib/xwi/stub_msg_validator/xwi_msg_validator.json";
option (.xwi.file_cpp_includes) = "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardcomm.pb.h";
option (.xwi.file_bazel_dependencies) = "//mm3rd/abseil-cpp:absl";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto:mmpaymchcardcomm_proto";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/common_enum/errorcode:errorcode";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_basic/domain/mmpaymchbrandbasictocsvr:mmpaymchbrandbasictocsvrclient";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_apply/comm/proto:mmpaymchmgraddition_proto";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto:mmpaymchcardcomm";
option (.xwi.file_bazel_dependencies) = "//phxgateway/evcgateway/api:mmpayevcapi";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto:mmpaymchcardmgrcomm_proto";
option (.xwi.file_bazel_dependencies) = "//mmpaygitgateway/mmpay_comm/service_export_gen/comm/proto:wxp_protobuf_options";
option (.xwi.file_bazel_dependencies) = "//mmpay_git/mmpay_comm/xlib_export/wxp_tool/protobuf:wxp_protobuf_json_convert";
option (.xwi.file_bazel_dependencies) = "//mmpay/mmpayxwatt/xwatt/log:log";
option (.xwi.file_bazel_sources) = "comm/card_info_util.cpp";
option (.xwi.file_bazel_sources) = "consumer_handler/card_info_consumer_handler.cpp";
option (.xwi.file_bazel_sources) = "consumer_handler/card_info_consumer_auditing.cpp";
option (.xwi.file_bazel_sources) = "consumer_handler/card_info_consumer_audit_pass.cpp";
option (.xwi.file_namespace) = "mmpaymchcardinfomgrmedi";
option (.xwisvrkit.file_svrkit_client_extraction) = {
  keep_dependencies: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardjumpinfo.proto"
  keep_dependencies: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardcomm.proto"
  keep_dependencies: "mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto/mmpaymchcardmgrcomm.proto"
  explicit_client_deps: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto:mmpaymchcardcomm_proto"
  explicit_client_deps: "//mmpaygitgateway/mmpay_comm/service_export_gen/wxpay/mch_open_app/comm/proto:mmpaymchcardmgrcomm_proto"
};
option (.xwisvrkit.file_customized_options) = {
  disable_generate_client: true
  disable_generate_docker: true
};
option (.base_msg_validator.file_options) = {
  error_code: 268574582
};

message MerchantCardServiceJumpInfoMiniProgram {
  optional string appid = 1 [(.tlvpickle.Desc) = "小程序appid"];
  optional string path = 2 [(.tlvpickle.Desc) = "小程序路径"];
}

message MerchantCardServiceJumpInfoWeb {
  optional string path = 1 [(.tlvpickle.Desc) = "web路径"];
}

message MerchantCardServiceJumpInfo {
  optional .mmpaymchcardmgrcomm.MerchantCardServiceJumpType jump_type = 1;
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceJumpInfoMiniProgram mini_program = 2;
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceJumpInfoWeb web = 3;
}

message MerchantCardService {
  optional string service_id = 1;
  optional string service_name = 2;
  optional string service_classify_name = 3;
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceJumpInfo jump_info = 5;
}

message MerchantCardServiceConfig {
  repeated .mmpaymchcardinfomgrmedi.MerchantCardService service_list = 1;
}

message MerchantCardInfoPublishProcessAudit {
  optional string overall_reject_reason = 1 [(.tlvpickle.Desc) = "总体驳回原因"];
}

message GetServiceConfigRequest {
  optional uint64 brand_id = 1 [(.tlvpickle.Desc) = "品牌ID"];
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetServiceConfigResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceConfig service_config = 2;
}

message GetLatestServicePublishProcessRequest {
  optional uint64 brand_id = 1 [(.tlvpickle.Desc) = "品牌ID"];
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetLatestServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardServicePublishProcess process = 2;
}

message CreateServicePublishProcessRequest {
  optional uint64 brand_id = 1 [(.tlvpickle.Desc) = "品牌ID"];
  optional string source = 2;
  optional string tutorial_process_id = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message CreateServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional string process_id = 2 [(.tlvpickle.Desc) = "处理ID"];
}

message SaveServicePublishProcessRequest {
  optional uint64 brand_id = 1 [(.tlvpickle.Desc) = "品牌ID"];
  optional string process_id = 2 [(.tlvpickle.Desc) = "处理ID"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceConfig content = 3;
  optional string source = 4;
  optional uint64 member_id = 5;
  optional uint64 staff_id = 6;
}

message SaveServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message SubmitServicePublishProcessRequest {
  optional uint64 brand_id = 1 [(.tlvpickle.Desc) = "品牌ID"];
  optional string process_id = 2 [(.tlvpickle.Desc) = "处理ID"];
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message SubmitServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message GetServicePublishProcessRequest {
  optional uint64 brand_id = 1 [(.tlvpickle.Desc) = "品牌ID"];
  optional string process_id = 2 [(.tlvpickle.Desc) = "处理ID"];
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message GetServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardServicePublishProcess process = 2;
}

message CreateCardInfoPublishProcessRequest {
  optional uint64 brand_id = 1 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "品牌ID"];
  optional string source = 2 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "来源"];
  optional string tutorial_process_id = 3 [(.tlvpickle.Desc) = "新手指引处理ID"];
  optional uint64 member_id = 4 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "成员ID"];
  optional uint64 staff_id = 5 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "员工系统ID"];
}

message CreateCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional string process_id = 2 [(.tlvpickle.Desc) = "处理ID"];
}

message SaveCardInfoPublishProcessRequest {
  optional uint64 brand_id = 1 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "品牌ID"];
  optional string process_id = 2 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "处理ID"];
  optional .mmpaymchcardcomm.MerchantCardInfo content = 3 [(.tlvpickle.Desc) = "单据内容"];
  optional string source = 4 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "来源"];
  optional uint64 member_id = 5 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "成员ID"];
  optional uint64 staff_id = 6 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "员工系统ID"];
}

message SaveCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message SubmitCardInfoPublishProcessRequest {
  optional uint64 brand_id = 1 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "品牌ID"];
  optional string process_id = 2 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "处理ID"];
  optional string source = 3 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "来源"];
  optional uint64 member_id = 4 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "成员ID"];
  optional uint64 staff_id = 5 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "员工系统ID"];
}

message SubmitCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message GetCardInfoPublishProcessRequest {
  optional uint64 brand_id = 1 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "品牌ID"];
  optional string process_id = 2 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "处理ID"];
  optional string source = 3 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "来源"];
  optional uint64 member_id = 4 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "成员ID"];
  optional uint64 staff_id = 5 [(.base_msg_validator.field_rules) = {
    multiplicity_min: 1
  }, (.tlvpickle.Desc) = "员工系统ID"];
}

message GetCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardInfoPublishProcess process = 2;
}

message GetCardInfoRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetCardInfoResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardcomm.MerchantCardInfo card_info = 2;
}

message GetExposeConfigStateRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetExposeConfigStateResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardmgrcomm.ExposeConfigState state = 2;
}

message GetShakeCouponAccessStateRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetShakeCouponAccessStateResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardmgrcomm.ShakeCouponAccessState state = 2;
}

message GetBrandMemberAccessStateRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetBrandMemberAccessStateResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardmgrcomm.BrandMemberAccessState state = 2;
}

message GetLatestCardInfoPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetLatestCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardInfoPublishProcess process = 2;
}

message GetDrawPreviewCodeRequest {
  optional uint64 brand_id = 1;
  optional .mmpaymchcardcomm.MerchantCardInfo card_info = 2;
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceConfig service_config = 3;
  optional string source = 4;
  optional uint64 member_id = 5;
  optional uint64 staff_id = 6;
}

message GetDrawPreviewCodeResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional string preview_id = 2;
  optional bytes preview_code = 3;
  optional uint64 expire_time = 4;
}

message GetPublishPreviewCodeRequest {
  optional uint64 brand_id = 1;
  optional string process_id = 2;
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message GetPublishPreviewCodeResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional uint64 preview_id = 2;
  optional bytes preview_code = 3;
  optional uint64 expire_time = 4;
}

message GetPreviewCodeScanStateRequest {
  optional string preview_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetPreviewCodeScanStateResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardmgrcomm.PreviewCodeScanState state = 2;
}

message QueryBrandAppidListRequest {
  message PaginationInfo {
    optional uint32 page_index = 1;
    optional uint32 page_size = 2;
  }
  optional uint64 brand_id = 1;
  optional .mmpaymchcardinfomgrmedi.QueryBrandAppidListRequest.PaginationInfo pagination_info = 2;
  optional string source = 3;
  optional string appid = 4;
  optional uint64 member_id = 5;
  optional uint64 staff_id = 6;
}

message QueryBrandAppidListResponse {
  message BrandAppidRelation {
    optional string appid = 1;
    optional string app_name = 2;
  }
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  repeated .mmpaymchcardinfomgrmedi.QueryBrandAppidListResponse.BrandAppidRelation brand_appid_relation_list = 2;
  optional uint64 total_count = 3;
}

message QueryBrandFinderListRequest {
  message PaginationInfo {
    optional uint32 page_index = 1;
    optional uint32 page_size = 2;
  }
  optional uint64 brand_id = 1;
  optional .mmpaymchcardinfomgrmedi.QueryBrandFinderListRequest.PaginationInfo pagination_info = 2;
  optional string source = 3;
  optional string finder_username = 4;
  optional uint64 member_id = 5;
  optional uint64 staff_id = 6;
}

message QueryBrandFinderListResponse {
  message BrandFinderRelation {
    optional string finder_username = 1;
    optional string finder_nickname = 2;
  }
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  repeated .mmpaymchcardinfomgrmedi.QueryBrandFinderListResponse.BrandFinderRelation brand_finder_relation_list = 2;
  optional uint64 total_count = 3;
}

message CreateTutorialPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message CreateTutorialPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional string process_id = 2;
}

message SubmitTutorialPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string process_id = 2;
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message SubmitTutorialPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message MerchantCardTutorialPublishProcessContent {
}

message MerchantCardTutorialPublishProcessAudit {
  optional string overall_audit_result = 1;
}

message MerchantCardTutorialPublishProcess {
  optional string process_id = 1 [(.tlvpickle.Desc) = "发布单标识ID"];
  optional uint64 brand_id = 2 [(.tlvpickle.Desc) = "品牌ID"];
  optional uint32 process_type = 3 [(.tlvpickle.Desc) = "发布单类型"];
  optional string primary_process_id = 4 [(.tlvpickle.Desc) = "父发布单标识ID"];
  optional string business_code = 5 [(.tlvpickle.Desc) = "业务单号"];
  optional string idempotent_id = 6 [(.tlvpickle.Desc) = "幂等ID"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardTutorialPublishProcessContent content = 7 [(.tlvpickle.Desc) = "名片信息配置"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardTutorialPublishProcessAudit audit = 8 [(.tlvpickle.Desc) = "审核信息"];
  optional .mmpaymchcardmgrcomm.MerchantCardPublishProcessState state = 9 [(.tlvpickle.Desc) = "状态"];
  optional uint64 state_version = 10 [(.tlvpickle.Desc) = "状态版本号"];
  optional string creator = 11 [(.tlvpickle.Desc) = "创建人"];
  optional string modifier = 12 [(.tlvpickle.Desc) = "修改人"];
  optional uint64 property = 13 [(.tlvpickle.Desc) = "属性"];
  optional uint64 submit_audit_time = 14 [(.tlvpickle.Desc) = "提交审核时间"];
  optional uint64 audit_pass_time = 15 [(.tlvpickle.Desc) = "审核通过时间"];
  optional uint64 audit_reject_time = 16 [(.tlvpickle.Desc) = "审核驳回时间"];
  optional uint64 cancel_time = 17 [(.tlvpickle.Desc) = "撤销时间"];
  optional uint64 submit_publish_time = 18 [(.tlvpickle.Desc) = "提交发布时间"];
  optional uint64 start_publish_time = 19 [(.tlvpickle.Desc) = "开始发布时间"];
  optional uint64 finish_time = 20 [(.tlvpickle.Desc) = "完成时间"];
  optional .mmpaymchcardmgrcomm.MerchantCardPublishType publish_type = 21 [(.tlvpickle.Desc) = "发布类型"];
  optional uint64 schedule_time = 22 [(.tlvpickle.Desc) = "计划发布时间"];
  optional uint64 create_time = 23 [(.tlvpickle.Desc) = "创建时间"];
  optional uint64 modify_time = 24 [(.tlvpickle.Desc) = "修改时间"];
}

message MerchantCardInfoPublishProcess {
  optional string process_id = 1 [(.tlvpickle.Desc) = "发布单标识ID"];
  optional uint64 brand_id = 2 [(.tlvpickle.Desc) = "品牌ID"];
  optional uint32 process_type = 3 [(.tlvpickle.Desc) = "发布单类型"];
  optional string primary_process_id = 4 [(.tlvpickle.Desc) = "父发布单标识ID"];
  optional string business_code = 5 [(.tlvpickle.Desc) = "业务单号"];
  optional string idempotent_id = 6 [(.tlvpickle.Desc) = "幂等ID"];
  optional .mmpaymchcardcomm.MerchantCardInfo content = 7 [(.tlvpickle.Desc) = "名片信息配置"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardInfoPublishProcessAudit audit = 8 [(.tlvpickle.Desc) = "审核信息"];
  optional .mmpaymchcardmgrcomm.MerchantCardPublishProcessState state = 9 [(.tlvpickle.Desc) = "状态"];
  optional uint64 state_version = 10 [(.tlvpickle.Desc) = "状态版本号"];
  optional string creator = 11 [(.tlvpickle.Desc) = "创建人"];
  optional string modifier = 12 [(.tlvpickle.Desc) = "修改人"];
  optional uint64 property = 13 [(.tlvpickle.Desc) = "属性"];
  optional uint64 submit_audit_time = 14 [(.tlvpickle.Desc) = "提交审核时间"];
  optional uint64 audit_pass_time = 15 [(.tlvpickle.Desc) = "审核通过时间"];
  optional uint64 audit_reject_time = 16 [(.tlvpickle.Desc) = "审核驳回时间"];
  optional uint64 cancel_time = 17 [(.tlvpickle.Desc) = "撤销时间"];
  optional uint64 submit_publish_time = 18 [(.tlvpickle.Desc) = "提交发布时间"];
  optional uint64 start_publish_time = 19 [(.tlvpickle.Desc) = "开始发布时间"];
  optional uint64 finish_time = 20 [(.tlvpickle.Desc) = "完成时间"];
  optional .mmpaymchcardmgrcomm.MerchantCardPublishType publish_type = 21 [(.tlvpickle.Desc) = "发布类型"];
  optional uint64 schedule_time = 22 [(.tlvpickle.Desc) = "计划发布时间"];
  optional uint64 create_time = 23 [(.tlvpickle.Desc) = "创建时间"];
  optional uint64 modify_time = 24 [(.tlvpickle.Desc) = "修改时间"];
}

message MerchantCardServicePublishProcessAudit {
  optional string overall_audit_result = 1;
}

message MerchantCardServicePublishProcess {
  optional string process_id = 1 [(.tlvpickle.Desc) = "发布单标识ID"];
  optional uint64 brand_id = 2 [(.tlvpickle.Desc) = "品牌ID"];
  optional uint32 process_type = 3 [(.tlvpickle.Desc) = "发布单类型"];
  optional string primary_process_id = 4 [(.tlvpickle.Desc) = "父发布单标识ID"];
  optional string business_code = 5 [(.tlvpickle.Desc) = "业务单号"];
  optional string idempotent_id = 6 [(.tlvpickle.Desc) = "幂等ID"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardServiceConfig content = 7 [(.tlvpickle.Desc) = "名片信息配置"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardServicePublishProcessAudit audit = 8 [(.tlvpickle.Desc) = "审核信息"];
  optional .mmpaymchcardmgrcomm.MerchantCardPublishProcessState state = 9 [(.tlvpickle.Desc) = "状态"];
  optional uint64 state_version = 10 [(.tlvpickle.Desc) = "状态版本号"];
  optional string creator = 11 [(.tlvpickle.Desc) = "创建人"];
  optional string modifier = 12 [(.tlvpickle.Desc) = "修改人"];
  optional uint64 property = 13 [(.tlvpickle.Desc) = "属性"];
  optional uint64 submit_audit_time = 14 [(.tlvpickle.Desc) = "提交审核时间"];
  optional uint64 audit_pass_time = 15 [(.tlvpickle.Desc) = "审核通过时间"];
  optional uint64 audit_reject_time = 16 [(.tlvpickle.Desc) = "审核驳回时间"];
  optional uint64 cancel_time = 17 [(.tlvpickle.Desc) = "撤销时间"];
  optional uint64 submit_publish_time = 18 [(.tlvpickle.Desc) = "提交发布时间"];
  optional uint64 start_publish_time = 19 [(.tlvpickle.Desc) = "开始发布时间"];
  optional uint64 finish_time = 20 [(.tlvpickle.Desc) = "完成时间"];
  optional .mmpaymchcardmgrcomm.MerchantCardPublishType publish_type = 21 [(.tlvpickle.Desc) = "发布类型"];
  optional uint64 schedule_time = 22 [(.tlvpickle.Desc) = "计划发布时间"];
  optional uint64 create_time = 23 [(.tlvpickle.Desc) = "创建时间"];
  optional uint64 modify_time = 24 [(.tlvpickle.Desc) = "修改时间"];
}

message GetLatestTutorialPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string source = 2;
  optional uint64 member_id = 3;
  optional uint64 staff_id = 4;
}

message GetLatestTutorialPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardTutorialPublishProcess process = 2;
}

message GetTutorialPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string process_id = 2;
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message GetTutorialPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional .mmpaymchcardinfomgrmedi.MerchantCardTutorialPublishProcess process = 2;
}

message CancelServicePublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string process_id = 2;
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message CancelServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message CancelCardInfoPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string process_id = 2;
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message CancelCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message CancelTutorialPublishProcessRequest {
  optional uint64 brand_id = 1;
  optional string process_id = 2;
  optional string source = 3;
  optional uint64 member_id = 4;
  optional uint64 staff_id = 5;
}

message CancelTutorialPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message SavePublishConfigRequest {
  optional string source = 1;
  optional uint64 brand_id = 2;
  optional string process_id = 3;
  optional .mmpaymchcardmgrcomm.MerchantCardPublishType publish_type = 4;
  optional uint64 schedule_time = 5;
  optional uint64 member_id = 6;
  optional uint64 staff_id = 7;
}

message SavePublishConfigResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
}

message QueryQcAuditServicePublishProcessRequest {
  message PaginationInfo {
    optional string page_context = 1;
    optional uint32 page_size = 2;
  }
  optional .mmpaymchcardinfomgrmedi.QueryQcAuditServicePublishProcessRequest.PaginationInfo pagination_info = 1;
  optional string source = 2;
}

message QueryQcAuditServicePublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional string page_context = 2;
  repeated .mmpaymchcardinfomgrmedi.MerchantCardServicePublishProcess process_list = 3;
}

message QueryQcAuditCardInfoPublishProcessRequest {
  message PaginationInfo {
    optional string page_context = 1;
    optional uint32 page_size = 2;
  }
  optional .mmpaymchcardinfomgrmedi.QueryQcAuditCardInfoPublishProcessRequest.PaginationInfo pagination_info = 1;
  optional string source = 2;
}

message QueryQcAuditCardInfoPublishProcessResponse {
  optional string error_message = 1 [(.xwisvrkit.field_response_data_binding) = SRDB_ERROR_MESSAGE, (.tlvpickle.Desc) = "用于描述具体的错误消息"];
  optional string page_context = 2;
  repeated .mmpaymchcardinfomgrmedi.MerchantCardInfoPublishProcess process_list = 3;
}

service MMPayMchCardInfoMgrMedi {
  option (.xwisvrkit.service_oss_attr_id) = 89094;
  option (.tlvpickle.Magic) = 21759;
  rpc GetCardInfo(.mmpaymchcardinfomgrmedi.GetCardInfoRequest) returns (.mmpaymchcardinfomgrmedi.GetCardInfoResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 1;
    option (.tlvpickle.Brief) = "查询名片信息";
  }
  rpc GetServiceConfig(.mmpaymchcardinfomgrmedi.GetServiceConfigRequest) returns (.mmpaymchcardinfomgrmedi.GetServiceConfigResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 2;
    option (.tlvpickle.Brief) = "查询名片服务配置";
  }
  rpc GetExposeConfigState(.mmpaymchcardinfomgrmedi.GetExposeConfigStateRequest) returns (.mmpaymchcardinfomgrmedi.GetExposeConfigStateResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 3;
    option (.tlvpickle.Brief) = "查询名片曝光配置状态";
  }
}

service PublishProcess {
  option (.xwisvrkit.service_oss_attr_id) = 89094;
  option (.tlvpickle.Magic) = 21759;
  rpc CreateCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.CreateCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.CreateCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 4;
    option (.tlvpickle.Brief) = "创建名片信息发布单";
  }
  rpc SaveCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.SaveCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.SaveCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 5;
    option (.tlvpickle.Brief) = "保存名片信息发布单";
  }
  rpc SubmitCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.SubmitCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.SubmitCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 6;
    option (.tlvpickle.Brief) = "提交名片信息发布单";
  }
  rpc GetCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.GetCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.GetCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 7;
    option (.tlvpickle.Brief) = "查询名片信息发布单";
  }
  rpc GetLatestCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.GetLatestCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.GetLatestCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 8;
    option (.tlvpickle.Brief) = "查询最新的名片信息发布单";
  }
  rpc CancelCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.CancelCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.CancelCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 9;
    option (.tlvpickle.Brief) = "作废发布单";
  }
  rpc GetLatestServicePublishProcess(.mmpaymchcardinfomgrmedi.GetLatestServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.GetLatestServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 10;
    option (.tlvpickle.Brief) = "查询最新的名片服务发布单";
  }
  rpc GetServicePublishProcess(.mmpaymchcardinfomgrmedi.GetServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.GetServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 11;
    option (.tlvpickle.Brief) = "查询名片服务发布单";
  }
  rpc CreateServicePublishProcess(.mmpaymchcardinfomgrmedi.CreateServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.CreateServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 12;
    option (.tlvpickle.Brief) = "创建名片服务发布单";
  }
  rpc SaveServicePublishProcess(.mmpaymchcardinfomgrmedi.SaveServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.SaveServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 13;
    option (.tlvpickle.Brief) = "保存名片服务发布单";
  }
  rpc SubmitServicePublishProcess(.mmpaymchcardinfomgrmedi.SubmitServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.SubmitServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 14;
    option (.tlvpickle.Brief) = "提交名片服务发布单";
  }
  rpc CancelServicePublishProcess(.mmpaymchcardinfomgrmedi.CancelServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.CancelServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 15;
    option (.tlvpickle.Brief) = "作废名片服务发布单";
  }
  rpc CreateTutorialPublishProcess(.mmpaymchcardinfomgrmedi.CreateTutorialPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.CreateTutorialPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 16;
    option (.tlvpickle.Brief) = "创建新手引导发布单";
  }
  rpc SubmitTutorialPublishProcess(.mmpaymchcardinfomgrmedi.SubmitTutorialPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.SubmitTutorialPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 17;
    option (.tlvpickle.Brief) = "提交新手引导发布单";
  }
  rpc GetTutorialPublishProcess(.mmpaymchcardinfomgrmedi.GetTutorialPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.GetTutorialPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 18;
    option (.tlvpickle.Brief) = "查询新手引导发布单";
  }
  rpc CancelTutorialPublishProcess(.mmpaymchcardinfomgrmedi.CancelTutorialPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.CancelTutorialPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 19;
    option (.tlvpickle.Brief) = "撤回新手引导发布单";
  }
  rpc GetLatestTutorialPublishProcess(.mmpaymchcardinfomgrmedi.GetLatestTutorialPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.GetLatestTutorialPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 20;
    option (.tlvpickle.Brief) = "查询最新的新手引导发布单";
  }
  rpc MchCardPublishProcessStateChangeSubscriber(.mmevc.EvcPostRequest) returns (.mmevc.EvcPostResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 21;
    option (.tlvpickle.Brief) = "商家名片发布单状态变更订阅者";
  }
  rpc SavePublishConfig(.mmpaymchcardinfomgrmedi.SavePublishConfigRequest) returns (.mmpaymchcardinfomgrmedi.SavePublishConfigResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 22;
    option (.tlvpickle.Brief) = "保存发布配置";
  }
  rpc GetDrawPreviewCode(.mmpaymchcardinfomgrmedi.GetDrawPreviewCodeRequest) returns (.mmpaymchcardinfomgrmedi.GetDrawPreviewCodeResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 23;
    option (.tlvpickle.Brief) = "查询草稿预览码";
  }
  rpc GetPublishPreviewCode(.mmpaymchcardinfomgrmedi.GetPublishPreviewCodeRequest) returns (.mmpaymchcardinfomgrmedi.GetPublishPreviewCodeResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 24;
    option (.tlvpickle.Brief) = "查询发布预览码";
  }
  rpc GetPreviewCodeScanState(.mmpaymchcardinfomgrmedi.GetPreviewCodeScanStateRequest) returns (.mmpaymchcardinfomgrmedi.GetPreviewCodeScanStateResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 25;
    option (.tlvpickle.Brief) = "查询预览码扫码状态";
  }
  rpc QueryQcAuditServicePublishProcess(.mmpaymchcardinfomgrmedi.QueryQcAuditServicePublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.QueryQcAuditServicePublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 30;
    option (.tlvpickle.Brief) = "用于接受 30 的网络包";
  }
  rpc QueryQcAuditCardInfoPublishProcess(.mmpaymchcardinfomgrmedi.QueryQcAuditCardInfoPublishProcessRequest) returns (.mmpaymchcardinfomgrmedi.QueryQcAuditCardInfoPublishProcessResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 31;
    option (.tlvpickle.Brief) = "用于接受 31 的网络包";
  }
}

service BusinessParticipants {
  rpc GetShakeCouponAccessState(.mmpaymchcardinfomgrmedi.GetShakeCouponAccessStateRequest) returns (.mmpaymchcardinfomgrmedi.GetShakeCouponAccessStateResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 26;
    option (.tlvpickle.Brief) = "查询摇优惠接入状态";
  }
  rpc GetBrandMemberAccessState(.mmpaymchcardinfomgrmedi.GetBrandMemberAccessStateRequest) returns (.mmpaymchcardinfomgrmedi.GetBrandMemberAccessStateResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 27;
    option (.tlvpickle.Brief) = "查询品牌会员接入状态";
  }
  rpc QueryBrandAppidList(.mmpaymchcardinfomgrmedi.QueryBrandAppidListRequest) returns (.mmpaymchcardinfomgrmedi.QueryBrandAppidListResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 28;
    option (.tlvpickle.Brief) = "查询品牌关联APPID列表";
  }
  rpc QueryBrandFinderList(.mmpaymchcardinfomgrmedi.QueryBrandFinderListRequest) returns (.mmpaymchcardinfomgrmedi.QueryBrandFinderListResponse) {
    option (.xwi.rpc_no_test) = true;
    option (.xwi.rpc_handler_signature) = RHS_VOID;
    option (.tlvpickle.CmdID) = 29;
    option (.tlvpickle.Brief) = "查询品牌关联视频号关系列表";
  }
}