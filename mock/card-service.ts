import { MockMethod } from 'vite-plugin-mock';

// 直接定义baseUrl，避免导入src文件造成依赖问题
const baseUrl = '/xdc/mchcardinfomgrweb';

// enum MerchantCardPublishProcessState {
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_INIT = 0;           // 初始状态
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_DRAFT = 1;          // 草稿
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_AUDITING = 2;       // 待审核
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_AUDIT_PASS = 3;     // 审核通过
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_AUDIT_REJECT = 4;   // 审核驳回
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_CANCELED = 5;       // 已作废
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_TO_BE_PUBLISH = 6;  // 待发布
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_PUBLISHING = 7;     // 发布中
//   MERCHANT_CARD_PUBLISH_PROCESS_STATE_FINISH = 8;         // 已完成
// }

// 模拟服务列表数据
const mockServiceList = [
  {
    serviceId: '001',
    serviceName: '会员福利1',
    serviceClassifyName: 'mock分类',
    operationSlot: {
      slotKey: 'slot_1',
      slotTitle: '会员福利',
      slotDesc: '尊享会员专属福利',
      imageList: ['https://dummyimage.com/100x100/000/fff'],
    },
    jumpInfo: {
      jumpType: 1,
      miniProgram: {
        appid: 'wx12345678',
        path: 'pages/index/index',
      },
      web: {
        path: '',
      },
    },
  },
  {
    serviceId: '002',
    serviceName: '优惠券',
    serviceClassifyName: 'mock分类',
    operationSlot: {
      slotKey: 'slot_2',
      slotTitle: '优惠券',
      slotDesc: '领取专属优惠券',
      imageList: ['https://dummyimage.com/100x100/000/fff'],
    },
    jumpInfo: {
      jumpType: 1,
      miniProgram: {
        appid: 'wx12345678',
        path: 'pages/coupon/index',
      },
      web: {
        path: '',
      },
    },
  },
  {
    serviceId: '003',
    serviceName: '限时秒杀',
    serviceClassifyName: 'mock分类2',
    operationSlot: {
      slotKey: 'slot_3',
      slotTitle: '限时秒杀',
      slotDesc: '超值好物抢购',
      imageList: ['https://dummyimage.com/100x100/000/fff'],
    },
    jumpInfo: {
      jumpType: 1,
      miniProgram: {
        appid: 'wx12345678',
        path: 'pages/seckill/index',
      },
      web: {
        path: '',
      },
    },
  },
];

const mockServiceList2 = [
  {
    serviceId: '001',
    serviceName: '会员福利1',
    serviceClassifyName: 'latest',
    operationSlot: {
      slotKey: 'slot_1',
      slotTitle: '会员福利',
      slotDesc: '尊享会员专属福利',
      imageList: ['https://dummyimage.com/100x100/000/fff'],
    },
    jumpInfo: {
      jumpType: 1,
      miniProgram: {
        appid: 'wx12345678',
        path: 'pages/index/index',
      },
      web: {
        path: '',
      },
    },
  },
  {
    serviceId: '002',
    serviceName: '优惠券',
    serviceClassifyName: 'latest',
    operationSlot: {
      slotKey: 'slot_2',
      slotTitle: '优惠券',
      slotDesc: '领取专属优惠券',
      imageList: ['https://dummyimage.com/100x100/000/fff'],
    },
    jumpInfo: {
      jumpType: 1,
      miniProgram: {
        appid: 'wx12345678',
        path: 'pages/coupon/index',
      },
      web: {
        path: '',
      },
    },
  },
  {
    serviceId: '003',
    serviceName: '限时秒杀',
    serviceClassifyName: 'test2',
    operationSlot: {
      slotKey: 'slot_3',
      slotTitle: '限时秒杀',
      slotDesc: '超值好物抢购',
      imageList: ['https://dummyimage.com/100x100/000/fff'],
    },
    jumpInfo: {
      jumpType: 1,
      miniProgram: {
        appid: 'wx12345678',
        path: 'pages/seckill/index',
      },
      web: {
        path: '',
      },
    },
  },
];

const mockEmpty = [];

// 模拟流程数据
let mockProcess = {
  finishTime: '1692095400000',
  creator: 'admin',
  modifier: 'admin',
  createTime: '1691654400000',
  modifyTime: '1692095400000',
  rejectReason: {
    overallRejectReason: '',
  },
  processId: 'mockProcessid',
  //  1立即发布 2定时发布
  type: 1,
  businessCode: 'CARD_SERVICE',
  // 1 草稿 2审核中 4驳回 6待发布 8已发布
  state: 4,
  scheduleTime: '1692095400000',
  property: 'service_config',
  content: {
    serviceList: mockEmpty,
    // serviceList: mockServiceList2,
  },
  idempotentId: 'idem_12345',
  submitAuditTime: '',
  auditPassTime: '',
  auditRejectTime: '',
  cancelTime: '',
};

// get-latest-service-publish-process
let mockProcess2 = {
  processId: '2025071317505854803991',
  brandId: '100000007005',
  processType: 3,
  primaryProcessId: '2025071317303550274757',
  businessCode: '100000007005_3',
  idempotentId: '100000007005_3',
  content: {
    serviceList: [
      {
        serviceId: '',
        serviceName: '321',
        serviceClassifyName: '无分类',
        jumpInfo: {
          jumpType: 4,
          miniProgram: {
            appid: '',
            path: '',
          },
          web: {
            path: 'http://123.com',
          },
        },
      },
      {
        serviceId: '2222222',
        serviceName: '21321',
        serviceClassifyName: 'aimage',
        jumpInfo: {
          jumpType: 4,
          miniProgram: {
            appid: '',
            path: '',
          },
          web: {
            path: 'http://996.com',
          },
        },
      },
      {
        serviceId: '',
        serviceName: '321321',
        serviceClassifyName: 'aimage',
        jumpInfo: {
          jumpType: 4,
          miniProgram: {
            appid: '',
            path: '',
          },
          web: {
            path: 'http://hha.com',
          },
        },
      },
    ],
  },
  state: 2,
  stateVersion: '6',
  creator: '500127677',
  modifier: 'system',
  property: '0',
  submitAuditTime: '1752411101',
  auditPassTime: '0',
  auditRejectTime: '0',
  audit: {
    auditor:
      '1.分类名称包含平台禁止的敏感词，请调整为无违规的分类名称\n2.服务名称包含平台禁止的敏感词，请调整为无违规的服务名称\n3.跳',
  },
  cancelTime: '0',
  submitPublishTime: '0',
  startPublishTime: '0',
  finishTime: '0',
  publishType: 0,
  scheduleTime: '0',
  createTime: '1752400258',
  modifyTime: '1752411101',
};
export default [
  // 1. 撤销名片服务发布单
  {
    url: `${baseUrl}/cancel-service-publish-process`,
    method: 'post',
    response: ({ body }) => {
      return {
        code: 0,
        message: 'success',
        data: {},
      };
    },
  },

  // 2. 创建名片服务发布单
  {
    url: `${baseUrl}/create-service-publish-process`,
    method: 'post',
    response: () => {
      // 重置流程状态
      const currentTime = Date.now().toString();
      mockProcess = {
        ...mockProcess,
        state: 0, // 初始状态
        createTime: currentTime,
        modifyTime: currentTime,
        submitAuditTime: '',
        auditPassTime: '',
        auditRejectTime: '',
        cancelTime: '',
      };

      return {
        code: 0,
        message: 'success',
        data: {
          processId: mockProcess.processId,
        },
      };
    },
  },

  // 4. 获取服务配置
  {
    url: `${baseUrl}/get-service-config`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          serviceConfig: {
            serviceList: mockServiceList,
          },
        },
      };
    },
  },

  // 5. 查询名片服务发布单详情
  {
    url: `${baseUrl}/get-service-publish-process`,
    method: 'get',
    response: ({ query }) => {
      if (query && query.processId) {
        return {
          code: 0,
          message: 'success',
          data: {
            process: mockProcess2,
          },
        };
      }
    },
  },
  {
    url: `${baseUrl}/get-card-info-publish-process`,
    method: 'get',
    response: ({ query }) => {
      if (query && query.processId) {
        return {
          code: 0,
          message: 'success',
          data: {
            process: mockProcess2,
          },
        };
      }
      return {
        code: 0,
        message: 'success',
        data: {},
      };
    },
  },

  // 6. 保存名片服务发布单
  {
    url: `${baseUrl}/save-service-publish-process`,
    method: 'post',
    response: ({ body }) => {
      return {
        code: 0,
        message: 'success',
        data: {},
      };
    },
  },

  // 7. 提交名片服务发布单
  {
    url: `${baseUrl}/submit-service-publish-process`,
    method: 'post',
    response: ({ body }) => {
      return {
        code: 0,
        message: 'success',
        data: {},
      };
    },
  },

  // 8. 查询品牌关联小程序列表
  {
    url: `${baseUrl}/query-brand-miniprogram-list`,
    method: 'get',
    response: ({ query }) => {
      return {
        code: 0,
        message: '查询成功',
        data: {
          brandAppidRelationList: [
            {
              appid: '0000000001',
              appName: 'test1',
            },
            {
              appid: '0000000002',
              appName: 'test2',
            },
            {
              appid: '0000000003',
              appName: 'test3',
            },
          ],
          totalCount: '3',
        },
      };
    },
  },
] as MockMethod[];
