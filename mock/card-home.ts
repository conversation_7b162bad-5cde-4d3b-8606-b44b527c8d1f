import { MockMethod } from 'vite-plugin-mock';
// 直接使用数字常量，避免导入可能导致的问题
// ProcessState.PUBLISHED = 8
// import { ProcessState } from '../src/pages/card-mna/common/model/tutorial';

// 模拟数据
const mockCardInfo = {
  isConfigured: false,
  cardType: 'basic',
  lastUpdated: new Date().toISOString(),
};

// 模拟API接口
export default [
  // 获取商户卡片基础信息
  {
    url: '/api/merchant/card/info',
    method: 'get',
    response: () => ({
      code: 0,
      data: mockCardInfo,
    }),
  },

  // 创建商户卡片
  {
    url: '/api/merchant/card/create',
    method: 'post',
    response: (req: { body: Record<string, unknown> }) => {
      console.log('创建卡片请求数据:', req.body);
      return {
        code: 0,
        data: {
          cardId: `mock-card-${Date.now()}`,
          ...req.body,
        },
      };
    },
  },

  // 创建新手引导发布流程
  {
    url: '/api/tutorial/create-process',
    method: 'post',
    response: () => ({
      code: 0,
      data: {
        processId: `mock-process-${Date.now()}`,
      },
    }),
  },

  // 获取最新的新手引导发布流程
  {
    url: '/get-latest-tutorial-publish-process',
    method: 'get',
    response: () =>
      // 模拟新用户状态，没有发布流程
      ({
        code: -1,
        message: '未找到教程流程',
      }),
  },

  // 获取卡片信息
  {
    url: '/get-card-info',
    method: 'get',
    response: () =>
      // 模拟未配置状态
      ({
        code: -1,
        message: '未配置',
      }),
  },

  // 获取服务配置
  {
    url: '/get-service-config',
    method: 'get',
    response: () =>
      // 模拟未配置状态
      ({
        code: -1,
        message: '未配置',
      }),
  },
] as MockMethod[];
