import { MockMethod } from 'vite-plugin-mock';

// 直接定义baseUrl，避免导入src文件造成依赖问题
const baseUrl = '/xdc/mchcardinfomgrweb';

export default [
  {
    url: `${baseUrl}/card-info`,
    method: 'get',
    response: () => {
      return {
        code: 1,
        message: 'success',
        data: {
          cardBrief: 'string',
          customerService: [
            {
              customerServiceType: 1,
              miniProgram: {
                appid: 'string',
                username: 'string',
              },
              wecom: {
                path: 'string',
              },
              customize: {
                path: 'string',
              },
              servicePhone: 'string',
            },
          ],
          sceneList: [
            {
              sceneType: 1,
              miniProgram: {
                sceneId: 'string',
                appid: 'string',
                sceneTag: 'string',
                imageList: ['string'],
              },
              finder: {
                sceneId: 'string',
                finderUsername: 'string',
                sceneTag: 'string',
                imageList: ['string'],
              },
              sceneTag: 'string',
              imageList: ['string'],
            },
          ],
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-expose-config-state`,
    method: 'get',
    response: () => {
      return {
        code: 1,
        message: 'success',
        data: {
          exposeState: 1,
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-brand-member-access-state`,
    method: 'get',
    response: () => {
      return {
        code: 1,
        message: 'success',
        data: {
          accessState: 1,
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-shake-coupon-access-state`,
    method: 'get',
    response: () => {
      return {
        code: 1,
        message: 'success',
        data: {
          accessState: 1,
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-latest-tutorial-publish-process`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          process: {
            // 新手单状态
            state: 0,
            processId: '2025071317303550274757',
            brandId: '100000007005',
            processType: 1,
            primaryProcessId: '',
            businessCode: '100000007005_1',
            idempotentId: '100000007005_1',
            content: null,
            audit: null,
            stateVersion: '1',
            creator: '500127677',
            modifier: '500127677',
            property: '0',
            submitAuditTime: '0',
            auditPassTime: '0',
            auditRejectTime: '0',
            cancelTime: '0',
            submitPublishTime: '0',
            startPublishTime: '0',
            finishTime: '0',
            publishType: 1,
            scheduleTime: '0',
            createTime: '1752399035',
            modifyTime: '1752399035',
          },
        },
      };
    },
  },

  {
    url: `${baseUrl}/get-latest-service-publish-process`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          process: {
            processId: '2025072411355813189539',
            brandId: '100000001000',
            processType: 3,
            primaryProcessId: '',
            businessCode: '100000001000_3',
            idempotentId: '100000001000_3',
            content: {
              serviceList: [
                {
                  serviceId: '4931bb4373824fb7bfc13156b7bfaba4',
                  serviceName: '哈66',
                  serviceClassifyName: '',
                  jumpInfo: {
                    jumpType: 4,
                    web: {
                      path: 'https://www.a301oc.om',
                    },
                  },
                },
                {
                  serviceId: '3cc78b2dc040427ab33cac570222788f',
                  serviceName: '321312',
                  serviceClassifyName: '',
                  jumpInfo: {
                    jumpType: 4,
                    web: {
                      path: 'http://www.321.com',
                    },
                  },
                },
              ],
            },
            state: 3,
            stateVersion: '10',
            creator: '500128876',
            modifier: 'system',
            property: '0',
            submitAuditTime: '1753328231',
            auditPassTime: '1753328293',
            auditRejectTime: '0',
            cancelTime: '0',
            submitPublishTime: '1753328294',
            startPublishTime: '0',
            finishTime: '0',
            publishType: 0,
            scheduleTime: '1753328158',
            createTime: '1753328158',
            modifyTime: '1753328294',
          },
        },
      };
    },
  },

  {
    url: `${baseUrl}/get-latest-card-info-publish-process`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          process: {
            processId: 'string',
            type: 1,
            primaryProcessId: 'string',
            businessCode: 'string',
            state: 6,
            stateVersion: 'string',
            property: 'string',
            content: {
              customerServiceList: [
                {
                  customerServiceType: 1,
                  miniProgram: {
                    appid: 'string',
                    username: 'miniProgram',
                  },
                  wecom: {
                    path: 'string',
                  },
                  customizeWeb: {
                    path: 'string',
                  },
                  customizeMp: {
                    appid: 'string',
                    path: 'string',
                  },
                  servicePhone: 'string',
                },
              ],
              sceneList: [
                {
                  sceneType: 1,
                  miniProgram: {
                    appid: 'string',
                    sceneTag: 'string',
                    imageList: ['string'],
                  },
                  finder: {
                    finderUsername: 'finderUsername',
                    sceneTag: 'sceneTag',
                    imageList: ['string'],
                  },
                  sceneId: 'string',
                },
                {
                  sceneType: 2,
                  miniProgram: {
                    appid: 'string',
                    sceneTag: 'sceneTag',
                    imageList: ['string'],
                  },
                  finder: {
                    finderUsername: 'finderUsername',
                    sceneTag: 'sceneTag',
                    imageList: ['string'],
                  },
                  sceneId: 'string',
                },
              ],
              cardBrief: 'string',
            },
            audit: {
              overallAuditResult: 'string',
            },
            idempotentId: 'string',
            submitAuditTime: '1752399035',
            auditPassTime: '1752399035',
            auditRejectTime: '1752399035',
            cancelTime: '1752399035',
            submitPublishTime: '1752399035',
            startPublishTime: '1752399035',
            finishTime: '1752399035',
            publishType: 1,
            scheduleTime: '1752399035',
            creator: '1752399035',
            modifier: '1752399035',
            createTime: '1752399035',
            modifyTime: '1752399035',
          },
        },
      };
    },
  },
  {
    url: `${baseUrl}/create-tutorial-publish-process`,
    method: 'post',
    response: () => {
      return {
        code: 1,
        message: 'success',
        data: {
          processId: 'string',
        },
      };
    },
  },
  {
    url: `${baseUrl}/submit-tutorial-publish-process`,
    method: 'post',
    response: () => {
      return {
        code: 1,
        message: 'success',
        data: {},
      };
    },
  },
] as MockMethod[];
