import { Mo<PERSON><PERSON>eth<PERSON> } from 'vite-plugin-mock';
export default [
  {
    url: '/xdc/brandmanageweb/v1/compass-activity/overview',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          activity_overview: {
            activity_overview_statistics_list: [
              {
                activity_overview_type: 1,
                overview_statistics_num: 13330,
                overview_compare_last_cycle: '+47.78%',
              },
              {
                activity_overview_type: 2,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 3,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 4,
                overview_statistics_num: 3,
                overview_compare_last_cycle: '+50.00%',
              },
              {
                activity_overview_type: 5,
                overview_statistics_num: 3,
                overview_compare_last_cycle: '+50.00%',
              },
              {
                activity_overview_type: 6,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 7,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 8,
                overview_statistics_num: 3,
                overview_compare_last_cycle: '+200.00%',
              },
              {
                activity_overview_type: 9,
                overview_statistics_num: 3,
                overview_compare_last_cycle: '+200.00%',
              },
              {
                activity_overview_type: 10,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 11,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 12,
                overview_statistics_num: 10000,
                overview_compare_last_cycle: '0',
              },
              {
                activity_overview_type: 13,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '0',
              },
            ],
            activity_overview_trend_list: [
              {
                activity_overview_type: 1,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 4510 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 4510 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 4310 },
                ],
              },
              {
                activity_overview_type: 2,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 3,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 4,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 1 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 1 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 1 },
                ],
              },
              {
                activity_overview_type: 5,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 1 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 1 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 1 },
                ],
              },
              {
                activity_overview_type: 6,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 7,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 8,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 1 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 1 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 1 },
                ],
              },
              {
                activity_overview_type: 9,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 1 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 1 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 1 },
                ],
              },
              {
                activity_overview_type: 10,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 11,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 12,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 10000 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 10000 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 10000 },
                ],
              },
              {
                activity_overview_type: 13,
                activity_overview_detail_list: [
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
            ],
          },
          activity_persona: {
            user_persona_survey_category_list: [
              {
                item_type: 2,
                number: 0,
                percent: '66.67%',
                user_persona_type: 1,
                label: '女性',
              },
              {
                item_type: 7,
                number: 0,
                percent: '33.33%',
                user_persona_type: 2,
                label: '45-49岁',
              },
              {
                item_type: 440000,
                number: 0,
                percent: '66.67%',
                user_persona_type: 3,
                label: '广东省',
              },
              {
                item_type: 440300,
                number: 0,
                percent: '66.67%',
                user_persona_type: 4,
                label: '深圳市',
              },
              {
                item_type: 7,
                number: 0,
                percent: '33.33%',
                user_persona_type: 5,
                label: '￥500-999',
              },
              {
                item_type: 55,
                number: 0,
                percent: '33.33%',
                user_persona_type: 6,
                label: '糕点面包',
              },
              {
                item_type: 7,
                number: 0,
                percent: '33.33%',
                user_persona_type: 7,
                label: '晚上（21:00 - 23:59）',
              },
            ],
          },
          active_compass_rule_period: {
            active_begin_time: 20240901,
            active_end_time: 20240930,
          },
          active_compass_rule: { brand_version: 1 },
          real_date: { begin_date: '2024-09-24', end_date: '2024-09-30' },
        },
      };
    },
  },
  {
    url: '/xdc/brandmanageweb/v1/activity',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          total_count: 81,
          activity_info_list: [
            {
              act_id: 131,
              act_name: 'wade测试活动名称',
              act_description: '活动介绍活动介绍活动介绍',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727712000,
                delivery_end_time: 1729094399,
                activity_label: [
                  [
                    { label_type: 1, label_value: '3', label_display: '[25,30)' },
                    { label_type: 1, label_value: '4', label_display: '[30,35)' },
                    { label_type: 1, label_value: '5', label_display: '[35,40)' },
                  ],
                  [{ label_type: 2, label_value: '2', label_display: '女' }],
                  [
                    { label_type: 3, label_value: '130426', label_display: '涉县' },
                    { label_type: 3, label_value: '130427', label_display: '磁县' },
                    { label_type: 3, label_value: '130428', label_display: '未知' },
                    { label_type: 3, label_value: '130429', label_display: '未知' },
                    { label_type: 3, label_value: '130430', label_display: '邱县' },
                    { label_type: 3, label_value: '130431', label_display: '鸡泽县' },
                    { label_type: 3, label_value: '130432', label_display: '广平县' },
                    { label_type: 3, label_value: '130433', label_display: '馆陶县' },
                    { label_type: 3, label_value: '130434', label_display: '魏县' },
                    { label_type: 3, label_value: '130435', label_display: '曲周县' },
                    { label_type: 3, label_value: '130481', label_display: '武安市' },
                    { label_type: 3, label_value: '130500', label_display: '邢台市' },
                    { label_type: 3, label_value: '130502', label_display: '桥东区' },
                    { label_type: 3, label_value: '130503', label_display: '桥西区' },
                    { label_type: 3, label_value: '130521', label_display: '邢台县' },
                    { label_type: 3, label_value: '130522', label_display: '临城县' },
                    { label_type: 3, label_value: '130523', label_display: '内丘县' },
                    { label_type: 3, label_value: '130524', label_display: '柏乡县' },
                    { label_type: 3, label_value: '130525', label_display: '隆尧县' },
                    { label_type: 3, label_value: '130526', label_display: '任县' },
                    { label_type: 3, label_value: '130527', label_display: '南和县' },
                    { label_type: 3, label_value: '130528', label_display: '宁晋县' },
                    { label_type: 3, label_value: '130529', label_display: '巨鹿县' },
                    { label_type: 3, label_value: '130530', label_display: '新河县' },
                  ],
                  [
                    { label_type: 6, label_value: '2', label_display: '交易频次提升' },
                    { label_type: 6, label_value: '3', label_display: '交易频次下降' },
                    { label_type: 6, label_value: '4', label_display: '回流用户' },
                    { label_type: 6, label_value: '1', label_display: '交易频次稳定' },
                  ],
                  [
                    { label_type: 7, label_value: '4', label_display: '流失人群' },
                    { label_type: 7, label_value: '1', label_display: '高频交易用户' },
                  ],
                  [
                    { label_type: 8, label_value: '4', label_display: '交易用户' },
                    { label_type: 8, label_value: '2', label_display: '复购老用户' },
                    { label_type: 8, label_value: '1', label_display: '首购新用户' },
                    { label_type: 8, label_value: '3', label_display: '潜在用户' },
                  ],
                  [
                    {
                      label_type: 11,
                      label_value: '2',
                      label_display: '清晨（6:00 - 8:59）',
                    },
                    {
                      label_type: 11,
                      label_value: '3',
                      label_display: '上午（9:00 - 11:59）',
                    },
                    {
                      label_type: 11,
                      label_value: '4',
                      label_display: '中午（12:00 - 13:59）',
                    },
                  ],
                  [{ label_type: 12, label_value: '2', label_display: '非拼单爱好人群' }],
                  [
                    { label_type: 14, label_value: '230207', label_display: '碾子山区' },
                    {
                      label_type: 14,
                      label_value: '230208',
                      label_display: '梅里斯达斡尔族区',
                    },
                    { label_type: 14, label_value: '230221', label_display: '龙江县' },
                    { label_type: 14, label_value: '230223', label_display: '依安县' },
                    { label_type: 14, label_value: '230224', label_display: '泰来县' },
                    { label_type: 14, label_value: '230225', label_display: '甘南县' },
                    { label_type: 14, label_value: '230227', label_display: '富裕县' },
                    { label_type: 14, label_value: '230229', label_display: '克山县' },
                    { label_type: 14, label_value: '230230', label_display: '克东县' },
                    { label_type: 14, label_value: '230231', label_display: '拜泉县' },
                    { label_type: 14, label_value: '230281', label_display: '讷河市' },
                    { label_type: 14, label_value: '230300', label_display: '鸡西市' },
                    { label_type: 14, label_value: '230302', label_display: '鸡冠区' },
                    { label_type: 14, label_value: '230303', label_display: '恒山区' },
                    { label_type: 14, label_value: '230304', label_display: '滴道区' },
                    { label_type: 14, label_value: '230305', label_display: '梨树区' },
                    { label_type: 14, label_value: '230306', label_display: '城子河区' },
                    { label_type: 14, label_value: '230307', label_display: '麻山区' },
                    { label_type: 14, label_value: '230321', label_display: '鸡东县' },
                    { label_type: 14, label_value: '230381', label_display: '虎林市' },
                    { label_type: 14, label_value: '230382', label_display: '密山市' },
                    { label_type: 14, label_value: '230400', label_display: '鹤岗市' },
                    { label_type: 14, label_value: '230402', label_display: '向阳区' },
                    { label_type: 14, label_value: '230403', label_display: '工农区' },
                    { label_type: 14, label_value: '230404', label_display: '南山区' },
                    { label_type: 14, label_value: '230405', label_display: '兴安区' },
                    { label_type: 14, label_value: '230406', label_display: '东山区' },
                    { label_type: 14, label_value: '230407', label_display: '兴山区' },
                    { label_type: 14, label_value: '230421', label_display: '萝北县' },
                    { label_type: 14, label_value: '230422', label_display: '绥滨县' },
                    { label_type: 14, label_value: '230500', label_display: '双鸭山市' },
                  ],
                ],
              },
              create_time: 1727678587,
              modify_time: 1727678587,
              stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
            },
            {
              act_id: 129,
              act_name: '14名称活',
              act_description: 'zzcccc',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727625600,
                delivery_end_time: 1728230399,
                activity_label: [
                  [
                    { label_type: 3, label_value: '54123213', label_display: '未知' },
                    { label_type: 3, label_value: '54', label_display: '未知' },
                  ],
                  [
                    {
                      label_type: 11,
                      label_value: '1',
                      label_display: '凌晨（0:00 - 5:59）',
                    },
                  ],
                  [{ label_type: 12, label_display: '未知' }],
                  [
                    { label_type: 14, label_value: '1442', label_display: '未知' },
                    { label_type: 14, label_value: '1200', label_display: '未知' },
                  ],
                ],
              },
              create_time: 1727676182,
              modify_time: 1727676182,
              stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
            },
            {
              act_id: 128,
              act_name: '活动名称活动名称活',
              act_description: '活动介绍活动介绍活动介绍活动介绍',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727712000,
                delivery_end_time: 1729007999,
                activity_label: [
                  [
                    { label_type: 1, label_value: '2', label_display: '[18,25)' },
                    { label_type: 1, label_value: '6', label_display: '[40,45)' },
                  ],
                  [
                    { label_type: 3, label_value: '110108', label_display: '海淀区' },
                    { label_type: 3, label_value: '110109', label_display: '门头沟区' },
                    { label_type: 3, label_value: '110111', label_display: '房山区' },
                    { label_type: 3, label_value: '110112', label_display: '通州区' },
                    { label_type: 3, label_value: '110113', label_display: '顺义区' },
                    { label_type: 3, label_value: '110114', label_display: '昌平区' },
                    { label_type: 3, label_value: '110115', label_display: '大兴区' },
                    { label_type: 3, label_value: '110116', label_display: '怀柔区' },
                    { label_type: 3, label_value: '110117', label_display: '平谷区' },
                    { label_type: 3, label_value: '110118', label_display: '密云区' },
                    { label_type: 3, label_value: '110119', label_display: '延庆区' },
                    { label_type: 3, label_value: '120000', label_display: '未知' },
                    { label_type: 3, label_value: '120100', label_display: '未知' },
                    { label_type: 3, label_value: '120101', label_display: '和平区' },
                    { label_type: 3, label_value: '120102', label_display: '河东区' },
                    { label_type: 3, label_value: '120103', label_display: '河西区' },
                    { label_type: 3, label_value: '120104', label_display: '南开区' },
                    { label_type: 3, label_value: '120105', label_display: '河北区' },
                    { label_type: 3, label_value: '120106', label_display: '红桥区' },
                    { label_type: 3, label_value: '120110', label_display: '东丽区' },
                  ],
                  [
                    { label_type: 5, label_value: '4', label_display: '小吃爱好人群' },
                    {
                      label_type: 5,
                      label_value: '12',
                      label_display: '影剧演出爱好人群',
                    },
                  ],
                  [{ label_type: 6, label_value: '4', label_display: '回流用户' }],
                  [{ label_type: 12, label_value: '2', label_display: '非拼单爱好人群' }],
                  [
                    { label_value: '130426', label_display: '未知' },
                    { label_value: '130427', label_display: '未知' },
                    { label_value: '130428', label_display: '未知' },
                    { label_value: '130429', label_display: '未知' },
                    { label_value: '130430', label_display: '未知' },
                    { label_value: '130431', label_display: '未知' },
                    { label_value: '130432', label_display: '未知' },
                    { label_value: '130433', label_display: '未知' },
                    { label_value: '130434', label_display: '未知' },
                    { label_value: '130435', label_display: '未知' },
                    { label_value: '130481', label_display: '未知' },
                    { label_value: '130500', label_display: '未知' },
                    { label_value: '130502', label_display: '未知' },
                    { label_value: '130503', label_display: '未知' },
                    { label_value: '130521', label_display: '未知' },
                    { label_value: '130522', label_display: '未知' },
                    { label_value: '130523', label_display: '未知' },
                    { label_value: '130524', label_display: '未知' },
                    { label_value: '130525', label_display: '未知' },
                    { label_value: '130526', label_display: '未知' },
                    { label_value: '130527', label_display: '未知' },
                    { label_value: '130528', label_display: '未知' },
                    { label_value: '130529', label_display: '未知' },
                    { label_value: '130530', label_display: '未知' },
                  ],
                ],
              },
              create_time: 1727672451,
              modify_time: 1727672453,
              stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
            },
            {
              act_id: 127,
              act_name: '活动名称活动名称',
              act_description:
                '活动介绍活动介绍\n活动介绍\n活动介绍活动介绍活动介绍活动介绍',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727712000,
                delivery_end_time: 1729094399,
                activity_label: [
                  [
                    { label_type: 1, label_value: '3', label_display: '[25,30)' },
                    { label_type: 1, label_value: '7', label_display: '[45,50)' },
                    { label_type: 1, label_value: '8', label_display: '[50,55)' },
                  ],
                  [{ label_type: 2, label_value: '2', label_display: '女' }],
                  [
                    {
                      label_type: 3,
                      label_value: '1213213，12314124',
                      label_display: '未知',
                    },
                    { label_type: 3, label_value: '111', label_display: '未知' },
                  ],
                  [{ label_type: 5, label_value: '0', label_display: '未知' }],
                  [{ label_type: 6, label_value: '1', label_display: '交易频次稳定' }],
                  [{ label_type: 7, label_value: '4', label_display: '流失人群' }],
                  [{ label_type: 12, label_value: '1', label_display: '是拼单爱好人群' }],
                  [
                    { label_value: '1213213，12314124', label_display: '未知' },
                    { label_value: '3123', label_display: '未知' },
                    { label_value: '111', label_display: '未知' },
                    { label_value: '11111111', label_display: '未知' },
                  ],
                ],
              },
              create_time: 1727665594,
              modify_time: 1727665595,
              stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
            },
            {
              act_id: 125,
              act_name: '111',
              act_description: '111',
              act_status: 4,
              act_delivery_rule: {
                delivery_start_time: 1727452800,
                delivery_end_time: 1727711999,
                activity_label: [],
              },
              create_time: 1727619739,
              modify_time: 1727678636,
              stock_list: [{ stock_id: '1272610000117545', stock_name: 'xigua' }],
            },
            {
              act_id: 124,
              act_name: '123',
              act_description: '123',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727452800,
                delivery_end_time: 1727711999,
                activity_label: [],
              },
              create_time: 1727619679,
              modify_time: 1727619679,
              stock_list: [{ stock_id: '1272610000117545', stock_name: 'xigua' }],
            },
            {
              act_id: 123,
              act_name: '341',
              act_description: '124',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727280000,
                delivery_end_time: 1727625599,
                activity_label: [],
              },
              create_time: 1727619574,
              modify_time: 1727619574,
              stock_list: [{ stock_id: '1272610000117545', stock_name: 'xigua' }],
            },
            {
              act_id: 122,
              act_name: '真实活动,非常牛',
              act_description:
                '这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的牛牛牛牛测试,只要你的字符数量小于600,你就可以添加非常爆炸多多信息这是一个非常厉害的',
              act_status: 4,
              act_delivery_rule: {
                delivery_start_time: 1727280000,
                delivery_end_time: 1729439999,
                activity_label: [],
              },
              create_time: 1727614372,
              modify_time: 1727617562,
              stock_list: [{ stock_id: '1272610000117545', stock_name: 'xigua' }],
            },
            {
              act_id: 121,
              act_name: 'wade测试1940',
              act_description:
                '活动介绍活动介绍活动介绍\n活动介绍\n活动介绍\n活动介绍活动介绍活动介绍',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727712000,
                delivery_end_time: 1729094399,
                activity_label: [
                  [{ label_type: 1, label_value: '2', label_display: '[18,25)' }],
                  [{ label_type: 7, label_value: '1', label_display: '高频交易用户' }],
                  [{ label_type: 8, label_value: '1', label_display: '首购新用户' }],
                  [{ label_type: 12, label_value: '2', label_display: '非拼单爱好人群' }],
                ],
              },
              create_time: 1727612707,
              modify_time: 1727612707,
              stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
            },
            {
              act_id: 116,
              act_name: 'wade测试155',
              act_description: '活动介绍活动介绍活动介绍活动介绍',
              act_status: 2,
              act_delivery_rule: {
                delivery_start_time: 1727712000,
                delivery_end_time: 1729094399,
                activity_label: [
                  [{ label_type: 7, label_value: '2', label_display: '低频交易用户' }],
                  [{ label_type: 8, label_value: '2', label_display: '复购老用户' }],
                  [{ label_type: 12, label_value: '2', label_display: '非拼单爱好人群' }],
                ],
              },
              create_time: 1727596539,
              modify_time: 1727596539,
              stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
            },
          ],
        },
      };
    },
  },
  {
    url: '/xdc/brandmanageweb/v1/compass-activity-detail/overview',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          activity_detail_overview: {
            activity_overview_statistics_list: [
              {
                activity_overview_type: 1,
                overview_statistics_num: 104000,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 2,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 3,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 4,
                overview_statistics_num: 4100,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 5,
                overview_statistics_num: 52,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 6,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 7,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 8,
                overview_statistics_num: 1633,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 9,
                overview_statistics_num: 26,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 10,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 11,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 12,
                overview_statistics_num: 127,
                overview_compare_last_cycle: '-',
              },
              {
                activity_overview_type: 13,
                overview_statistics_num: 0,
                overview_compare_last_cycle: '-',
              },
            ],
            activity_overview_trend_list: [
              {
                activity_overview_type: 1,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 40000 },
                  { time_date: 20240927, user_num: 28000 },
                  { time_date: 20240928, user_num: 20000 },
                  { time_date: 20240929, user_num: 12000 },
                  { time_date: 20240930, user_num: 4000 },
                ],
              },
              {
                activity_overview_type: 2,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 3,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 4,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 706 },
                  { time_date: 20240919, user_num: 758 },
                  { time_date: 20240920, user_num: 772 },
                  { time_date: 20240921, user_num: 570 },
                  { time_date: 20240922, user_num: 616 },
                  { time_date: 20240923, user_num: 324 },
                  { time_date: 20240924, user_num: 198 },
                  { time_date: 20240925, user_num: 52 },
                  { time_date: 20240926, user_num: 38 },
                  { time_date: 20240927, user_num: 30 },
                  { time_date: 20240928, user_num: 20 },
                  { time_date: 20240929, user_num: 12 },
                  { time_date: 20240930, user_num: 4 },
                ],
              },
              {
                activity_overview_type: 5,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 20 },
                  { time_date: 20240927, user_num: 14 },
                  { time_date: 20240928, user_num: 10 },
                  { time_date: 20240929, user_num: 6 },
                  { time_date: 20240930, user_num: 2 },
                ],
              },
              {
                activity_overview_type: 6,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 7,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 8,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 275 },
                  { time_date: 20240919, user_num: 311 },
                  { time_date: 20240920, user_num: 319 },
                  { time_date: 20240921, user_num: 231 },
                  { time_date: 20240922, user_num: 265 },
                  { time_date: 20240923, user_num: 129 },
                  { time_date: 20240924, user_num: 64 },
                  { time_date: 20240925, user_num: 13 },
                  { time_date: 20240926, user_num: 10 },
                  { time_date: 20240927, user_num: 7 },
                  { time_date: 20240928, user_num: 5 },
                  { time_date: 20240929, user_num: 3 },
                  { time_date: 20240930, user_num: 1 },
                ],
              },
              {
                activity_overview_type: 9,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 10 },
                  { time_date: 20240927, user_num: 7 },
                  { time_date: 20240928, user_num: 5 },
                  { time_date: 20240929, user_num: 3 },
                  { time_date: 20240930, user_num: 1 },
                ],
              },
              {
                activity_overview_type: 10,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 11,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
              {
                activity_overview_type: 12,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 5263 },
                  { time_date: 20240927, user_num: 4667 },
                  { time_date: 20240928, user_num: 5000 },
                  { time_date: 20240929, user_num: 5000 },
                  { time_date: 20240930, user_num: 5000 },
                ],
              },
              {
                activity_overview_type: 13,
                activity_overview_detail_list: [
                  { time_date: 20240908, user_num: 0 },
                  { time_date: 20240909, user_num: 0 },
                  { time_date: 20240910, user_num: 0 },
                  { time_date: 20240911, user_num: 0 },
                  { time_date: 20240912, user_num: 0 },
                  { time_date: 20240913, user_num: 0 },
                  { time_date: 20240914, user_num: 0 },
                  { time_date: 20240915, user_num: 0 },
                  { time_date: 20240916, user_num: 0 },
                  { time_date: 20240917, user_num: 0 },
                  { time_date: 20240918, user_num: 0 },
                  { time_date: 20240919, user_num: 0 },
                  { time_date: 20240920, user_num: 0 },
                  { time_date: 20240921, user_num: 0 },
                  { time_date: 20240922, user_num: 0 },
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 0 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 0 },
                  { time_date: 20240929, user_num: 0 },
                  { time_date: 20240930, user_num: 0 },
                ],
              },
            ],
          },
          active_compass_rule_period: {
            active_begin_time: 20240901,
            active_end_time: 20240930,
          },
          real_date: { begin_date: '2024-09-08', end_date: '2024-10-07' },
        },
      };
    },
  },
  {
    url: '/xdc/brandmanageweb/v1/activity/:activityId',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          activity_info: {
            act_id: 131,
            act_name: 'wade测试活动名称',
            act_description: '活动介绍活动介绍活动介绍',
            act_status: 2,
            act_delivery_rule: {
              delivery_start_time: 1727712000,
              delivery_end_time: 1729094399,
              activity_label: [
                [
                  { label_type: 1, label_value: '3', label_display: '[25,30)' },
                  { label_type: 1, label_value: '4', label_display: '[30,35)' },
                  { label_type: 1, label_value: '5', label_display: '[35,40)' },
                ],
                [{ label_type: 2, label_value: '2', label_display: '女' }],
                [
                  { label_type: 3, label_value: '130426', label_display: '涉县' },
                  { label_type: 3, label_value: '130427', label_display: '磁县' },
                  { label_type: 3, label_value: '130428', label_display: '未知' },
                  { label_type: 3, label_value: '130429', label_display: '未知' },
                  { label_type: 3, label_value: '130430', label_display: '邱县' },
                  { label_type: 3, label_value: '130431', label_display: '鸡泽县' },
                  { label_type: 3, label_value: '130432', label_display: '广平县' },
                  { label_type: 3, label_value: '130433', label_display: '馆陶县' },
                  { label_type: 3, label_value: '130434', label_display: '魏县' },
                  { label_type: 3, label_value: '130435', label_display: '曲周县' },
                  { label_type: 3, label_value: '130481', label_display: '武安市' },
                  { label_type: 3, label_value: '130500', label_display: '邢台市' },
                  { label_type: 3, label_value: '130502', label_display: '桥东区' },
                  { label_type: 3, label_value: '130503', label_display: '桥西区' },
                  { label_type: 3, label_value: '130521', label_display: '邢台县' },
                  { label_type: 3, label_value: '130522', label_display: '临城县' },
                  { label_type: 3, label_value: '130523', label_display: '内丘县' },
                  { label_type: 3, label_value: '130524', label_display: '柏乡县' },
                  { label_type: 3, label_value: '130525', label_display: '隆尧县' },
                  { label_type: 3, label_value: '130526', label_display: '任县' },
                  { label_type: 3, label_value: '130527', label_display: '南和县' },
                  { label_type: 3, label_value: '130528', label_display: '宁晋县' },
                  { label_type: 3, label_value: '130529', label_display: '巨鹿县' },
                  { label_type: 3, label_value: '130530', label_display: '新河县' },
                ],
                [
                  { label_type: 6, label_value: '2', label_display: '交易频次提升' },
                  { label_type: 6, label_value: '3', label_display: '交易频次下降' },
                  { label_type: 6, label_value: '4', label_display: '回流用户' },
                  { label_type: 6, label_value: '1', label_display: '交易频次稳定' },
                ],
                [
                  { label_type: 7, label_value: '4', label_display: '流失人群' },
                  { label_type: 7, label_value: '1', label_display: '高频交易用户' },
                ],
                [
                  { label_type: 8, label_value: '4', label_display: '交易用户' },
                  { label_type: 8, label_value: '2', label_display: '复购老用户' },
                  { label_type: 8, label_value: '1', label_display: '首购新用户' },
                  { label_type: 8, label_value: '3', label_display: '潜在用户' },
                ],
                [
                  {
                    label_type: 11,
                    label_value: '2',
                    label_display: '清晨（6:00 - 8:59）',
                  },
                  {
                    label_type: 11,
                    label_value: '3',
                    label_display: '上午（9:00 - 11:59）',
                  },
                  {
                    label_type: 11,
                    label_value: '4',
                    label_display: '中午（12:00 - 13:59）',
                  },
                ],
                [{ label_type: 12, label_value: '2', label_display: '非拼单爱好人群' }],
                [
                  { label_type: 14, label_value: '230207', label_display: '碾子山区' },
                  {
                    label_type: 14,
                    label_value: '230208',
                    label_display: '梅里斯达斡尔族区',
                  },
                  { label_type: 14, label_value: '230221', label_display: '龙江县' },
                  { label_type: 14, label_value: '230223', label_display: '依安县' },
                  { label_type: 14, label_value: '230224', label_display: '泰来县' },
                  { label_type: 14, label_value: '230225', label_display: '甘南县' },
                  { label_type: 14, label_value: '230227', label_display: '富裕县' },
                  { label_type: 14, label_value: '230229', label_display: '克山县' },
                  { label_type: 14, label_value: '230230', label_display: '克东县' },
                  { label_type: 14, label_value: '230231', label_display: '拜泉县' },
                  { label_type: 14, label_value: '230281', label_display: '讷河市' },
                  { label_type: 14, label_value: '230300', label_display: '鸡西市' },
                  { label_type: 14, label_value: '230302', label_display: '鸡冠区' },
                  { label_type: 14, label_value: '230303', label_display: '恒山区' },
                  { label_type: 14, label_value: '230304', label_display: '滴道区' },
                  { label_type: 14, label_value: '230305', label_display: '梨树区' },
                  { label_type: 14, label_value: '230306', label_display: '城子河区' },
                  { label_type: 14, label_value: '230307', label_display: '麻山区' },
                  { label_type: 14, label_value: '230321', label_display: '鸡东县' },
                  { label_type: 14, label_value: '230381', label_display: '虎林市' },
                  { label_type: 14, label_value: '230382', label_display: '密山市' },
                  { label_type: 14, label_value: '230400', label_display: '鹤岗市' },
                  { label_type: 14, label_value: '230402', label_display: '向阳区' },
                  { label_type: 14, label_value: '230403', label_display: '工农区' },
                  { label_type: 14, label_value: '230404', label_display: '南山区' },
                  { label_type: 14, label_value: '230405', label_display: '兴安区' },
                  { label_type: 14, label_value: '230406', label_display: '东山区' },
                  { label_type: 14, label_value: '230407', label_display: '兴山区' },
                  { label_type: 14, label_value: '230421', label_display: '萝北县' },
                  { label_type: 14, label_value: '230422', label_display: '绥滨县' },
                  { label_type: 14, label_value: '230500', label_display: '双鸭山市' },
                ],
              ],
            },
            create_time: 1727678587,
            modify_time: 1727678587,
            stock_list: [{ stock_id: '21720929', stock_name: '摇一摇活动券' }],
          },
        },
      };
    },
  },
  {
    url: '/xdc/brandmanageweb/v1/compass-activity/persona-detail',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          activity_persona_detail: {
            user_persona_detail_category_list: [
              {
                user_persona_type: 1,
                user_persona_detail_content_list: [
                  { item_type: 2, percent: '66.67%', rank: 1, label: '女性' },
                  { item_type: 1, percent: '33.33%', rank: 2, label: '男性' },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
              {
                user_persona_type: 2,
                user_persona_detail_content_list: [
                  { item_type: 3, percent: '33.33%', rank: 1, label: '25-29岁' },
                  { item_type: 6, percent: '33.33%', rank: 2, label: '40-44岁' },
                  { item_type: 7, percent: '33.33%', rank: 3, label: '45-49岁' },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
              {
                user_persona_type: 3,
                user_persona_detail_content_list: [
                  { item_type: 440000, percent: '66.67%', rank: 1, label: '广东省' },
                  { item_type: 510000, percent: '33.33%', rank: 2, label: '四川省' },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
              {
                user_persona_type: 4,
                user_persona_detail_content_list: [
                  { item_type: 440300, percent: '66.67%', rank: 1, label: '深圳市' },
                  { item_type: 511000, percent: '33.33%', rank: 2, label: '内江市' },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
              {
                user_persona_type: 5,
                user_persona_detail_content_list: [
                  { item_type: 5, percent: '33.33%', rank: 1, label: '￥100-199' },
                  { item_type: 6, percent: '33.33%', rank: 2, label: '￥200-499' },
                  { item_type: 7, percent: '33.33%', rank: 3, label: '￥500-999' },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
              {
                user_persona_type: 6,
                user_persona_detail_content_list: [
                  { item_type: 41, percent: '33.33%', rank: 1, label: '商圈' },
                  { item_type: 51, percent: '33.33%', rank: 2, label: '咖啡' },
                  { item_type: 55, percent: '33.33%', rank: 3, label: '糕点面包' },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
              {
                user_persona_type: 7,
                user_persona_detail_content_list: [
                  {
                    item_type: 2,
                    percent: '33.33%',
                    rank: 1,
                    label: '清晨（6:00 - 8:59）',
                  },
                  {
                    item_type: 6,
                    percent: '33.33%',
                    rank: 2,
                    label: '傍晚（18:00 - 20:59）',
                  },
                  {
                    item_type: 7,
                    percent: '33.33%',
                    rank: 3,
                    label: '晚上（21:00 - 23:59）',
                  },
                  { item_type: 0, percent: '0', label: '未知' },
                ],
              },
            ],
          },
        },
      };
    },
  },
] as MockMethod[];
