import { MockMethod } from 'vite-plugin-mock';

// 直接定义baseUrl，避免导入src文件造成依赖问题
const baseUrl = '/xdc/mchcardinfomgrweb';

export default [
  {
    url: '/compass-business/overview',
    method: 'get',
    response: ({ query }) => {
      return {
        code: 0,
        message: 'string',
        data: {
          trade_overview: {
            business_overview_statistics_list: [
              {
                business_overview_type: 1,
                overview_statistics_num: 27082977,
                overview_compare_last_cycle: '+32.00%',
              },
              {
                business_overview_type: 3,
                overview_statistics_num: 2675,
                overview_compare_last_cycle: '+35.92%',
              },
              {
                business_overview_type: 4,
                overview_statistics_num: 2380,
                overview_compare_last_cycle: '+30.70%',
              },
              {
                business_overview_type: 2,
                overview_statistics_num: 11379,
                overview_compare_last_cycle: '+1.00%',
              },
            ],
            business_overview_trend_list: [
              {
                business_overview_type: 1,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 12440538 },
                  { time_date: 20240924, user_num: 3535014 },
                  { time_date: 20240925, user_num: 2578831 },
                  { time_date: 20240926, user_num: 2627563 },
                  { time_date: 20240927, user_num: 1929809 },
                  { time_date: 20240928, user_num: 1589127 },
                  { time_date: 20240929, user_num: 2382095 },
                ],
              },
              {
                business_overview_type: 3,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 781 },
                  { time_date: 20240924, user_num: 408 },
                  { time_date: 20240925, user_num: 352 },
                  { time_date: 20240926, user_num: 344 },
                  { time_date: 20240927, user_num: 259 },
                  { time_date: 20240928, user_num: 224 },
                  { time_date: 20240929, user_num: 307 },
                ],
              },
              {
                business_overview_type: 4,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 729 },
                  { time_date: 20240924, user_num: 356 },
                  { time_date: 20240925, user_num: 297 },
                  { time_date: 20240926, user_num: 320 },
                  { time_date: 20240927, user_num: 222 },
                  { time_date: 20240928, user_num: 200 },
                  { time_date: 20240929, user_num: 256 },
                ],
              },
              {
                business_overview_type: 2,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 17065 },
                  { time_date: 20240924, user_num: 9929 },
                  { time_date: 20240925, user_num: 8682 },
                  { time_date: 20240926, user_num: 8211 },
                  { time_date: 20240927, user_num: 8692 },
                  { time_date: 20240928, user_num: 7945 },
                  { time_date: 20240929, user_num: 9305 },
                ],
              },
            ],
          },
          market_overview: {
            business_overview_statistics_list: [
              {
                business_overview_type: 5,
                overview_statistics_num: 1380,
                overview_compare_last_cycle: '-33.33%',
              },
              {
                business_overview_type: 6,
                overview_statistics_num: 2,
                overview_compare_last_cycle: '0',
              },
              {
                business_overview_type: 7,
                overview_statistics_num: 842,
                overview_compare_last_cycle: '+17.11%',
              },
              {
                business_overview_type: 8,
                overview_statistics_num: 2,
                overview_compare_last_cycle: '0',
              },
            ],
            business_overview_trend_list: [
              {
                business_overview_type: 5,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 690 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 690 },
                  { time_date: 20240929, user_num: 0 },
                ],
              },
              {
                business_overview_type: 6,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 1 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 1 },
                  { time_date: 20240929, user_num: 0 },
                ],
              },
              {
                business_overview_type: 7,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 95 },
                  { time_date: 20240924, user_num: 102 },
                  { time_date: 20240925, user_num: 106 },
                  { time_date: 20240926, user_num: 119 },
                  { time_date: 20240927, user_num: 123 },
                  { time_date: 20240928, user_num: 159 },
                  { time_date: 20240929, user_num: 138 },
                ],
              },
              {
                business_overview_type: 8,
                business_overview_detail_list: [
                  { time_date: 20240923, user_num: 0 },
                  { time_date: 20240924, user_num: 1 },
                  { time_date: 20240925, user_num: 0 },
                  { time_date: 20240926, user_num: 0 },
                  { time_date: 20240927, user_num: 0 },
                  { time_date: 20240928, user_num: 1 },
                  { time_date: 20240929, user_num: 0 },
                ],
              },
            ],
          },
          business_suggest: {
            optimization_list: [
              {
                optimization_type: 2,
                content:
                  '参与同行热门活动「摇一摇有优惠」，针对「潜在用户」投放提频优惠券，提升新客转化',
                button_content: '创建活动',
                empty_url_content: '当前仅可运营代创建，请联系对接运营',
              },
              {
                optimization_type: 2,
                content:
                  '参与同行业热门活动「摇一摇有优惠」，针对「沉默用户、流失用户」投放召回优惠券，召回老客',
                button_content: '创建活动',
                empty_url_content: '当前仅可运营代创建，请联系对接运营',
              },
              {
                optimization_type: 2,
                content: '在「支付有礼」针对「低频用户」投放提频效果好的优惠券，提升频次',
                button_content: '创建活动',
                button_url:
                  'https://pay.weixin.qq.com/index.php/xphp/cpayment_polite/pay_gift#/pages/create/create',
              },
              {
                optimization_type: 2,
                content:
                  '在「支付有礼」针对「首购新用户」投放复购效果较好的优惠券，提升复购',
                button_content: '创建活动',
                button_url:
                  'https://pay.weixin.qq.com/index.php/xphp/cpayment_polite/pay_gift#/pages/create/create',
              },
              {
                optimization_type: 2,
                content:
                  '在「支付有礼」针对「高频用户」适时发放全场满减券，提升客单价、维系客户忠诚度',
                button_content: '创建活动',
                button_url:
                  'https://pay.weixin.qq.com/index.php/xphp/cpayment_polite/pay_gift#/pages/create/create',
              },
            ],
          },
          active_compass_rule: { brand_version: 1 },
          active_compass_rule_period: {
            active_begin_time: 20240901,
            active_end_time: 20240929,
          },
          real_date: { begin_date: '2024-09-23', end_date: '2024-09-29' },
        },
      };
    },
  },
  {
    url: '/compass-business/overview/:businessType',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          business_data: {
            business_overview_statistics_list: [
              {
                business_overview_type: 1,
                overview_statistics_num: 185030974,
                overview_compare_last_cycle: '-',
              },
              {
                business_overview_type: 3,
                overview_statistics_num: 12762,
                overview_compare_last_cycle: '-',
              },
              {
                business_overview_type: 4,
                overview_statistics_num: 10951,
                overview_compare_last_cycle: '-',
              },
              {
                business_overview_type: 2,
                overview_statistics_num: 16896,
                overview_compare_last_cycle: '-',
              },
            ],
            business_overview_trend_list: [
              {
                business_overview_type: 1,
                business_overview_detail_list: [
                  { time_date: 20240901, user_num: 2658848 },
                  { time_date: 20240902, user_num: 1557063 },
                  { time_date: 20240903, user_num: 1390379 },
                  { time_date: 20240904, user_num: 28805022 },
                  { time_date: 20240905, user_num: 3240448 },
                  { time_date: 20240906, user_num: 3609612 },
                  { time_date: 20240907, user_num: 11484377 },
                  { time_date: 20240908, user_num: 8314480 },
                  { time_date: 20240909, user_num: 8557729 },
                  { time_date: 20240910, user_num: 5932820 },
                  { time_date: 20240911, user_num: 5059459 },
                  { time_date: 20240912, user_num: 28894751 },
                  { time_date: 20240913, user_num: 8732205 },
                  { time_date: 20240914, user_num: 16073200 },
                  { time_date: 20240915, user_num: 3120464 },
                  { time_date: 20240916, user_num: 3480577 },
                  { time_date: 20240917, user_num: 2978767 },
                  { time_date: 20240918, user_num: 2931793 },
                  { time_date: 20240919, user_num: 2760686 },
                  { time_date: 20240920, user_num: 2077587 },
                  { time_date: 20240921, user_num: 1768195 },
                  { time_date: 20240922, user_num: 4519535 },
                  { time_date: 20240923, user_num: 12440538 },
                  { time_date: 20240924, user_num: 3535014 },
                  { time_date: 20240925, user_num: 2578831 },
                  { time_date: 20240926, user_num: 2627563 },
                  { time_date: 20240927, user_num: 1929809 },
                  { time_date: 20240928, user_num: 1589127 },
                  { time_date: 20240929, user_num: 2382095 },
                ],
              },
              {
                business_overview_type: 3,
                business_overview_detail_list: [
                  { time_date: 20240901, user_num: 305 },
                  { time_date: 20240902, user_num: 230 },
                  { time_date: 20240903, user_num: 214 },
                  { time_date: 20240904, user_num: 1351 },
                  { time_date: 20240905, user_num: 669 },
                  { time_date: 20240906, user_num: 503 },
                  { time_date: 20240907, user_num: 753 },
                  { time_date: 20240908, user_num: 422 },
                  { time_date: 20240909, user_num: 669 },
                  { time_date: 20240910, user_num: 266 },
                  { time_date: 20240911, user_num: 248 },
                  { time_date: 20240912, user_num: 1108 },
                  { time_date: 20240913, user_num: 342 },
                  { time_date: 20240914, user_num: 805 },
                  { time_date: 20240915, user_num: 234 },
                  { time_date: 20240916, user_num: 179 },
                  { time_date: 20240917, user_num: 275 },
                  { time_date: 20240918, user_num: 283 },
                  { time_date: 20240919, user_num: 334 },
                  { time_date: 20240920, user_num: 273 },
                  { time_date: 20240921, user_num: 258 },
                  { time_date: 20240922, user_num: 366 },
                  { time_date: 20240923, user_num: 781 },
                  { time_date: 20240924, user_num: 408 },
                  { time_date: 20240925, user_num: 352 },
                  { time_date: 20240926, user_num: 344 },
                  { time_date: 20240927, user_num: 259 },
                  { time_date: 20240928, user_num: 224 },
                  { time_date: 20240929, user_num: 307 },
                ],
              },
              {
                business_overview_type: 4,
                business_overview_detail_list: [
                  { time_date: 20240901, user_num: 254 },
                  { time_date: 20240902, user_num: 197 },
                  { time_date: 20240903, user_num: 192 },
                  { time_date: 20240904, user_num: 918 },
                  { time_date: 20240905, user_num: 595 },
                  { time_date: 20240906, user_num: 458 },
                  { time_date: 20240907, user_num: 635 },
                  { time_date: 20240908, user_num: 357 },
                  { time_date: 20240909, user_num: 549 },
                  { time_date: 20240910, user_num: 212 },
                  { time_date: 20240911, user_num: 205 },
                  { time_date: 20240912, user_num: 1018 },
                  { time_date: 20240913, user_num: 308 },
                  { time_date: 20240914, user_num: 641 },
                  { time_date: 20240915, user_num: 211 },
                  { time_date: 20240916, user_num: 155 },
                  { time_date: 20240917, user_num: 265 },
                  { time_date: 20240918, user_num: 276 },
                  { time_date: 20240919, user_num: 311 },
                  { time_date: 20240920, user_num: 248 },
                  { time_date: 20240921, user_num: 228 },
                  { time_date: 20240922, user_num: 338 },
                  { time_date: 20240923, user_num: 729 },
                  { time_date: 20240924, user_num: 356 },
                  { time_date: 20240925, user_num: 297 },
                  { time_date: 20240926, user_num: 320 },
                  { time_date: 20240927, user_num: 222 },
                  { time_date: 20240928, user_num: 200 },
                  { time_date: 20240929, user_num: 256 },
                ],
              },
              {
                business_overview_type: 2,
                business_overview_detail_list: [
                  { time_date: 20240901, user_num: 10467 },
                  { time_date: 20240902, user_num: 7903 },
                  { time_date: 20240903, user_num: 7241 },
                  { time_date: 20240904, user_num: 31378 },
                  { time_date: 20240905, user_num: 5446 },
                  { time_date: 20240906, user_num: 7881 },
                  { time_date: 20240907, user_num: 18085 },
                  { time_date: 20240908, user_num: 23289 },
                  { time_date: 20240909, user_num: 15587 },
                  { time_date: 20240910, user_num: 27985 },
                  { time_date: 20240911, user_num: 24680 },
                  { time_date: 20240912, user_num: 28383 },
                  { time_date: 20240913, user_num: 28351 },
                  { time_date: 20240914, user_num: 25075 },
                  { time_date: 20240915, user_num: 14788 },
                  { time_date: 20240916, user_num: 22455 },
                  { time_date: 20240917, user_num: 11240 },
                  { time_date: 20240918, user_num: 10622 },
                  { time_date: 20240919, user_num: 8876 },
                  { time_date: 20240920, user_num: 8377 },
                  { time_date: 20240921, user_num: 7755 },
                  { time_date: 20240922, user_num: 13371 },
                  { time_date: 20240923, user_num: 17065 },
                  { time_date: 20240924, user_num: 9929 },
                  { time_date: 20240925, user_num: 8682 },
                  { time_date: 20240926, user_num: 8211 },
                  { time_date: 20240927, user_num: 8692 },
                  { time_date: 20240928, user_num: 7945 },
                  { time_date: 20240929, user_num: 9305 },
                ],
              },
            ],
          },
        },
      };
    },
  },
  {
    url: '/miniprogram/list',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: [
          {
            name: '微信支付',
            appid: 'wx8888888888888888',
          },
          {
            name: '微信读书',
            appid: 'wx9999999999999999',
          },
          {
            name: '微信小程序示例',
            appid: 'wx7777777777777777',
          },
          {
            name: '微信城市服务',
            appid: 'wx6666666666666666',
          },
          {
            name: '微信运动',
            appid: 'wx5555555555555555',
          },
        ],
      };
    },
  },
  {
    url: '/card/card-info',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          cardBrief: '这里是商家名片简介',
          customerService: [
            {
              customerServiceType: 1,
              miniProgram: {
                appid: 'wx8888888888888888',
                username: 'miniprogram_service',
              },
            },
            {
              customerServiceType: 4,
              servicePhone: '13812345678',
            },
          ],
          sceneList: [
            {
              sceneType: 1,
              miniProgram: {
                sceneId: 'scene123',
                appid: 'wx8888888888888888',
                sceneTag: 'mini_program_scene',
                imageList: [
                  'https://tdesign.gtimg.com/demo/demo-image-1.png', // 示例图片：黑色拉布拉多犬
                ],
              },
            },
            {
              sceneType: 2,
              finder: {
                sceneId: 'scene123',
                appid: 'wx8888888888888888',
                sceneTag: 'mini_program_scene',
                imageList: [
                  'https://tdesign.gtimg.com/demo/demo-image-1.png', // 示例图片：黑色拉布拉多犬
                  'https://tdesign.gtimg.com/demo/demo-image-1.png', // 示例图片：黑色拉布拉多犬
                ],
              },
            },
          ],
        },
      };
    },
  },
  {
    url: '/brand/brand-info',
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          brand_logo:
            'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=',
          brand_alias: 'nike_official',
          brand_show_type: 'official_account',
          brand_id: 'brand_123456789',
          brand_name: 'Nike Official',
        },
      };
    },
  },
  {
    url: '/api/merchant/card/config',
    method: 'post',
    response: ({ body }) => {
      // Log request body for debugging
      console.log('Received card config request:', body);
      return {
        code: 0,
        message: 'success',
        data: {},
      };
    },
  },
  {
    url: '/api/upload',
    method: 'post',
    timeout: 1000,
    response: ({ body }) => {
      // Generate mock file response
      const files = body?.files || [];
      const results = files.map((file: any) => ({
        uid: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: file.name || 'mock-file.png',
        status: 'done',
        url: 'https://mock-server.com/uploads/' + (file.name || 'mock-file.png'),
        thumbUrl: 'https://mock-server.com/thumbs/' + (file.name || 'mock-file.png'),
        size: file.size || 1024,
        type: file.type || 'image/png',
      }));

      return {
        code: 0,
        message: 'Upload success',
        data: {
          files: results,
          total: results.length,
        },
      };
    },
  },
  {
    url: '/api/card/stats',
    method: 'get',
    response: ({ query }) => {
      const isRandom = typeof query.date !== 'undefined' ? true : false;
      return {
        code: 0,
        message: 'success',
        data: {
          stats: {
            totalCards: isRandom ? Math.floor(Math.random() * 1000) : 28,
            activeCards: isRandom ? Math.floor(Math.random() * 1000) : 18,
            viewCount: isRandom ? Math.floor(Math.random() * 1000) : 3562,
            conversionRate: isRandom ? Math.floor(Math.random() * 100) : 5.8,
          },
        },
      };
    },
  },
  {
    url: '/api/card/list',
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10, status = '' } = query;

      // 生成模拟数据
      const mockCards = Array.from({ length: 30 }).map((_, index) => {
        const id = index + 1;
        const isActive =
          status === 'active' ? true : status === 'inactive' ? false : id % 3 !== 0; // 对于全部状态，每3个有1个是非活跃的

        return {
          id,
          title: `商家名片 ${id}`,
          description: `这是商家名片${id}的简要描述`,
          status: isActive ? 'active' : 'inactive',
          createTime: `2023-${Math.floor(id % 12) + 1}-${Math.floor((id % 28) + 1)}`,
          viewCount: Math.floor(Math.random() * 1000),
          conversionCount: Math.floor(Math.random() * 100),
          conversionRate: (Math.random() * 10).toFixed(1),
        };
      });

      // 根据状态筛选
      const filteredCards = status
        ? mockCards.filter((card) => card.status === status)
        : mockCards;

      // 分页
      const startIndex = (Number(page) - 1) * Number(pageSize);
      const endIndex = startIndex + Number(pageSize);
      const paginatedCards = filteredCards.slice(startIndex, endIndex);

      return {
        code: 0,
        message: 'success',
        data: {
          list: paginatedCards,
          total: filteredCards.length,
        },
      };
    },
  },
  // 处理仪表盘初始请求，合并卡片统计和列表数据
  {
    url: '/api/dashboard',
    method: 'get',
    response: ({ query }) => {
      const { page = 1, pageSize = 10 } = query;

      // 生成卡片统计数据
      const statsData = {
        stats: {
          totalCards: 28,
          activeCards: 18,
          viewCount: 3562,
          conversionRate: 5.8,
        },
      };

      // 生成卡片列表数据
      const mockCards = Array.from({ length: 30 }).map((_, index) => {
        const id = index + 1;
        const isActive = id % 3 !== 0; // 每3个有1个是非活跃的

        return {
          id,
          title: `商家名片 ${id}`,
          description: `这是商家名片${id}的简要描述`,
          status: isActive ? 'active' : 'inactive',
          createTime: `2023-${Math.floor(id % 12) + 1}-${Math.floor((id % 28) + 1)}`,
          viewCount: Math.floor(Math.random() * 1000),
          conversionCount: Math.floor(Math.random() * 100),
          conversionRate: (Math.random() * 10).toFixed(1),
        };
      });

      // 分页
      const startIndex = (Number(page) - 1) * Number(pageSize);
      const endIndex = startIndex + Number(pageSize);
      const paginatedCards = mockCards.slice(startIndex, endIndex);

      const listData = {
        list: paginatedCards,
        total: mockCards.length,
      };

      return {
        code: 0,
        message: 'success',
        data: [statsData, listData],
      };
    },
  },
  {
    url: '/api/card/activate',
    method: 'post',
    response: ({ body }) => {
      const { cardId } = body;
      return {
        code: 0,
        message: 'success',
        data: {
          id: cardId,
          status: 'active',
        },
      };
    },
  },
  {
    url: '/api/card/deactivate',
    method: 'post',
    response: ({ body }) => {
      const { cardId } = body;
      return {
        code: 0,
        message: 'success',
        data: {
          id: cardId,
          status: 'inactive',
        },
      };
    },
  },
  {
    url: '/api/merchant/card/approve',
    method: 'get',
    response: ({}) => {
      return {
        code: 1,
        message: 'fail',
        status: 0,
      };
    },
  },
  {
    url: `${baseUrl}/get-publish-preview-code`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          previewId: '1',
          previewCode: 'https://tailwindcss.com/docs/padding',
          expireTime: '1752659309',
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-draw-preview-code`,
    method: 'post',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          previewId: '1',
          previewCode: 'https://tailwindcss.com/docs/padding',
          expireTime: '1752659309',
        },
      };
    },
  },

  {
    url: `${baseUrl}/get-tutorial-publish-process`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          process: {
            primaryProcessId: 'string',
            businessCode: 'string',
            state: 8,
            stateVersion: 'string',
            property: 'string',
            content: {},
            audit: {
              overallAuditResult: 'string',
            },
            idempotentId: 'string',
            submitAuditTime: 'string',
            auditPassTime: 'string',
            auditRejectTime: 'string',
            cancelTime: 'string',
            submitPublishTime: 'string',
            startPublishTime: 'string',
            finishTime: 'string',
            publishType: 1,
            scheduleTime: 'string',
            creator: 'string',
            modifier: 'string',
            createTime: 'string',
            modifyTime: 'string',
            processId: 'string',
            type: 1,
          },
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-publish-preview-code`,
    method: 'post',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          previewId: '1',
          previewCode: 'https://tailwindcss.com/docs/padding',
          expireTime: '1752659309',
        },
      };
    },
  },
  {
    url: `${baseUrl}/submit-tutorial-publish-process`,
    method: 'post',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {},
      };
    },
  },
  {
    url: `${baseUrl}/get-preview-code-scan-state`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: '',
        data: {
          state: 2,
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-brand-info`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'string',
        data: {
          brandLogo: 'https://dummyimage.com/80x80/222/fff&text=image',
          brandAlias: 'string',
          brandShowType: 1,
          brandId: 'string',
          brandName: '爱马哥',
        },
      };
    },
  },

  {
    url: `${baseUrl}/get-card-info`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          cardInfo: {
            customerServiceList: [
              {
                customerServiceType: 1,
                miniProgram: {
                  appid: 'string',
                  username: 'string',
                },
                wecom: {
                  path: 'string',
                },
                customizeWeb: {
                  path: 'string',
                },
                customizeMp: {
                  appid: 'string',
                  path: 'string',
                },
                servicePhone: 'string',
              },
            ],
            sceneList: [
              {
                //0-不使用 1-小程序 2-视频号
                sceneType: 1,
                //商家名片小程序场景
                miniProgram: {
                  appid: 'string',
                  sceneTag: '新品上市',
                  imageList: [
                    'http://mchcdnpublic-40010.sh.gfp.tencent-cloud.com/subjectstore/mchcardinfo/dcaaa2ab5d37cb7203f1ef7e73e81050.png?sign=q-sign-algorithm%3dsha1%26q-ak%3dJGrb4ACVFrZzpbSa6Fo0BcVK%26q-sign-time%3d1753013305%3b1753099715%26q-key-time%3d1753013305%3b1753099715%26q-header-list%3dhost%26q-url-param-list%3d%26q-signature%3dd7ef5bcc415696639c74c9674e4f604369490f88',
                  ],
                },
                // 商家名片视频号场景
                finder: {
                  finderUsername: 'string',
                  sceneTag: '新发布作品',
                  imageList: [
                    'http://mchcdnpublic-40010.sh.gfp.tencent-cloud.com/subjectstore/mchcardinfo/dcaaa2ab5d37cb7203f1ef7e73e81050.png?sign=q-sign-algorithm%3dsha1%26q-ak%3dJGrb4ACVFrZzpbSa6Fo0BcVK%26q-sign-time%3d1753013305%3b1753099715%26q-key-time%3d1753013305%3b1753099715%26q-header-list%3dhost%26q-url-param-list%3d%26q-signature%3dd7ef5bcc415696639c74c9674e4f604369490f88',
                    'http://mchcdnpublic-40010.sh.gfp.tencent-cloud.com/subjectstore/mchcardinfo/dcaaa2ab5d37cb7203f1ef7e73e81050.png?sign=q-sign-algorithm%3dsha1%26q-ak%3dJGrb4ACVFrZzpbSa6Fo0BcVK%26q-sign-time%3d1753013305%3b1753099715%26q-key-time%3d1753013305%3b1753099715%26q-header-list%3dhost%26q-url-param-list%3d%26q-signature%3dd7ef5bcc415696639c74c9674e4f604369490f88',
                  ],
                },
                sceneId: 'string',
              },
              {
                //0-不使用 1-小程序 2-视频号
                sceneType: 2,
                //商家名片小程序场景
                miniProgram: {
                  appid: 'string',
                  sceneTag: '新品上市',
                  imageList: [
                    'http://mchcdnpublic-40010.sh.gfp.tencent-cloud.com/subjectstore/mchcardinfo/dcaaa2ab5d37cb7203f1ef7e73e81050.png?sign=q-sign-algorithm%3dsha1%26q-ak%3dJGrb4ACVFrZzpbSa6Fo0BcVK%26q-sign-time%3d1753013305%3b1753099715%26q-key-time%3d1753013305%3b1753099715%26q-header-list%3dhost%26q-url-param-list%3d%26q-signature%3dd7ef5bcc415696639c74c9674e4f604369490f88',
                  ],
                },
                // 商家名片视频号场景
                finder: {
                  finderUsername: 'string',
                  sceneTag: '新发布作品',
                  imageList: [
                    'http://mchcdnpublic-40010.sh.gfp.tencent-cloud.com/subjectstore/mchcardinfo/dcaaa2ab5d37cb7203f1ef7e73e81050.png?sign=q-sign-algorithm%3dsha1%26q-ak%3dJGrb4ACVFrZzpbSa6Fo0BcVK%26q-sign-time%3d1753013305%3b1753099715%26q-key-time%3d1753013305%3b1753099715%26q-header-list%3dhost%26q-url-param-list%3d%26q-signature%3dd7ef5bcc415696639c74c9674e4f604369490f88',
                    'http://mchcdnpublic-40010.sh.gfp.tencent-cloud.com/subjectstore/mchcardinfo/dcaaa2ab5d37cb7203f1ef7e73e81050.png?sign=q-sign-algorithm%3dsha1%26q-ak%3dJGrb4ACVFrZzpbSa6Fo0BcVK%26q-sign-time%3d1753013305%3b1753099715%26q-key-time%3d1753013305%3b1753099715%26q-header-list%3dhost%26q-url-param-list%3d%26q-signature%3dd7ef5bcc415696639c74c9674e4f604369490f88',
                  ],
                },
                sceneId: 'string',
              },
            ],
            cardBrief: 'string',
          },
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-brand-member-access-state`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          state: 1,
        },
      };
    },
  },
  {
    url: `${baseUrl}/get-shake-coupon-access-state`,
    method: 'get',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          state: 1,
        },
      };
    },
  },
  {
    url: `${baseUrl}/save-publish-config`,
    method: 'post',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          state: 1,
        },
      };
    },
  },
  {
    url: `${baseUrl}/submit-card-info-publish-process`,
    method: 'post',
    response: () => {
      return {
        code: 0,
        message: 'success',
        data: {
          state: 1,
        },
      };
    },
  },
] as MockMethod[];
