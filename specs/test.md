# 商家经营名片系统 - 测试驱动开发方案

## 📋 测试策略概述

### TDD开发流程
基于**测试驱动开发(TDD)**原则，我们采用"红-绿-重构"的开发循环：

1. **红阶段**: 编写失败的测试用例
2. **绿阶段**: 编写最少代码使测试通过
3. **重构阶段**: 优化代码结构，保持测试通过

### 测试金字塔架构

```
    /\
   /  \     E2E Tests (端到端测试)
  /____\    - 用户场景测试
 /      \   - 关键业务流程
/__________\ Integration Tests (集成测试)
/          \ - 组件集成测试
/____________\ Unit Tests (单元测试)
              - 组件单元测试
              - 工具函数测试
              - Store状态测试
```

## 🛠️ 技术栈选择

### 测试框架
- **单元测试**: Vitest + Vue Test Utils
- **组件测试**: @vue/test-utils + jsdom
- **E2E测试**: Playwright
- **覆盖率**: c8 (Vitest内置)
- **Mock工具**: vi.mock (Vitest内置)

### 测试环境配置
```json
{
  "scripts": {
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui"
  }
}
```

## 📊 测试覆盖率目标

### 覆盖率指标
- **行覆盖率**: ≥ 90%
- **分支覆盖率**: ≥ 85%
- **函数覆盖率**: ≥ 95%
- **语句覆盖率**: ≥ 90%

### 关键模块覆盖率要求
- **组件逻辑**: 100%
- **状态管理**: 100%
- **API服务**: 95%
- **工具函数**: 100%
- **业务逻辑**: 95%

## 🧪 测试分类和规范

### 1. 单元测试 (Unit Tests)

#### 组件测试规范
```typescript
// 测试文件命名: [ComponentName].test.ts
// 测试位置: src/pages/[module]/components/__tests__/

describe('ComponentName', () => {
  describe('渲染测试', () => {
    it('应该正确渲染基本结构', () => {})
    it('应该根据props渲染不同内容', () => {})
  })
  
  describe('交互测试', () => {
    it('应该响应用户点击事件', () => {})
    it('应该正确处理表单输入', () => {})
  })
  
  describe('状态测试', () => {
    it('应该正确管理内部状态', () => {})
    it('应该正确emit事件', () => {})
  })
})
```

#### Store测试规范
```typescript
// 测试文件命名: [storeName].test.ts
// 测试位置: src/stores/__tests__/

describe('Store: storeName', () => {
  describe('状态初始化', () => {
    it('应该有正确的初始状态', () => {})
  })
  
  describe('Actions测试', () => {
    it('应该正确执行异步操作', () => {})
    it('应该正确处理错误情况', () => {})
  })
  
  describe('Getters测试', () => {
    it('应该返回正确的计算值', () => {})
  })
})
```

### 2. 集成测试 (Integration Tests)

#### 页面集成测试
```typescript
// 测试文件命名: [PageName].integration.test.ts
// 测试位置: src/pages/[module]/__tests__/

describe('PageName Integration', () => {
  describe('页面初始化', () => {
    it('应该正确加载页面数据', () => {})
    it('应该正确处理路由参数', () => {})
  })
  
  describe('组件协作', () => {
    it('应该正确处理组件间通信', () => {})
    it('应该正确同步状态变化', () => {})
  })
})
```

### 3. E2E测试 (End-to-End Tests)

#### 用户场景测试
```typescript
// 测试文件命名: [feature].e2e.test.ts
// 测试位置: tests/e2e/

test.describe('功能模块E2E测试', () => {
  test('用户完整操作流程', async ({ page }) => {
    // 模拟真实用户操作
  })
})
```

## 📁 测试目录结构

```
tests/
├── __mocks__/              # 全局Mock文件
│   ├── api.ts
│   └── router.ts
├── fixtures/               # 测试数据
│   ├── card-home.json
│   └── card-detail.json
├── helpers/                # 测试工具函数
│   ├── mount-with-store.ts
│   └── mock-api.ts
├── e2e/                    # E2E测试
│   ├── card-home.e2e.test.ts
│   └── card-detail.e2e.test.ts
└── setup.ts                # 测试环境配置

src/
├── pages/[module]/
│   ├── __tests__/          # 页面级测试
│   │   ├── [Page].test.ts
│   │   └── [Page].integration.test.ts
│   └── components/
│       └── __tests__/      # 组件测试
│           └── [Component].test.ts
├── stores/
│   └── __tests__/          # Store测试
│       └── [store].test.ts
├── services/
│   └── __tests__/          # API服务测试
│       └── [service].test.ts
└── utils/
    └── __tests__/          # 工具函数测试
        └── [util].test.ts
```

## 🎯 测试实施计划

### 阶段1: 测试基础设施搭建
- [x] 配置Vitest测试环境
- [x] 配置Vue Test Utils
- [x] 配置Playwright E2E测试
- [x] 建立测试目录结构
- [x] 编写测试工具函数

### 阶段2: Card-Home模块测试补全
- [ ] 组件单元测试
- [ ] Store状态测试
- [ ] API服务测试
- [ ] 页面集成测试
- [ ] E2E场景测试

### 阶段3: Card-Detail模块测试开发
- [ ] TDD方式开发组件测试
- [ ] 同步开发功能和测试
- [ ] 集成测试验证
- [ ] E2E测试覆盖

### 阶段4: 测试体系完善
- [ ] 测试覆盖率达标
- [ ] 性能测试基准
- [ ] 测试文档完善
- [ ] CI/CD集成

## 🔧 测试工具配置

### Vitest配置 (vitest.config.ts)
```typescript
import { defineConfig } from 'vitest/config'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./tests/setup.ts'],
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'tests/',
        '**/*.d.ts',
        '**/*.config.*'
      ],
      thresholds: {
        global: {
          branches: 85,
          functions: 95,
          lines: 90,
          statements: 90
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src')
    }
  }
})
```

### Playwright配置 (playwright.config.ts)
```typescript
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './tests/e2e',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
  },
})
```

## 📋 测试检查清单

### 开发前检查
- [ ] 是否编写了测试用例？
- [ ] 测试用例是否覆盖主要场景？
- [ ] 是否包含边界条件测试？
- [ ] 是否包含错误处理测试？

### 开发中检查
- [ ] 新功能是否有对应测试？
- [ ] 修改代码后测试是否通过？
- [ ] 测试覆盖率是否达标？
- [ ] 是否有集成测试验证？

### 发布前检查
- [ ] 所有单元测试通过？
- [ ] 所有集成测试通过？
- [ ] E2E测试场景通过？
- [ ] 测试覆盖率达到目标？
- [ ] 性能测试基准达标？

## 🎨 测试最佳实践

### 1. 测试命名规范
```typescript
// ✅ 好的测试命名
describe('CardHome组件', () => {
  it('应该在用户首次访问时显示新手引导', () => {})
  it('应该在点击功能卡片时正确跳转', () => {})
  it('应该在网络错误时显示错误提示', () => {})
})

// ❌ 不好的测试命名
describe('CardHome', () => {
  it('test1', () => {})
  it('should work', () => {})
})
```

### 2. 测试数据管理
```typescript
// ✅ 使用测试夹具
import { cardHomeFixture } from '@/tests/fixtures/card-home'

// ✅ 每个测试独立的数据
beforeEach(() => {
  const testData = createTestData()
})

// ❌ 测试间共享可变数据
const sharedData = { count: 0 } // 危险！
```

### 3. Mock策略
```typescript
// ✅ 精确Mock
vi.mock('@/services/cardHome', () => ({
  getCardHomeData: vi.fn().mockResolvedValue(mockData)
}))

// ✅ 条件Mock
vi.mock('@/utils/router', () => ({
  navigateTo: vi.fn().mockImplementation((path) => {
    if (path === '/error') throw new Error('Navigation failed')
    return Promise.resolve()
  })
}))
```

### 4. 异步测试处理
```typescript
// ✅ 正确的异步测试
it('应该正确处理异步数据加载', async () => {
  const promise = store.loadData()
  expect(store.loading).toBe(true)
  
  await promise
  expect(store.loading).toBe(false)
  expect(store.data).toBeDefined()
})

// ✅ 错误处理测试
it('应该正确处理API错误', async () => {
  mockApi.mockRejectedValue(new Error('API Error'))
  
  await expect(store.loadData()).rejects.toThrow('API Error')
  expect(store.error).toBe('API Error')
})
```

## 📊 测试报告和监控

### 覆盖率报告
- **HTML报告**: 详细的覆盖率可视化
- **JSON报告**: CI/CD集成数据
- **控制台报告**: 开发时快速反馈

### 测试性能监控
- **测试执行时间**: 监控测试套件性能
- **内存使用**: 防止内存泄漏
- **并发执行**: 优化测试执行效率

### CI/CD集成
```yaml
# GitHub Actions示例
name: Test
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:coverage
      - run: npm run test:e2e
      - uses: codecov/codecov-action@v3
```

## 🎯 质量门禁

### 代码提交门禁
- 所有相关测试必须通过
- 测试覆盖率不能降低
- 新功能必须有测试覆盖

### 发布门禁
- 单元测试通过率 100%
- 集成测试通过率 100%
- E2E测试通过率 ≥ 95%
- 代码覆盖率达到目标

## 📚 测试文档和培训

### 文档维护
- 测试用例文档
- 测试数据说明
- Mock策略文档
- 故障排查指南

### 团队培训
- TDD开发流程培训
- 测试工具使用培训
- 测试最佳实践分享
- 代码评审标准

---

**测试是质量的保障，让我们通过TDD确保每一行代码都经过验证！** 🧪✨

**文档版本**: 1.0  
**创建时间**: 2024-01-01  
**最后更新**: 2024-01-01  
**维护人**: 前端测试团队