# Card-Detail 页面验收测试用例 (Gherkin格式)

## 1. 测试概述

### 1.1 测试范围
本文档包含Card-Detail页面的所有功能验收测试用例，采用Gherkin语法编写，确保页面功能的完整性和可靠性。

### 1.2 测试环境
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **设备**: 桌面端、平板端、移动端
- **网络**: 正常网络、慢速网络、离线状态

## 2. 页面加载和初始化测试

### Feature: 页面加载和初始化

#### Scenario: 正常加载页面
```gherkin
Given 用户已登录系统
And 用户具有名片配置权限
When 用户访问名片详情配置页面
Then 页面应该在5秒内完成加载
And 显示页面标题"名片详情配置"
And 显示配置导航菜单
And 显示预览区域
And 显示底部操作栏
```

#### Scenario: 加载已有配置
```gherkin
Given 用户已有名片配置数据
When 用户访问名片详情配置页面
Then 系统应该加载用户的配置数据
And 在表单中显示已保存的信息
And 预览区域显示当前配置效果
And 页面状态显示为"已保存"
```

#### Scenario: 权限验证失败
```gherkin
Given 用户已登录但无配置权限
When 用户尝试访问名片详情配置页面
Then 系统应该显示权限错误提示
And 禁用所有编辑功能
And 只允许查看现有配置
```

## 3. 基础信息配置测试

### Feature: 基础信息配置

#### Scenario: 编辑商户名称
```gherkin
Given 用户在基础信息配置页面
When 用户在商户名称字段输入"测试商户名称"
Then 字段应该接受输入
And 实时验证字段内容
And 预览区域应该更新显示新名称
And 页面状态标记为"未保存"
```

#### Scenario: 商户名称验证
```gherkin
Given 用户在编辑商户名称
When 用户输入超过50个字符的名称
Then 系统应该显示字符限制错误
And 阻止继续输入
And 错误信息显示在字段下方
```

#### Scenario: 上传商户Logo
```gherkin
Given 用户在基础信息配置页面
When 用户点击Logo上传区域
And 选择一个2MB以内的jpg图片
Then 系统应该开始上传图片
And 显示上传进度条
And 上传完成后在预览区域显示Logo
And 保存Logo的URL到配置中
```

#### Scenario: Logo上传失败
```gherkin
Given 用户尝试上传Logo
When 用户选择一个超过2MB的图片文件
Then 系统应该显示文件大小错误
And 不执行上传操作
And 提示用户选择合适大小的文件
```

#### Scenario: 设置营业时间
```gherkin
Given 用户在基础信息配置页面
When 用户点击营业时间设置
And 选择"周一到周五 9:00-18:00"
Then 系统应该保存营业时间设置
And 在预览区域显示营业时间信息
And 标记配置为已修改状态
```

## 4. 联系方式配置测试

### Feature: 联系方式配置

#### Scenario: 添加客服电话
```gherkin
Given 用户在联系方式配置页面
When 用户点击"添加电话"按钮
And 输入电话号码"400-123-4567"
Then 系统应该验证电话号码格式
And 将电话添加到联系方式列表
And 在预览区域显示电话信息
```

#### Scenario: 电话号码格式验证
```gherkin
Given 用户在添加客服电话
When 用户输入无效的电话号码"abc123"
Then 系统应该显示格式错误提示
And 不允许保存无效号码
And 提供正确格式示例
```

#### Scenario: 设置联系方式优先级
```gherkin
Given 用户已添加多个联系方式
When 用户拖拽调整联系方式顺序
Then 系统应该更新联系方式的显示顺序
And 在预览区域按新顺序显示
And 保存新的优先级设置
```

#### Scenario: 生成微信二维码
```gherkin
Given 用户在联系方式配置页面
When 用户输入微信号"test_wechat"
And 点击"生成二维码"按钮
Then 系统应该生成对应的微信二维码
And 在预览区域显示二维码图片
And 保存二维码图片URL
```

## 5. 业务介绍配置测试

### Feature: 业务介绍配置

#### Scenario: 编辑业务描述
```gherkin
Given 用户在业务介绍配置页面
When 用户在富文本编辑器中输入业务描述
And 使用格式化功能（粗体、斜体）
Then 编辑器应该正确应用格式
And 预览区域显示格式化后的内容
And 保存富文本内容到配置中
```

#### Scenario: 插入业务图片
```gherkin
Given 用户在编辑业务描述
When 用户点击插入图片按钮
And 上传一张业务相关图片
Then 图片应该插入到编辑器中
And 在预览区域正确显示
And 支持图片的拖拽调整位置
```

#### Scenario: 设置业务分类
```gherkin
Given 用户在业务介绍配置页面
When 用户选择"餐饮服务"作为主营业务
And 添加"外卖配送"作为辅助业务
Then 系统应该保存业务分类设置
And 在预览区域显示业务标签
And 支持业务分类的搜索和筛选
```

#### Scenario: 添加业务关键词
```gherkin
Given 用户在业务介绍配置页面
When 用户添加关键词"美食、快餐、外卖"
Then 系统应该保存关键词列表
And 支持关键词的编辑和删除
And 在预览中适当位置显示关键词
```

## 6. 实时预览功能测试

### Feature: 实时预览功能

#### Scenario: 实时预览更新
```gherkin
Given 用户在任意配置页面
When 用户修改任何配置信息
Then 预览区域应该在500毫秒内更新
And 显示最新的配置效果
And 不影响用户的编辑操作
```

#### Scenario: 设备预览切换
```gherkin
Given 用户在预览区域
When 用户点击"平板预览"按钮
Then 预览容器应该切换到平板尺寸
And 内容自适应新的显示尺寸
And 保持预览内容的完整性
```

#### Scenario: 全屏预览
```gherkin
Given 用户在预览区域
When 用户点击全屏预览按钮
Then 预览应该以全屏模式显示
And 提供退出全屏的操作按钮
And 支持在全屏模式下的交互测试
```

#### Scenario: 预览交互测试
```gherkin
Given 用户在预览区域
When 用户点击预览中的联系电话
Then 应该显示拨打电话的提示
And 模拟真实用户的交互体验
And 不执行实际的拨打操作
```

## 7. 配置保存功能测试

### Feature: 配置保存功能

#### Scenario: 手动保存配置
```gherkin
Given 用户已修改配置信息
When 用户点击"保存"按钮
Then 系统应该验证所有必填字段
And 将配置数据提交到后端
And 显示保存成功提示
And 更新页面状态为"已保存"
```

#### Scenario: 自动保存功能
```gherkin
Given 用户正在编辑配置
When 用户停止操作超过30秒
Then 系统应该自动保存当前配置
And 显示"自动保存"状态提示
And 不干扰用户的编辑操作
```

#### Scenario: 保存验证失败
```gherkin
Given 用户尝试保存配置
When 商户名称字段为空（必填字段）
Then 系统应该显示验证错误
And 高亮显示错误字段
And 阻止保存操作
And 提供错误修正建议
```

#### Scenario: 网络异常保存
```gherkin
Given 用户点击保存配置
When 网络连接异常导致保存失败
Then 系统应该显示网络错误提示
And 保留用户输入的数据
And 提供重试保存选项
And 支持离线数据暂存
```

## 8. 配置发布功能测试

### Feature: 配置发布功能

#### Scenario: 发布配置
```gherkin
Given 用户已完成所有必要配置
When 用户点击"发布"按钮
Then 系统应该执行完整配置验证
And 将配置发布为正式版本
And 更新名片的发布状态
And 显示发布成功确认
```

#### Scenario: 发布前验证
```gherkin
Given 用户尝试发布配置
When 存在未完成的必填配置项
Then 系统应该显示验证失败提示
And 列出所有未完成的配置项
And 提供快速跳转到对应配置的链接
And 阻止发布操作
```

#### Scenario: 发布状态更新
```gherkin
Given 用户成功发布配置
When 发布操作完成
Then 页面应该显示"已发布"状态
And 更新发布时间信息
And 在预览区域显示发布版本
And 提供查看发布历史的选项
```

## 9. 错误处理测试

### Feature: 错误处理

#### Scenario: API调用超时
```gherkin
Given 用户执行需要API调用的操作
When API响应超过10秒未返回
Then 系统应该显示超时错误提示
And 提供重试操作选项
And 不影响页面其他功能的使用
```

#### Scenario: 数据格式异常
```gherkin
Given 系统接收到格式异常的API响应
When 数据无法正常解析
Then 系统应该使用默认值填充
And 显示数据异常警告
And 记录异常信息用于排查
And 允许用户手动修正数据
```

#### Scenario: 图片处理失败
```gherkin
Given 用户上传图片文件
When 图片处理过程中发生错误
Then 系统应该显示具体错误信息
And 提供重新上传选项
And 建议用户检查文件格式
And 保持页面其他功能正常
```

## 10. 性能测试

### Feature: 性能表现

#### Scenario: 页面加载性能
```gherkin
Given 用户访问名片详情配置页面
When 页面开始加载
Then 页面应该在5秒内完成初始化
And 关键内容在3秒内可见
And 交互元素在加载完成后立即可用
```

#### Scenario: 预览更新性能
```gherkin
Given 用户修改配置信息
When 触发预览更新
Then 预览应该在500毫秒内更新完成
And 更新过程不阻塞用户操作
And 内存使用保持在合理范围
```

#### Scenario: 大量数据处理
```gherkin
Given 用户配置包含大量图片和文本
When 执行保存或预览操作
Then 系统应该正常处理大量数据
And 响应时间不超过预期阈值
And 不出现内存溢出问题
And 提供处理进度反馈
```

## 11. 兼容性测试

### Feature: 浏览器兼容性

#### Scenario: Chrome浏览器兼容性
```gherkin
Given 用户使用Chrome 90+浏览器
When 用户访问名片详情配置页面
Then 所有功能应该正常工作
And 界面显示效果符合设计规范
And 交互响应正常无异常
```

#### Scenario: Firefox浏览器兼容性
```gherkin
Given 用户使用Firefox 88+浏览器
When 用户执行配置和预览操作
Then 功能表现应该与Chrome一致
And 富文本编辑器正常工作
And 图片上传和预览正常
```

#### Scenario: Safari浏览器兼容性
```gherkin
Given 用户使用Safari 14+浏览器
When 用户进行名片配置操作
Then 所有交互功能正常
And 样式显示无明显差异
And 性能表现在可接受范围内
```

### Feature: 设备兼容性

#### Scenario: 桌面端适配
```gherkin
Given 用户使用桌面设备（1200px+宽度）
When 用户访问配置页面
Then 页面应该显示完整的双栏布局
And 配置面板和预览面板并排显示
And 所有功能区域清晰可见
```

#### Scenario: 平板端适配
```gherkin
Given 用户使用平板设备（768-1199px宽度）
When 用户访问配置页面
Then 页面应该自适应平板布局
And 保持良好的操作体验
And 预览功能正常可用
```

#### Scenario: 移动端适配
```gherkin
Given 用户使用移动设备（<768px宽度）
When 用户访问配置页面
Then 页面应该切换为垂直布局
And 预览区域置于配置区域上方
And 支持触摸操作和手势
```

## 12. 安全性测试

### Feature: 数据安全

#### Scenario: 输入数据验证
```gherkin
Given 用户在配置表单中输入数据
When 用户输入包含脚本标签的内容
Then 系统应该过滤危险脚本
And 显示安全警告提示
And 只保存安全的文本内容
```

#### Scenario: 文件上传安全
```gherkin
Given 用户尝试上传文件
When 用户选择非图片格式的文件
Then 系统应该拒绝上传
And 显示文件类型错误提示
And 只允许安全的图片格式
```

#### Scenario: 权限控制验证
```gherkin
Given 用户尝试访问他人的配置
When 用户修改URL参数访问其他用户数据
Then 系统应该验证用户权限
And 拒绝未授权的访问
And 重定向到权限错误页面
```

## 13. 可访问性测试

### Feature: 无障碍访问

#### Scenario: 键盘导航
```gherkin
Given 用户只使用键盘操作
When 用户按Tab键在页面中导航
Then 焦点应该按逻辑顺序移动
And 所有交互元素都可以通过键盘访问
And 焦点状态清晰可见
```

#### Scenario: 屏幕阅读器支持
```gherkin
Given 用户使用屏幕阅读器
When 用户访问配置页面
Then 页面结构应该被正确识别
And 表单标签和字段关联正确
And 错误信息能够被正确朗读
```

#### Scenario: 高对比度模式
```gherkin
Given 用户启用系统高对比度模式
When 用户访问配置页面
Then 页面应该适配高对比度显示
And 文本和背景对比度符合标准
And 所有信息都清晰可读
```

## 14. 集成测试

### Feature: 系统集成

#### Scenario: 与其他页面的集成
```gherkin
Given 用户从首页跳转到配置页面
When 用户完成配置并保存
And 返回到首页
Then 首页应该显示最新的配置状态
And 数据同步正确无误
```

#### Scenario: 与后端API的集成
```gherkin
Given 配置页面需要调用多个后端API
When 用户执行配置操作
Then 所有API调用应该正确执行
And 数据传输格式符合接口规范
And 错误处理机制正常工作
```

#### Scenario: 与状态管理的集成
```gherkin
Given 用户在多个页面间切换
When 配置数据发生变更
Then Pinia状态应该正确更新
And 所有相关页面同步更新
And 状态持久化正常工作
```

## 15. 回归测试

### Feature: 功能回归

#### Scenario: 核心功能回归测试
```gherkin
Given 系统进行了版本更新
When 执行完整的功能测试流程
Then 所有核心功能应该正常工作
And 性能指标不低于之前版本
And 用户体验保持一致
```

#### Scenario: 数据兼容性测试
```gherkin
Given 系统存在历史配置数据
When 用户访问配置页面
Then 历史数据应该正确加载和显示
And 支持数据格式的向后兼容
And 数据迁移过程无损失
```

## 16. 压力测试

### Feature: 系统压力

#### Scenario: 并发用户测试
```gherkin
Given 多个用户同时访问配置页面
When 系统承受高并发访问
Then 每个用户的操作应该正常响应
And 系统性能保持稳定
And 数据不出现混乱或丢失
```

#### Scenario: 大文件处理测试
```gherkin
Given 用户上传接近大小限制的图片文件
When 系统处理大文件上传
Then 上传过程应该稳定完成
And 提供准确的进度反馈
And 不影响其他用户的操作
```

## 17. 用户体验测试

### Feature: 用户体验

#### Scenario: 新用户首次使用
```gherkin
Given 新用户首次访问配置页面
When 用户开始配置操作
Then 页面应该提供清晰的操作指引
And 重要功能有适当的提示说明
And 用户能够顺利完成基础配置
```

#### Scenario: 操作流程优化
```gherkin
Given 用户需要完成完整的配置流程
When 用户按照页面引导操作
Then 整个流程应该逻辑清晰
And 减少不必要的操作步骤
And 提供快捷操作选项
```

#### Scenario: 错误恢复体验
```gherkin
Given 用户操作过程中遇到错误
When 系统显示错误信息
Then 错误提示应该清晰易懂
And 提供具体的解决建议
And 支持快速恢复到正常状态
```

## 18. 测试数据管理

### Feature: 测试数据

#### Scenario: 测试数据准备
```gherkin
Given 需要执行自动化测试
When 测试开始前准备数据
Then 应该创建完整的测试数据集
And 包含各种边界情况的数据
And 确保数据的一致性和有效性
```

#### Scenario: 测试数据清理
```gherkin
Given 测试执行完成
When 需要清理测试数据
Then 应该完全清除测试产生的数据
And 不影响生产环境数据
And 恢复系统到初始状态
```

## 19. 测试报告

### Feature: 测试结果

#### Scenario: 测试结果统计
```gherkin
Given 所有测试用例执行完成
When 生成测试报告
Then 报告应该包含详细的执行结果
And 统计通过率和失败率
And 提供失败用例的详细信息
```

#### Scenario: 性能指标报告
```gherkin
Given 性能测试执行完成
When 分析性能数据
Then 报告应该包含关键性能指标
And 与基准值进行对比分析
And 识别性能瓶颈和优化建议
```

## 20. 测试维护

### Feature: 测试维护

#### Scenario: 测试用例更新
```gherkin
Given 产品功能发生变更
When 需要更新测试用例
Then 应该及时修改相关测试用例
And 确保测试覆盖新增功能
And 移除过时的测试场景
```

#### Scenario: 测试环境维护
```gherkin
Given 测试环境需要维护
When 执行环境更新操作
Then 应该确保测试环境的稳定性
And 验证所有测试工具正常工作
And 保持与生产环境的一致性
```

---

## 测试执行计划

### 测试阶段安排

1. **单元测试阶段** (开发期间)
   - 组件功能测试
   - 状态管理测试
   - 工具函数测试

2. **集成测试阶段** (功能完成后)
   - API集成测试
   - 页面间导航测试
   - 数据流测试

3. **系统测试阶段** (集成完成后)
   - 完整功能流程测试
   - 性能和兼容性测试
   - 安全性测试

4. **验收测试阶段** (发布前)
   - 用户场景测试
   - 业务流程验证
   - 最终质量确认

### 测试工具和框架

- **单元测试**: Vitest + Vue Test Utils
- **E2E测试**: Cypress
- **性能测试**: Lighthouse + WebPageTest
- **可访问性测试**: axe-core
- **兼容性测试**: BrowserStack

### 测试覆盖率目标

- **代码覆盖率**: ≥ 80%
- **功能覆盖率**: 100%
- **浏览器覆盖率**: Chrome, Firefox, Safari, Edge
- **设备覆盖率**: 桌面端、平板端、移动端

---

**文档版本**: 1.0  
**创建时间**: 2024-01-01  
**最后更新**: 2024-01-01  
**维护人**: 测试团队  
**状态**: 测试用例完成 ✅
