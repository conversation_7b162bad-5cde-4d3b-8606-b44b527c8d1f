# Card-Detail 页面设计文档

## 1. 设计概述

### 1.1 页面定位
Card-Detail页面是商家名片管理系统的核心配置页面，承担着名片信息编辑、实时预览、配置管理等关键功能。页面设计需要平衡功能复杂性和用户体验，确保用户能够高效完成名片配置。

### 1.2 设计目标
- **功能完整性**: 提供全面的名片配置功能
- **操作便捷性**: 简化配置流程，提升用户效率
- **视觉一致性**: 与系统整体设计风格保持一致
- **响应式设计**: 适配多种设备和屏幕尺寸

## 2. 整体架构设计

### 2.1 页面布局架构

```mermaid
graph TB
    A[页面容器] --> B[顶部导航栏]
    A --> C[主内容区域]
    A --> D[底部操作栏]
    
    C --> E[左侧配置面板]
    C --> F[右侧预览区域]
    
    E --> G[配置导航]
    E --> H[配置表单区]
    
    F --> I[预览工具栏]
    F --> J[预览画布]
    
    subgraph "配置导航"
        G1[基础信息]
        G2[联系方式]
        G3[业务介绍]
        G4[样式设置]
    end
    
    subgraph "预览工具栏"
        I1[设备切换]
        I2[刷新预览]
        I3[全屏预览]
    end
```

### 2.2 组件层次结构

```mermaid
graph TD
    A[CardDetail.vue] --> B[CardDetailHeader.vue]
    A --> C[CardDetailContent.vue]
    A --> D[CardDetailFooter.vue]
    
    C --> E[ConfigPanel.vue]
    C --> F[PreviewPanel.vue]
    
    E --> G[ConfigNavigation.vue]
    E --> H[ConfigForm.vue]
    
    H --> I[BasicInfoForm.vue]
    H --> J[ContactForm.vue]
    H --> K[BusinessForm.vue]
    H --> L[StyleForm.vue]
    
    F --> M[PreviewToolbar.vue]
    F --> N[PreviewCanvas.vue]
    
    N --> O[CardPreview.vue]
```

## 3. 详细设计规范

### 3.1 顶部导航栏设计

#### 3.1.1 布局结构
```scss
.card-detail-header {
  height: 64px;
  background: #ffffff;
  border-bottom: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}
```

#### 3.1.2 功能元素
- **左侧区域**:
  - 返回按钮 (t-button variant="text" + t-icon name="chevron-left")
  - 页面标题 "名片详情配置"
  - 配置状态指示器 (进度条显示完成度)

- **右侧区域**:
  - 帮助按钮 (t-button variant="text" + t-icon name="help-circle")
  - 更多操作菜单 (t-dropdown)

### 3.2 主内容区域设计

#### 3.2.1 左侧配置面板 (60%宽度)

##### 配置导航设计
```scss
.config-navigation {
  width: 200px;
  background: #fafafa;
  border-right: 1px solid #e7e7e7;
  
  .nav-item {
    padding: 12px 16px;
    cursor: pointer;
    border-left: 3px solid transparent;
    
    &.active {
      background: #ffffff;
      border-left-color: #0052d9;
      color: #0052d9;
    }
    
    &:hover {
      background: #f3f3f3;
    }
  }
}
```

##### 配置表单区域
```scss
.config-form {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  
  .form-section {
    margin-bottom: 32px;
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #1f2937;
    }
  }
}
```

#### 3.2.2 右侧预览区域 (40%宽度)

##### 预览工具栏
```scss
.preview-toolbar {
  height: 48px;
  background: #f8f9fa;
  border-bottom: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  
  .device-switch {
    display: flex;
    background: #ffffff;
    border-radius: 6px;
    padding: 2px;
    
    .device-btn {
      padding: 6px 12px;
      border-radius: 4px;
      
      &.active {
        background: #0052d9;
        color: #ffffff;
      }
    }
  }
}
```

##### 预览画布
```scss
.preview-canvas {
  flex: 1;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24px;
  
  .preview-container {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    
    &.desktop {
      width: 400px;
      min-height: 600px;
    }
    
    &.tablet {
      width: 320px;
      min-height: 480px;
    }
    
    &.mobile {
      width: 280px;
      min-height: 400px;
    }
  }
}
```

### 3.3 底部操作栏设计

```scss
.card-detail-footer {
  height: 72px;
  background: #ffffff;
  border-top: 1px solid #e7e7e7;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  
  .footer-left {
    .auto-save-status {
      color: #6b7280;
      font-size: 14px;
    }
  }
  
  .footer-right {
    display: flex;
    gap: 12px;
    
    .save-btn {
      min-width: 80px;
    }
    
    .publish-btn {
      min-width: 100px;
    }
  }
}
```

## 4. 交互设计规范

### 4.1 配置导航交互

#### 4.1.1 导航切换
- **触发方式**: 点击导航项
- **视觉反馈**: 
  - 当前项高亮显示 (背景色 #ffffff, 左边框 #0052d9)
  - 切换动画: 200ms ease-in-out
- **内容切换**: 右侧表单区域平滑切换到对应配置内容

#### 4.1.2 进度指示
- **完成状态**: 导航项右侧显示绿色勾选图标
- **必填提醒**: 未完成必填项的导航项显示红色圆点
- **当前编辑**: 正在编辑的导航项显示蓝色编辑图标

### 4.2 表单交互设计

#### 4.2.1 字段验证
- **实时验证**: 用户输入时实时验证，延迟300ms
- **错误显示**: 字段下方显示红色错误文本
- **成功提示**: 验证通过显示绿色勾选图标

#### 4.2.2 图片上传交互
```scss
.image-upload {
  .upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      border-color: #0052d9;
      background: #f8faff;
    }
    
    &.dragover {
      border-color: #0052d9;
      background: #e6f3ff;
    }
  }
  
  .upload-progress {
    margin-top: 12px;
    
    .progress-bar {
      height: 4px;
      background: #e5e7eb;
      border-radius: 2px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: #0052d9;
        transition: width 0.3s ease;
      }
    }
  }
}
```

### 4.3 预览交互设计

#### 4.3.1 设备切换
- **切换动画**: 预览容器尺寸变化动画 300ms ease-in-out
- **内容适配**: 预览内容自动适配新的设备尺寸
- **状态保持**: 切换设备时保持预览内容的滚动位置

#### 4.3.2 实时更新
- **更新触发**: 配置变更后300ms延迟更新预览
- **加载状态**: 预览更新时显示加载遮罩
- **错误处理**: 预览生成失败时显示错误提示

## 5. 状态管理设计

### 5.1 状态结构设计

```typescript
interface CardDetailState {
  // 基础信息
  basicInfo: {
    merchantName: string;
    merchantDesc: string;
    logo: string;
    businessHours: BusinessHours;
    address: Address;
  };
  
  // 联系方式
  contactInfo: {
    phones: string[];
    wechat: string;
    email: string;
    website: string;
    socialMedia: SocialMedia[];
  };
  
  // 业务介绍
  businessInfo: {
    description: string;
    categories: string[];
    keywords: string[];
    images: string[];
  };
  
  // 样式设置
  styleConfig: {
    theme: string;
    colors: ColorScheme;
    layout: LayoutConfig;
  };
  
  // 页面状态
  pageState: {
    currentTab: string;
    isLoading: boolean;
    isSaving: boolean;
    isDirty: boolean;
    errors: Record<string, string>;
  };
  
  // 预览状态
  previewState: {
    device: 'desktop' | 'tablet' | 'mobile';
    isFullscreen: boolean;
    isUpdating: boolean;
  };
}
```

### 5.2 状态管理操作

```typescript
// Pinia Store Actions
export const useCardDetailStore = defineStore('cardDetail', {
  state: (): CardDetailState => ({
    basicInfo: {
      merchantName: '',
      merchantDesc: '',
      logo: '',
      businessHours: {},
      address: {}
    },
    contactInfo: {
      phones: [],
      wechat: '',
      email: '',
      website: '',
      socialMedia: []
    },
    businessInfo: {
      description: '',
      categories: [],
      keywords: [],
      images: []
    },
    styleConfig: {
      theme: 'default',
      colors: {},
      layout: {}
    },
    pageState: {
      currentTab: 'basic',
      isLoading: false,
      isSaving: false,
      isDirty: false,
      errors: {}
    },
    previewState: {
      device: 'desktop',
      isFullscreen: false,
      isUpdating: false
    }
  }),
  
  actions: {
    // 加载配置数据
    async loadCardConfig(cardId: string) {
      this.pageState.isLoading = true;
      try {
        const config = await cardDetailApi.getCardConfig(cardId);
        this.updateConfig(config);
      } catch (error) {
        this.handleError('加载配置失败', error);
      } finally {
        this.pageState.isLoading = false;
      }
    },
    
    // 保存配置
    async saveConfig() {
      this.pageState.isSaving = true;
      try {
        await cardDetailApi.saveCardConfig(this.getConfigData());
        this.pageState.isDirty = false;
        this.showSuccess('保存成功');
      } catch (error) {
        this.handleError('保存失败', error);
      } finally {
        this.pageState.isSaving = false;
      }
    },
    
    // 更新预览
    updatePreview: debounce(function() {
      this.previewState.isUpdating = true;
      // 预览更新逻辑
      setTimeout(() => {
        this.previewState.isUpdating = false;
      }, 300);
    }, 300)
  }
});
```

## 6. 组件设计规范

### 6.1 主组件 CardDetail.vue

```vue
<template>
  <div class="card-detail">
    <CardDetailHeader 
      :title="pageTitle"
      :progress="configProgress"
      @back="handleBack"
      @help="showHelp"
    />
    
    <div class="card-detail-content">
      <ConfigPanel 
        v-model:current-tab="currentTab"
        :config="configData"
        @update="handleConfigUpdate"
      />
      
      <PreviewPanel 
        :config="configData"
        :device="previewDevice"
        @device-change="handleDeviceChange"
      />
    </div>
    
    <CardDetailFooter 
      :is-saving="isSaving"
      :is-dirty="isDirty"
      @save="handleSave"
      @publish="handlePublish"
    />
  </div>
</template>
```

### 6.2 配置面板组件 ConfigPanel.vue

```vue
<template>
  <div class="config-panel">
    <ConfigNavigation 
      v-model:current="currentTab"
      :tabs="configTabs"
      :validation="validationStatus"
    />
    
    <div class="config-content">
      <component 
        :is="currentFormComponent"
        v-model="configData[currentTab]"
        @validate="handleValidation"
      />
    </div>
  </div>
</template>
```

### 6.3 预览面板组件 PreviewPanel.vue

```vue
<template>
  <div class="preview-panel">
    <PreviewToolbar 
      v-model:device="currentDevice"
      :devices="supportedDevices"
      @refresh="refreshPreview"
      @fullscreen="toggleFullscreen"
    />
    
    <div class="preview-canvas">
      <div 
        class="preview-container"
        :class="deviceClass"
      >
        <CardPreview 
          :config="previewConfig"
          :loading="isUpdating"
        />
      </div>
    </div>
  </div>
</template>
```

## 7. 样式主题设计

### 7.1 色彩系统

```scss
// 主色调
$primary-color: #0052d9;
$primary-light: #4080ff;
$primary-dark: #003ba3;

// 辅助色
$success-color: #00a870;
$warning-color: #ed7b2f;
$error-color: #e34d59;
$info-color: #0594fa;

// 中性色
$text-primary: #1f2937;
$text-secondary: #6b7280;
$text-placeholder: #9ca3af;
$border-color: #e7e7e7;
$background-light: #fafafa;
$background-white: #ffffff;

// 状态色
$hover-bg: #f3f4f6;
$active-bg: #e5e7eb;
$disabled-bg: #f9fafb;
$disabled-text: #d1d5db;
```

### 7.2 间距系统

```scss
// 间距变量
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 12px;
$spacing-lg: 16px;
$spacing-xl: 24px;
$spacing-2xl: 32px;
$spacing-3xl: 48px;

// 组件间距
$component-padding: $spacing-xl;
$section-margin: $spacing-2xl;
$form-item-margin: $spacing-lg;
```

### 7.3 字体系统

```scss
// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;

// 字体权重
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;
```

## 8. 响应式设计

### 8.1 断点系统

```scss
// 断点定义
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// 媒体查询混入
@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}
```

### 8.2 响应式布局

```scss
.card-detail-content {
  display: flex;
  height: calc(100vh - 136px); // 减去头部和底部高度
  
  @include mobile {
    flex-direction: column;
    height: auto;
  }
  
  .config-panel {
    flex: 0 0 60%;
    
    @include mobile {
      flex: none;
      order: 2;
    }
  }
  
  .preview-panel {
    flex: 0 0 40%;
    border-left: 1px solid $border-color;
    
    @include mobile {
      flex: none;
      order: 1;
      border-left: none;
      border-bottom: 1px solid $border-color;
      height: 300px;
    }
  }
}
```

## 9. 动画设计

### 9.1 过渡动画

```scss
// 基础过渡
$transition-fast: 0.15s ease-in-out;
$transition-base: 0.2s ease-in-out;
$transition-slow: 0.3s ease-in-out;

// 组件过渡
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-base;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform $transition-base;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}
```

### 9.2 加载动画

```scss
.loading-spinner {
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  
  animation: spin 1s linear infinite;
  width: 20px;
  height: 20px;
  border: 2px solid $border-color;
  border-top-color: $primary-color;
  border-radius: 50%;
}

.skeleton-loading {
  @keyframes skeleton-loading {
    0% { background-position: -200px 0; }
    100% { background-position: calc(200px + 100%) 0; }
  }
  
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
}
```

## 10. 可访问性设计

### 10.1 键盘导航

```scss
// 焦点样式
.focusable {
  &:focus {
    outline: 2px solid $primary-color;
    outline-offset: 2px;
  }
  
  &:focus:not(:focus-visible) {
    outline: none;
  }
}

// 跳过链接
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: $primary-color;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  
  &:focus {
    top: 6px;
  }
}
```

### 10.2 ARIA 标签

```vue
<template>
  <div 
    class="config-form"
    role="form"
    :aria-label="formTitle"
  >
    <fieldset>
      <legend>{{ sectionTitle }}</legend>
      
      <div class="form-item">
        <label 
          :for="fieldId"
          class="form-label"
        >
          {{ fieldLabel }}
          <span 
            v-if="required"
            aria-label="必填"
            class="required-mark"
          >*</span>
        </label>
        
        <input 
          :id="fieldId"
          v-model="fieldValue"
          :aria-describedby="errorId"
          :aria-invalid="hasError"
          class="form-input"
        />
        
        <div 
          v-if="hasError"
          :id="errorId"
          role="alert"
          class="error-message"
        >
          {{ errorMessage }}
        </div>
      </div>
    </fieldset>
  </div>
</template>
```

## 11. 性能优化设计

### 11.1 组件懒加载

```typescript
// 路由懒加载
const CardDetail = () => import('@/pages/card-mna/card-detail/CardDetail.vue');

// 组件懒加载
const RichTextEditor = defineAsyncComponent({
  loader: () => import('@/components/RichTextEditor.vue'),
  loadingComponent: LoadingSpinner,
  errorComponent: ErrorComponent,
  delay: 200,
  timeout: 3000
});
```

### 11.2 防抖优化

```typescript
// 预览更新防抖
const updatePreview = debounce((config: CardConfig) => {
  previewStore.updatePreview(config);
}, 300);

// 自动保存防抖
const autoSave = debounce(async (config: CardConfig) => {
  await cardDetailStore.saveDraft(config);
}, 2000);
```

## 12. 测试设计

### 12.1 单元测试覆盖

```typescript
// 组件测试
describe('CardDetail.vue', () => {
  it('should render correctly', () => {
    const wrapper = mount(CardDetail);
    expect(wrapper.find('.card-detail').exists()).toBe(true);
  });
  
  it('should handle config update', async () => {
    const wrapper = mount(CardDetail);
    await wrapper.vm.handleConfigUpdate({ merchantName: 'Test' });
    expect(wrapper.vm.isDirty).toBe(true);
  });
});

// Store测试
describe('cardDetailStore', () => {
  it('should load config correctly', async () => {
    const store = useCardDetailStore();
    await store.loadCardConfig('test-id');
    expect(store.basicInfo.merchantName).toBeDefined();
  });
});
```

### 12.2 集成测试

```typescript
// E2E测试场景
describe('Card Detail Configuration', () => {
  it('should complete full configuration flow', () => {
    cy.visit('/card-detail');
    cy.get('[data-testid="merchant-name"]').type('测试商户');
    cy.get('[data-testid="save-btn"]').click();
    cy.get('[data-testid="success-message"]').should('be.visible');
  });
});
```

## 13. 部署和维护

### 13.1 构建优化

```typescript
// Vite配置优化
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          'card-detail': ['./src/pages/card-mna/card-detail/index.ts'],
          'rich-editor': ['@/components/RichTextEditor.vue']
        }
      }
    }
  }
});
```

### 13.2 监控和日志

```typescript
// 错误监控
window.addEventListener('error', (event) => {
  console.error('Card Detail Error:', event.error);
  // 发送错误报告
});

// 性能监控
const observer = new PerformanceObserver((list) => {
  list.getEntries().forEach((entry) => {
    console.log('Performance:', entry);
  });
});
observer.observe({ entryTypes: ['measure'] });
```

---

**文档版本**: 1.0  
**创建时间**: 2024-01-01  
**最后更新**: 2024-01-01  
**维护人**: 前端架构团队  
**状态**: 设计完成 ✅
