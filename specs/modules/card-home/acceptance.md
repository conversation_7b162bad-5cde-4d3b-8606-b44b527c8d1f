# Card-Home 页面验收标准 (Gherkin格式)

## 验收概述

本文档基于需求规格说明书(requirements.md)中的EARS格式需求，使用Gherkin格式定义验收标准，确保所有功能需求都有对应的验收测试场景。

## 功能验收标准

### Feature: 页面初始化功能
**对应需求**: REQ-001, REQ-002

```gherkin
Feature: 商家名片首页初始化
  作为一个商家用户
  我希望访问商家名片首页时系统能自动检查我的配置状态
  以便我能看到合适的页面内容和操作选项

  Background:
    Given 我是一个已登录的商家用户
    And 系统已正常运行

  Scenario: 新用户首次访问页面
    Given 我是一个新用户，没有任何配置记录
    When 我访问商家名片首页 "/home/<USER>/card-home"
    Then 系统应该调用后端API检查我的配置状态
    And 页面应该显示商家名片介绍内容
    And "开始配置"按钮应该是可用状态
    And 我应该能看到三个功能介绍卡片
    And 页面标题应该显示"商家名片"
    And 页面描述应该显示"助力商家服务与经营，帮助商家实现和用户的深度连接"

  Scenario: 已完成配置的用户访问页面
    Given 我已经完成了新手引导配置
    And 我的新手引导状态为"已发布"(PUBLISHED)
    When 我访问商家名片首页 "/home/<USER>/card-home"
    Then 系统应该自动重定向我到仪表盘页面
    And 重定向目标应该是 "/home/<USER>/dashboard"
    And 我不应该看到首页的介绍内容

  Scenario: 有未完成配置的用户访问页面
    Given 我有一个未完成的新手引导流程
    And 我的新手引导状态为"草稿"(DRAFT)
    When 我访问商家名片首页 "/home/<USER>/card-home"
    Then 系统应该自动重定向我到详情配置页面
    And 重定向目标应该是 "/home/<USER>/card-detail"
    And 我应该能继续之前的配置流程

  Scenario: 页面加载性能验收
    Given 用户网络连接正常
    When 我访问商家名片首页
    Then 页面应该在3秒内完成初始化
    And 初始化包括API调用、状态更新和界面渲染
    And 页面应该显示完整的内容
    And 所有交互元素应该可用
```

### Feature: 开始配置功能
**对应需求**: REQ-003, REQ-004

```gherkin
Feature: 开始配置按钮功能
  作为一个新用户
  我希望点击"开始配置"按钮能创建新手引导流程
  以便我能开始配置我的商家名片

  Background:
    Given 我是一个新用户
    And 我在商家名片首页
    And 页面已正常加载

  Scenario: 新用户点击开始配置按钮成功
    Given "开始配置"按钮是可用状态
    And cardState.useable 为 false
    When 我点击"开始配置"按钮
    Then 系统应该显示加载状态
    And 按钮应该显示loading状态
    And 系统应该调用 "CreateTutorialPublishProcess" API
    And API调用成功后应该保存返回的processId到tutorialState.processId
    And 系统应该跳转到名片详情配置页面 "/home/<USER>/card-detail"
    And 跳转应该在500毫秒内完成

  Scenario: 按钮禁用状态验证
    Given 我的配置状态为"不可用"(cardState.useable = true)
    When 我查看"开始配置"按钮
    Then 按钮应该是禁用状态
    And 按钮应该显示为灰色不可点击样式
    And 按钮应该有disabled属性
    When 我尝试点击按钮
    Then 不应该有任何响应
    And 不应该触发API调用

  Scenario: 创建引导流程失败处理
    Given "开始配置"按钮是可用状态
    And API服务暂时不可用
    When 我点击"开始配置"按钮
    Then 系统应该显示加载状态
    And 系统应该调用 "CreateTutorialPublishProcess" API
    And API调用失败后应该显示错误提示
    And 错误提示应该包含"创建引导流程失败"信息
    And 错误提示应该显示5秒钟
    And 按钮应该恢复到可点击状态
    And 用户应该能够重试操作

  Scenario: API响应超时处理
    Given "开始配置"按钮是可用状态
    And API服务响应缓慢
    When 我点击"开始配置"按钮
    And API调用超过10秒
    Then 系统应该终止请求
    And 应该显示"请求超时"提示
    And 应该提供重试选项
    And 按钮功能应该保持可用
```

### Feature: 功能卡片导航
**对应需求**: REQ-005

```gherkin
Feature: 功能卡片导航功能
  作为一个用户
  我希望点击功能介绍卡片能导航到对应的功能页面
  以便我能了解和使用不同类型的名片功能

  Background:
    Given 我在商家名片首页
    And 页面显示了三个功能卡片

  Scenario Outline: 点击功能卡片导航
    Given 我能看到"<卡片名称>"功能卡片
    And 卡片显示正确的标题和描述
    And 卡片包含两张并排的示例图片
    When 我点击"<卡片名称>"功能卡片
    Then 系统应该导航到"<目标页面>"
    And 页面URL应该变为"<目标URL>"
    And 导航应该在500毫秒内完成

    Examples:
      | 卡片名称                    | 目标页面         | 目标URL                    |
      | 交易连接名片                | 交易连接名片页面  | /card-mna/transaction-card |
      | 名片连接商家的服务与优惠     | 商品服务名片页面  | /card-mna/service-card     |
      | 用户标记喜欢，稳定找回商家   | 宣传推广名片页面  | /card-mna/promotion-card   |

  Scenario: 功能卡片悬停效果验收
    Given 我能看到功能卡片
    When 我将鼠标悬停在任意功能卡片上
    Then 卡片应该显示悬停效果
    And 卡片应该有阴影变化(0 4px 12px rgba(0, 0, 0, 0.1))
    And 卡片应该有轻微的向上移动动画(translateY(-2px))
    And 卡片边框颜色应该变为品牌色(#0052d9)
    And 动画持续时间应该是0.3秒
    When 我移开鼠标
    Then 卡片应该恢复到原始状态

  Scenario: 功能卡片内容验证
    Given 我在商家名片首页
    Then 我应该能看到"交易连接名片"卡片
    And 卡片应该包含描述文字"添加交易场景，用户支付成功后，点击微信支付分公众号用户下发的交付凭证，将跳转商家名片"
    And 卡片应该包含两张并排的示例图片
    And 图片应该正确加载(01.png, 02.png)
    And 我应该能看到"名片连接商家的服务与优惠"卡片
    And 卡片应该包含描述文字"名片连接商家的小程序、视频号和客服，提供自定义服务配置，展示用户名商家的优惠券和会员权益"
    And 卡片应该包含两张并排的示例图片(03.png, 04.png)
    And 我应该能看到"用户标记喜欢，稳定找回商家"卡片
    And 卡片应该包含描述文字"用户标记了喜欢商家，商家名片将出现在「微信支付公众号-我的商家-喜欢的商家」列表，便于用户快速找回喜欢商家"
    And 卡片应该包含两张并排的示例图片(05.png, 06.png)
    And 所有卡片的布局应该保持一致
    And 卡片宽度应该为32%(桌面端)

  Scenario: 卡片导航失败处理
    Given 我在商家名片首页
    And 路由服务暂时不可用
    When 我点击任意功能卡片
    Then 系统应该捕获路由错误
    And 应该在控制台记录错误信息
    And 页面应该保持稳定，不应该崩溃
    And 用户应该能够重试操作
```

### Feature: 状态管理功能
**对应需求**: REQ-006, REQ-007

```gherkin
Feature: 页面状态管理
  作为系统
  我需要正确维护和同步页面状态
  以便确保用户界面与后端数据的一致性

  Background:
    Given 系统已正常初始化
    And 状态管理器已准备就绪

  Scenario: 初始状态设置验收
    Given 用户首次访问页面
    When 系统初始化页面状态
    Then 应该设置以下状态：
      | 状态字段                    | 初始值 | 类型   | 描述           |
      | cardState.useable          | true   | 布尔值 | 控制按钮可用性  |
      | tutorialState.processId    | ""     | 字符串 | 新手引导流程ID |
      | cardInfo.isConfigured      | false  | 布尔值 | 是否已配置     |
      | cardInfo.cardType          | ""     | 字符串 | 卡片类型       |
      | cardInfo.lastUpdated       | ""     | 字符串 | 最后更新时间   |
    And 所有状态应该有正确的类型定义
    And 状态应该通过TypeScript类型检查

  Scenario: 状态同步验收
    Given 页面状态已初始化
    When 用户执行状态变更操作(点击开始配置)
    Then 前端状态应该立即更新
    And loading状态应该设置为true
    And 状态变更应该通过API同步到后端
    And 后端确认后前端状态应该保持一致
    And processId应该被正确保存
    When API同步失败
    Then 应该回滚前端状态
    And 应该显示错误提示
    And 用户应该能够重试

  Scenario: 状态持久化验收
    Given 用户在页面上进行了操作
    And 状态已发生变更(processId已保存)
    When 用户刷新页面
    Then 系统应该从后端重新获取最新状态
    And 页面应该显示正确的状态信息
    And 如果有未完成流程，应该重定向到配置页面
    And 状态应该与刷新前保持一致

  Scenario: 状态错误处理验收
    Given 页面正在运行
    When 状态管理出现异常(如网络错误)
    Then 系统应该使用默认状态
    And 页面应该保持基本功能可用
    And 应该记录错误信息用于排查
    And 用户应该能够手动重试获取状态
```

### Feature: API交互功能
**对应需求**: REQ-008, REQ-009

```gherkin
Feature: API交互功能
  作为系统
  我需要正确调用后端API并处理响应
  以便获取和更新用户数据

  Background:
    Given 后端API服务正常运行
    And 用户已通过身份验证

  Scenario: 获取卡片信息成功验收
    Given 用户有有效的卡片配置
    When 系统调用 "/api/merchant/card/info" 接口
    Then API应该返回状态码200
    And 响应应该包含卡片基础信息
    And 响应格式应该符合接口规范
    And 系统应该更新本地cardInfo状态
    And 页面应该显示获取到的信息
    And API调用应该在3秒内完成

  Scenario: 获取卡片信息失败验收
    Given API服务暂时不可用
    When 系统调用 "/api/merchant/card/info" 接口
    Then API应该返回错误状态码(4xx或5xx)
    And 系统应该显示友好的错误提示
    And 错误提示应该明确指出问题原因
    And 系统应该提供重试选项
    And 页面应该使用默认状态显示
    And 基本功能应该保持可用

  Scenario: 创建新手引导流程成功验收
    Given 用户点击了"开始配置"按钮
    And 用户状态为新用户
    When 系统调用 "CreateTutorialPublishProcess" API
    Then API应该返回成功响应(code: 0)
    And 响应应该包含新的processId
    And processId应该是有效的字符串
    And 系统应该保存processId到tutorialState.processId
    And 系统应该跳转到配置页面
    And 整个流程应该在5秒内完成

  Scenario: 创建新手引导流程失败验收
    Given 用户点击了"开始配置"按钮
    And API服务返回错误
    When 系统调用 "CreateTutorialPublishProcess" API
    Then 系统应该捕获API错误
    And 应该显示"创建引导流程失败"错误提示
    And 错误提示应该包含具体错误信息
    And 按钮应该恢复到可点击状态
    And 用户应该能够重试操作
    And 不应该进行页面跳转

  Scenario: API超时处理验收
    Given API服务响应缓慢
    When 系统调用任何API接口
    And 调用时间超过10秒
    Then 系统应该终止请求
    And 应该显示"请求超时，请重试"提示
    And 应该提供重试按钮
    And 页面功能应该保持可用
    And 不应该影响其他功能的使用

  Scenario: 数据格式异常处理验收
    Given API返回了格式不正确的数据
    When 系统尝试解析响应数据
    Then 系统应该使用默认值处理
    And 页面应该不会崩溃
    And 应该记录异常信息用于排查
    And 用户应该能继续使用基本功能
    And 应该显示数据加载异常提示
```

### Feature: 响应式布局功能
**对应需求**: REQ-012

```gherkin
Feature: 响应式布局适配
  作为用户
  我希望在不同设备上都能正常使用页面
  以便在任何场景下都能访问功能

  Scenario Outline: 不同设备尺寸适配验收
    Given 我使用"<设备类型>"访问页面
    When 页面在"<屏幕尺寸>"下显示
    Then 功能卡片应该按"<布局方式>"排列
    And 卡片宽度应该是"<卡片宽度>"
    And 所有内容应该在屏幕内正常显示
    And 交互元素应该易于点击
    And 文字应该清晰可读
    And 图片应该正确缩放

    Examples:
      | 设备类型 | 屏幕尺寸    | 布局方式 | 卡片宽度 |
      | 桌面端   | ≥1200px    | 3列并排  | 32%     |
      | 平板端   | 768-1199px | 2列并排  | 48%     |
      | 移动端   | <768px     | 1列堆叠  | 100%    |

  Scenario: 移动端交互优化验收
    Given 我使用移动设备访问页面
    When 页面在移动端显示
    Then 标题和按钮应该垂直排列
    And 标题区域和操作区域应该有16px间距
    And 按钮应该有足够的点击区域(最小44px高度)
    And 卡片间距应该适合触摸操作(16px间距)
    And 文字大小应该易于阅读(最小16px)
    And 图片应该保持清晰度
    And 页面应该支持触摸滚动

  Scenario: 响应式断点切换验收
    Given 我在桌面端浏览页面
    And 页面显示3列卡片布局
    When 我调整浏览器窗口到平板尺寸
    Then 页面应该平滑切换到2列布局
    And 卡片应该重新排列
    And 不应该有布局闪烁
    When 我继续调整到移动端尺寸
    Then 页面应该切换到1列布局
    And 标题和按钮应该垂直排列
    And 所有功能应该保持正常
```

### Feature: 浏览器兼容性
**对应需求**: REQ-014

```gherkin
Feature: 浏览器兼容性验收
  作为用户
  我希望在不同浏览器中都能正常使用页面
  以便不受浏览器限制

  Scenario Outline: 主流浏览器兼容性验收
    Given 我使用"<浏览器>"访问页面
    When 页面在该浏览器中加载
    Then 所有功能应该正常工作
    And 样式显示应该正确
    And 交互行为应该一致
    And 性能应该满足要求(3秒内加载)
    And JavaScript功能应该正常执行
    And CSS样式应该正确渲染
    And 字体应该正确显示

    Examples:
      | 浏览器      |
      | Chrome 90+  |
      | Firefox 88+ |
      | Safari 14+  |
      | Edge 90+    |

  Scenario: 浏览器特性检测验收
    Given 我使用较旧版本的浏览器
    When 页面检测到不支持的特性
    Then 应该提供降级方案
    And 基本功能应该保持可用
    And 应该显示浏览器升级提示(如需要)
    And 不应该出现JavaScript错误
```

### Feature: 无障碍访问
**对应需求**: REQ-013

```gherkin
Feature: 无障碍访问支持
  作为使用辅助技术的用户
  我希望能够正常访问和使用页面功能
  以便享受平等的用户体验

  Scenario: 键盘导航支持验收
    Given 我使用键盘进行导航
    When 我按Tab键在页面元素间切换
    Then 焦点应该按逻辑顺序移动
    And 焦点顺序应该是：开始配置按钮 → 交易连接名片 → 服务优惠名片 → 用户标记名片
    And 当前焦点元素应该有明显的视觉指示
    And 焦点指示器应该有足够的对比度
    When 我在按钮上按Enter键
    Then 应该触发按钮点击事件
    When 我在卡片上按Enter或Space键
    Then 应该触发卡片点击事件
    And 我应该能访问所有交互元素

  Scenario: 屏幕阅读器支持验收
    Given 我使用屏幕阅读器访问页面
    When 屏幕阅读器读取页面内容
    Then 页面应该有正确的语义结构
    And 主要内容应该在main标签内
    And 标题应该使用正确的h1-h6层级
    And 所有图片应该有适当的alt文本
    And 装饰性图片应该有空alt=""
    And 按钮应该有描述性的aria-label
    And 卡片应该有role="button"属性
    And 状态变化应该通过aria-live通知
    And 加载状态应该被正确通知

  Scenario: 高对比度和缩放支持验收
    Given 我需要高对比度显示
    When 我启用系统高对比度模式
    Then 页面应该保持良好的可读性
    And 所有文本应该有足够的对比度(至少4.5:1)
    And 界面元素应该清晰可辨
    And 焦点指示器应该在高对比度下可见
    When 我将页面缩放到200%
    Then 页面应该保持功能完整
    And 文本应该清晰可读
    And 布局应该保持合理
    And 交互元素应该保持可用
```

### Feature: 安全防护
**对应需求**: REQ-022, REQ-023

```gherkin
Feature: 安全防护措施
  作为系统
  我需要保护用户数据和系统安全
  以便防范各种安全威胁

  Scenario: 敏感信息保护验收
    Given 系统处理用户敏感信息
    When 信息在前端传输和存储
    Then 敏感信息不应该明文存储在浏览器中
    And 不应该在localStorage中存储敏感数据
    And 不应该在sessionStorage中存储敏感数据
    And 应该使用HTTPS协议传输
    And 应该遵循最小权限原则
    And 不应该在控制台中暴露敏感数据
    And 不应该在网络面板中暴露敏感数据
    And API调用应该包含适当的认证头

  Scenario: 输入验证和清理验收
    Given 系统接收外部数据输入
    When 数据被处理和显示
    Then 应该对所有输入进行验证
    And 应该清理潜在的恶意内容
    And 应该防止XSS攻击
    And 应该正确编码输出内容
    And HTML内容应该被转义
    And 用户输入应该被过滤
    And 不应该执行未经验证的脚本
```

### Feature: 错误处理和异常情况
**对应需求**: REQ-019, REQ-020, REQ-021

```gherkin
Feature: 异常情况处理
  作为系统
  我需要优雅地处理各种异常情况
  以便为用户提供稳定的服务体验

  Scenario: 网络连接异常验收
    Given 用户网络连接不稳定
    When 系统尝试调用API
    And API调用失败
    Then 系统应该显示网络异常提示
    And 提示信息应该是"网络连接异常，请检查网络后重试"
    And 提示应该清晰说明问题
    And 系统应该提供重试按钮
    And 重试按钮应该是可点击状态
    When 用户点击重试后
    Then 应该重新尝试API调用
    And 页面基本功能应该保持可用

  Scenario: 权限不足处理验收
    Given 用户权限不足
    When 用户尝试访问受限功能
    Then 系统应该显示权限不足提示
    And 提示应该是"暂无访问权限，请联系管理员开通权限"
    And 应该提供联系管理员的方式
    And 应该引导用户到可用功能
    And 不应该显示敏感的错误信息
    And 页面应该保持稳定

  Scenario: 系统维护状态验收
    Given 系统正在维护
    When 用户访问页面
    Then 应该显示维护提示页面
    And 提示应该包含预计恢复时间
    And 应该提供联系方式
    And 页面样式应该保持一致
    And 不应该显示功能性内容
```

## 性能验收标准

### Feature: 页面性能要求
**对应需求**: REQ-010, REQ-011

```gherkin
Feature: 页面性能验收
  作为用户
  我希望页面能快速加载和响应
  以便获得良好的使用体验

  Scenario: 页面加载性能验收
    Given 用户网络连接正常(4G网络)
    When 用户访问商家名片首页
    Then 页面应该在3秒内完成初始化
    And 首屏内容应该在1.5秒内显示
    And 初始化包括API调用、状态更新和界面渲染
    And 页面应该显示完整的内容
    And 所有交互元素应该可用
    And 图片应该在5秒内全部加载完成

  Scenario: 交互响应性能验收
    Given 页面已完全加载
    When 用户点击任何交互元素
    Then 系统应该在500毫秒内给出视觉反馈
    And 反馈包括按钮状态变化或加载指示器
    And 用户应该能感知到操作已被接收
    And 页面应该保持响应，不应该卡顿
    And CPU使用率应该保持在合理范围内

  Scenario: 图片加载性能验收
    Given 页面包含功能卡片图片
    When 页面开始加载
    Then 图片应该使用懒加载策略
    And 图片应该有适当的占位符
    And 图片加载失败时应该显示默认占位符
    And 图片加载完成应该有平滑的显示动画
    And 图片文件大小应该经过优化
    And 应该支持WebP格式(如浏览器支持)

  Scenario: 内存使用验收
    Given 用户长时间使用页面
    When 用户进行各种操作
    Then 内存使用量应该保持稳定
    And 不应该有明显的内存泄漏
    And 页面刷新后内存应该被正确释放
    And 内存使用量应该在合理范围内(< 50MB)
```

## 集成验收标准

### Feature: 完整用户流程
**整体功能流程验收**

```gherkin
Feature: 完整用户流程验收
  作为新用户
  我希望能够顺利完成从首次访问到开始配置的完整流程
  以便成功开始使用商家名片功能

  Scenario: 新用户完整配置流程验收
    Given 我是一个新的商家用户
    And 我已经登录系统
    And 我没有任何配置记录
    When 我首次访问商家名片首页
    Then 我应该看到功能介绍页面
    And 我应该看到页面标题"商家名片"
    And 我应该看到页面描述"助力商家服务与经营，帮助商家实现和用户的深度连接"
    And 我应该看到三个功能卡片的详细介绍
    And "开始配置"按钮应该是可用的
    When 我点击"开始配置"按钮
    Then 系统应该显示加载状态
    And 系统应该创建新手引导流程
    And 我应该被重定向到配置详情页面"/home/<USER>/card-detail"
    And 我的配置状态应该被正确保存
    And processId应该被保存到系统状态中
    When 我再次访问首页
    Then 我应该被自动重定向到配置页面继续配置
    And 我应该能看到之前的配置进度

  Scenario: 用户状态变化流程验收
    Given 我有一个进行中的配置流程
    And 我的状态为"草稿"(DRAFT)
    When 我完成所有配置步骤
    And 配置被成功发布
    Then 我的状态应该更新为"已完成"(PUBLISHED)
    When 我再次访问商家名片首页
    Then 我应该被自动重定向到仪表盘页面"/home/<USER>/dashboard"
    And 我应该能看到我的名片数据和统计信息
    And 我不应该再看到首页的介绍内容

  Scenario: 跨页面状态一致性验收
    Given 我在商家名片首页
    And 我的状态已经初始化
    When 我导航到其他页面
    And 然后返回到首页
    Then 我的状态应该保持一致
    And 页面应该显示正确的内容
    And 不应该重复执行初始化逻辑
    And 用户体验应该是连贯的
```

## 验收执行指南

### 验收环境准备
1. **测试数据准备**
   - 新用户账号（无任何配置记录）
   - 配置中用户账号（有未完成的新手引导流程）
   - 已完成用户账号（已发布配置）
   - 权限受限用户账号

2. **测试工具配置**
   - 浏览器开发者工具
   - 网络模拟工具（模拟慢网络、断网、超时）
   - 屏幕阅读器测试工具（NVDA、JAWS、VoiceOver）
   - 响应式设计测试工具
   - 性能监控工具

3. **API Mock配置**
   - 正常响应场景
   - 错误响应场景（4xx、5xx）
   - 超时场景（>10秒）
   - 数据格式异常场景
   - 权限不足场景

### 验收执行优先级
1. **P0 - 核心功能验收**: 页面加载、状态检查、基本导航、开始配置
2. **P1 - 主要功能验收**: API交互、错误处理、路由重定向
3. **P2 - 用户体验验收**: 响应式布局、性能优化、无障碍访问
4. **P3 - 边缘情况验收**: 异常处理、安全防护、浏览器兼容性

### 验收通过标准
- **P0验收**: 100%通过，无阻塞性问题
- **P1验收**: 100%通过，无严重功能问题
- **P2验收**: ≥95%通过，用户体验良好
- **P3验收**: ≥90%通过，边缘情况处理合理

### 验收失败处理
1. **阻塞性问题**: 立即修复，重新验收
2. **严重问题**: 优先修复，24小时内重新验收
3. **一般问题**: 计划修复，下个版本验收
4. **建议性问题**: 记录备案，后续优化

## 验收报告模板

### 验收结果记录
```
验收日期: YYYY-MM-DD
验收人员: [验收人员姓名]
验收版本: [代码版本号]

验收结果统计:
- P0验收: X/X 通过 (100%)
- P1验收: X/X 通过 (100%)
- P2验收: X/X 通过 (XX%)
- P3验收: X/X 通过 (XX%)

总体评估: [通过/不通过]
```

### 问题记录格式
```
问题ID: [唯一标识]
问题标题: [简短描述]
问题等级: [P0/P1/P2/P3]
对应场景: [Gherkin场景名称]
问题描述: [详细描述]
重现步骤: [操作步骤]
期望结果: [应该的行为]
实际结果: [实际的行为]
修复建议: [修复方向]
```

## 自动化验收

### 自动化验收脚本
```bash
#!/bin/bash
# 自动化验收执行脚本

echo "开始执行Card-Home页面验收测试..."

# 执行单元测试
npm run test:unit -- --testPathPattern=card-home

# 执行集成测试
npm run test:integration -- --testPathPattern=card-home

# 执行E2E测试
npm run test:e2e -- --spec="**/card-home.e2e.spec.ts"

# 执行性能测试
npm run test:performance -- --page=card-home

# 执行无障碍测试
npm run test:a11y -- --page=card-home

# 生成验收报告
npm run generate:acceptance-report

echo "验收测试执行完成，请查看报告。"
```

### 持续集成验收
```yaml
name: Card-Home Acceptance Tests
on:
  pull_request:
    paths:
      - 'src/pages/card-mna/card-home-vue/**'
      - 'specs/modules/card-home/**'

jobs:
  acceptance:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:acceptance:card-home
      - uses: actions/upload-artifact@v3
        with:
          name: acceptance-report
          path: reports/acceptance/
```

---

**文档版本**: 1.0  
**创建时间**: 2024-01-02  
**基于需求**: specs/modules/card-home/requirements.md v1.0  
**维护人**: 产品验收团队  
**更新频率**: 随需求变更同步更新

## 附录

### A. Gherkin语法说明
- **Feature**: 功能特性描述
- **Scenario**: 具体验收场景
- **Given**: 前置条件
- **When**: 触发动作
- **Then**: 期望结果
- **And**: 连接词，表示并且
- **But**: 连接词，表示但是

### B. 验收与测试的区别
- **验收标准**: 从业务角度定义功能是否满足需求
- **测试用例**: 从技术角度验证代码实现是否正确
- **验收更关注**: 用户体验、业务价值、需求满足度
- **测试更关注**: 代码覆盖率、边界条件、异常处理

### C. 相关文档链接
- [需求规格说明](./requirements.md)
- [业务规则文档](./business-rules.md)
- [技术测试文档](./test.md)
- [系统设计文档](./design.md)
