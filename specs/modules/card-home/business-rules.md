# Card-Home 页面业务规则文档

## 页面概述

card-home 是商家名片管理系统的首页，主要用于展示商家名片功能介绍和引导用户开始配置。

## 核心业务规则

### 1. 页面状态管理

#### 1.1 卡片状态 (cardState)
```typescript
interface CardState {
  useable: boolean; // 是否可用，控制"开始配置"按钮的启用状态
}
```

#### 1.2 教程状态 (tutorialState)
```typescript
interface TutorialState {
  processId: string; // 新手引导流程ID
}
```

#### 1.3 卡片信息状态 (cardInfo)
```typescript
interface CardInfo {
  isConfigured: boolean; // 是否已配置
  cardType: string; // 卡片类型
  lastUpdated: string; // 最后更新时间
}
```

### 2. 页面交互规则

#### 2.1 开始配置按钮逻辑
- **触发条件**: 用户点击"开始配置"按钮
- **前置检查**: 按钮状态受 `cardState.useable` 控制
  - `useable = true`: 按钮禁用
  - `useable = false`: 按钮可用
- **执行流程**:
  1. 调用 `CreateNewTutorial` 交互
  2. 创建新手引导流程 (调用API)
  3. 保存 processId 到状态
  4. 跳转到 `/home/<USER>/card-detail` 页面

#### 2.2 功能卡片点击跳转
- **交易连接名片**: 跳转到 `/card-mna/transaction-card`
- **商品服务名片**: 跳转到 `/card-mna/service-card`  
- **宣传推广名片**: 跳转到 `/card-mna/promotion-card`

### 3. API 接口规则

#### 3.1 获取卡片信息
```
GET /api/merchant/card/info
用途: 获取商户卡片基础信息
```

#### 3.2 创建卡片
```
POST /api/merchant/card/create
用途: 创建新的商户卡片
```

#### 3.3 创建新手引导流程
```
API: CreateTutorialPublishProcess
用途: 创建新手引导发布流程
返回: { processId: string }
```

### 4. 业务流程规则

#### 4.1 新用户首次访问流程
1. 页面加载时检查用户状态
2. 如果是新用户，显示功能介绍卡片
3. 用户点击"开始配置"创建引导流程
4. 跳转到详情配置页面

#### 4.2 状态判断逻辑 (基于tutorial.ts)
```typescript
// 发布状态计算规则
export const calcPublishStatus = (data: any): boolean => {
  // 检查API调用是否有错误
  if (hasAnyError(responses)) return true;
  
  // 检查所有条件是否满足
  if (areAllConditionsMet(
    latestCardInfoPublishProcess,
    latestServicePublishProcess, 
    brandMemberAccess,
    shakeCouponAccess
  )) {
    return false; // 可以发布
  }
  
  return true; // 禁用发布
};
```

#### 4.3 进程状态枚举
```typescript
enum ProcessState {
  INIT = 0,        // 初始状态
  DRAFT = 1,       // 草稿
  AUDITING = 2,    // 待审核  
  AUDIT_PASS = 3,  // 审核通过
  REJECTED = 4,    // 审核驳回
  CANCELED = 5,    // 已作废
  APPROVED = 6,    // 待发布
  PUBLISHING = 7,  // 发布中
  PUBLISHED = 8,   // 已完成
}
```

### 5. 页面布局规则

#### 5.1 样式类定义
- `formtitle`: 表单标题样式 (白色背景，16px内边距)
- `cardSection`: 卡片区域样式 (白色背景，圆角8px，24px内边距)
- `card-container`: 卡片容器 (flex布局，space-between)
- `feature-card`: 功能卡片样式 (32%宽度，边框，悬停效果)

#### 5.2 响应式布局
- 功能卡片采用flex布局，每行3个卡片
- 卡片宽度为32%，支持换行显示
- 图片采用50%-50%左右分布

### 6. 用户体验规则

#### 6.1 视觉反馈
- 功能卡片支持悬停效果 (阴影变化)
- 按钮状态变化 (禁用/启用)
- 过渡动画 (0.3s transition)

#### 6.2 信息展示
- 页面标题: "微信支付 品牌经营"
- 主标题: "商家名片"  
- 副标题: "助力商家服务与经营，帮助商家实现和用户的深度连接"

### 7. 错误处理规则

#### 7.1 API错误处理
- 任何API调用返回错误时，禁用相关功能
- 错误状态下保持页面基本展示功能

#### 7.2 状态异常处理
- processId为空时的默认处理
- 网络异常时的降级展示


## 路由守卫逻辑

### 智能路由重定向规则
基于 `router/index.ts` 中的 `checkRouter` 函数：

```typescript
const checkRouter = async (): Promise<{
  code: number;
  message: string; 
  path: string;
}> => {
  // 1. 检查新手引导发布单状态
  const result = await request.get('/get-latest-tutorial-publish-process');
  
  if (result?.code === 0) {
    // 已完成新手引导 -> 跳转到仪表盘
    if (result.data?.process.state === ProcessState.PUBLISHED) {
      return { code: 3000, path: '/home/<USER>/dashboard' };
    }
    // 有未完成的新手单 -> 跳转到详情页继续配置
    return { code: 2000, path: '/home/<USER>/card-detail' };
  }
  
  // 2. 检查基础信息和服务配置状态
  const [cardInfo, cardService] = await Promise.all([
    request.get('/get-card-info'),
    request.get('/get-service-config'),
  ]);
  
  // 都已配置 -> 跳转到仪表盘
  if (cardInfo.code === 0 && cardService.code === 0) {
    return { code: 3000, path: '/home/<USER>/dashboard' };
  }
  
  // 新用户 -> 停留在首页
  return { code: 1000, path: '/home/<USER>/card-home' };
};
```

### 用户状态分类
1. **新用户** (code: 1000) - 显示card-home介绍页面
2. **配置中用户** (code: 2000) - 重定向到card-detail继续配置
3. **已完成用户** (code: 3000) - 重定向到dashboard仪表盘

## 侧边栏导航规则

### 导航结构 (基于home-model.ts)
```typescript
sideBarNavItemsRef = [
  {
    name: '商家名片',
    children: [
      {
        name: '名片管理',
        href: '/home/<USER>/card-home',
        children: [
          { name: '发布名片', href: '/home/<USER>/card-publish' },
          { name: '配置名片服务', href: '/home/<USER>/common-publish' },
          { name: '配置基础信息', href: '/home/<USER>/card-config' },
          { name: '基础信息', href: '/home/<USER>/card-view' },
          { name: '品牌会员或品牌优惠', href: '/home/<USER>/brand-vip' },
          { name: '名片配置', href: '/home/<USER>/card-detail' },
          { name: '商家名片', href: '/home/<USER>/dashboard' },
        ],
      },
      { name: '交易连接名片', href: '/home/<USER>/pay-link-card' },
    ],
  }
];
```

### 导航激活状态更新
```typescript
updateSideBarActiveItem(path: string) {
  // 根据当前路径更新侧边栏激活状态
  this.sideBarNavItemsRef.value = this.sideBarNavItemsRef.value.map((menu) => ({
    ...menu,
    children: menu.children.map((submenu) => ({
      ...submenu,
      active: submenu.href.replace('#', '') === path,
    })),
  }));
}
```

## 依赖关系

- 依赖 `tutorial.ts` 中的状态计算逻辑
- 依赖后端API接口 (`/get-latest-tutorial-publish-process`, `/get-card-info`, `/get-service-config`)
- 依赖路由守卫机制 (`checkRouter` 函数)
- 依赖TDesign UI组件库
- 依赖home-model.ts中的导航状态管理
- 依赖DSL-Page框架的schema渲染机制
